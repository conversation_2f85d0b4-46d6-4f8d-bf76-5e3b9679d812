{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "typeRoots": ["./src/types", "./node_modules/@types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "src/tests"]}