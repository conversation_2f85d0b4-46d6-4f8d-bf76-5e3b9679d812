{"name": "@tokai/backend", "version": "0.3.0", "description": "Tokai Backend API - Self-hosted wallet infrastructure platform", "type": "commonjs", "scripts": {"dev": "ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "install-sms": "npm install @aws-sdk/client-sns"}, "dependencies": {"@types/cookie-parser": "^1.4.9", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "bcryptjs": "^2.4.3", "better-sqlite3": "^9.2.2", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.10.1", "otp-generator": "^4.0.1", "qrcode": "^1.5.3", "speakeasy": "^2.0.0", "supertest": "^7.1.4", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.8", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "optionalDependencies": {"@aws-sdk/client-sns": "^3.470.0"}, "keywords": ["tokai", "wallet", "authentication", "blockchain", "self-hosted", "web3"], "author": "Tokai", "license": "MIT", "engines": {"node": ">=18.0.0"}}