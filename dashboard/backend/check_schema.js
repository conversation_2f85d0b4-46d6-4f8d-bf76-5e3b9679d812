const { Database } = require("bun:sqlite");

const db = new Database("wallet_service.db");

// Check table schema
try {
  const tableInfo = db.prepare("PRAGMA table_info(apps)").all();
  console.log("Apps table schema:");
  console.log(tableInfo);
  
  // Check if api_key_preview column exists
  const hasApiKeyPreview = tableInfo.some(col => col.name === 'api_key_preview');
  console.log("Has api_key_preview column:", hasApiKeyPreview);
  
} catch (error) {
  console.error("Error:", error);
} finally {
  db.close();
} 