import Database from "better-sqlite3";
import { logger } from "../utils/logger";
import { runMigrations } from "./migrations";

export class DatabaseService {
  private static instance: DatabaseService;
  private db: any | null = null;

  private constructor() {}

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  async initialize(): Promise<void> {
    try {
      // Initialize SQLite database
      this.db = new Database("wallet_service.db");

      // Enable WAL mode for better concurrency
      this.db.pragma("journal_mode = WAL");

      this.createTables();

      await runMigrations(this.db);

      this.createIndexes();

      // Insert default configurations
      await this.insertDefaultConfigs();

      logger.info("Database initialized successfully");
    } catch (error) {
      logger.error("Failed to initialize database:", error);
      throw error;
    }
  }

  private createTables(): void {
    // Users table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        full_name TEXT,
        email_verified BOOLEAN DEFAULT 0,
        email_verified_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Apps table - Each user can have multiple apps
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS apps (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        api_key_hash TEXT UNIQUE NOT NULL,
        api_key_preview TEXT NOT NULL,
        google_client_id TEXT,
        google_client_secret TEXT,
        domain TEXT,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Wallets table - Now belongs to apps
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS wallets (
        id TEXT PRIMARY KEY,
        app_id TEXT,
        user_id TEXT,
        end_user_id TEXT,
        network TEXT NOT NULL,
        wallet_type TEXT DEFAULT 'embedded',
        wallet_address TEXT NOT NULL,
        encrypted_private_key TEXT NOT NULL,
        encrypted_seed_phrase TEXT NOT NULL,
        public_key TEXT,
        derivation_path TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (app_id) REFERENCES apps (id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Auto-created wallets tracking table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS auto_created_wallets (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        wallet_id TEXT,
        network TEXT NOT NULL,
        wallet_type TEXT DEFAULT 'embedded',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Sessions table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        access_token TEXT UNIQUE NOT NULL,
        refresh_token TEXT UNIQUE NOT NULL,
        access_token_expires_at DATETIME NOT NULL,
        refresh_token_expires_at DATETIME NOT NULL,
        device_info TEXT,
        ip_address TEXT,
        user_agent TEXT,
        is_active BOOLEAN DEFAULT 1,
        last_used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        token_version INTEGER DEFAULT 1,
        jti TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        setting_key TEXT NOT NULL,
        setting_value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        UNIQUE(user_id, setting_key)
      )
    `);

    // API Keys table - Secondary keys for apps
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS api_keys (
        id TEXT PRIMARY KEY,
        app_id TEXT NOT NULL,
        name TEXT NOT NULL,
        key_hash TEXT UNIQUE NOT NULL,
        permissions TEXT DEFAULT 'read',
        is_active BOOLEAN DEFAULT 1,
        last_used DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (app_id) REFERENCES apps (id) ON DELETE CASCADE
      )
    `);

    // App-level settings
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS app_settings (
        id TEXT PRIMARY KEY,
        app_id TEXT NOT NULL,
        google_client_id TEXT,
        google_client_secret TEXT,
        default_wallet_network TEXT DEFAULT 'ethereum',
        auto_create_wallets BOOLEAN DEFAULT TRUE,
        email_otp_enabled BOOLEAN DEFAULT TRUE,
        google_auth_enabled BOOLEAN DEFAULT TRUE,
        auth_type TEXT DEFAULT 'password',
        email_verification_required BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (app_id) REFERENCES apps (id) ON DELETE CASCADE
      )
    `);

    // OAuth states table for CSRF protection
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS oauth_states (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        state TEXT UNIQUE NOT NULL,
        provider TEXT NOT NULL,
        redirect_url TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NOT NULL
      )
    `);

    // Social accounts table for linked social logins
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS social_accounts (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        provider TEXT NOT NULL,
        provider_id TEXT NOT NULL,
        email TEXT,
        name TEXT,
        avatar_url TEXT,
        verified BOOLEAN DEFAULT FALSE,
        connected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        UNIQUE(provider, provider_id)
      )
    `);

    // MFA setup table for temporary TOTP secrets
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_mfa_setup (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        totp_secret TEXT,
        backup_codes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        verified BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // MFA configuration table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_mfa (
        user_id TEXT PRIMARY KEY,
        totp_secret TEXT,
        totp_enabled BOOLEAN DEFAULT FALSE,
        sms_phone_number TEXT,
        sms_enabled BOOLEAN DEFAULT FALSE,
        backup_codes TEXT,
        used_backup_codes TEXT DEFAULT '[]',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // SMS verification codes table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS sms_verification_codes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        phone_number TEXT NOT NULL,
        code TEXT NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        used BOOLEAN DEFAULT FALSE,
        verified_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // User activity tracking table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_activity (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        activity TEXT NOT NULL,
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Session keys table for delegated permissions
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS session_keys (
        id TEXT PRIMARY KEY,
        session_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        app_id TEXT,
        key_name TEXT NOT NULL,
        key_hash TEXT UNIQUE NOT NULL,
        permissions TEXT NOT NULL,
        scope TEXT,
        expires_at DATETIME NOT NULL,
        last_used_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1,
        revoked_at DATETIME,
        revoked_by TEXT,
        metadata TEXT,
        FOREIGN KEY (session_id) REFERENCES sessions (id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (app_id) REFERENCES apps (id) ON DELETE CASCADE
      )
    `);

    // Permission definitions table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS permission_definitions (
        id TEXT PRIMARY KEY,
        name TEXT UNIQUE NOT NULL,
        description TEXT,
        category TEXT DEFAULT 'general',
        is_system BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Email verification tokens table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS email_verification_tokens (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        email TEXT NOT NULL,
        token TEXT UNIQUE NOT NULL,
        expires_at DATETIME NOT NULL,
        used BOOLEAN DEFAULT 0,
        verified_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Email OTPs table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS email_otps (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        email TEXT NOT NULL,
        otp TEXT NOT NULL,
        expires_at DATETIME NOT NULL,
        used BOOLEAN DEFAULT 0,
        verified_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Session key usage logs for audit trail
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS session_key_usage (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_key_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        action TEXT NOT NULL,
        resource TEXT,
        ip_address TEXT,
        user_agent TEXT,
        success BOOLEAN DEFAULT TRUE,
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (session_key_id) REFERENCES session_keys (id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Schema migrations registry
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS schema_migrations (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('Database tables created successfully');
  }

  private createIndexes(): void {
    if (!this.db) throw new Error("Database not initialized");

    const indexStatements = [
      `CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)`,
      `CREATE INDEX IF NOT EXISTS idx_apps_user_id ON apps(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_apps_api_key_hash ON apps(api_key_hash)`,
      `CREATE INDEX IF NOT EXISTS idx_wallets_app_id ON wallets(app_id)`,
      `CREATE INDEX IF NOT EXISTS idx_wallets_user_id_is_active ON wallets(user_id, is_active)`,
      `CREATE INDEX IF NOT EXISTS idx_wallets_end_user_id ON wallets(end_user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_wallets_network ON wallets(network)`,
      `CREATE INDEX IF NOT EXISTS idx_auto_created_wallets_user_id ON auto_created_wallets(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_sessions_user_id_is_active ON sessions(user_id, is_active)`,
      `CREATE INDEX IF NOT EXISTS idx_sessions_access_token ON sessions(access_token)`,
      `CREATE INDEX IF NOT EXISTS idx_sessions_refresh_token ON sessions(refresh_token)`,
      `CREATE INDEX IF NOT EXISTS idx_sessions_refresh_token_expires_at ON sessions(refresh_token_expires_at)`,
      `CREATE INDEX IF NOT EXISTS idx_settings_user_id ON settings(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_api_keys_app_id ON api_keys(app_id)`,
      `CREATE INDEX IF NOT EXISTS idx_api_keys_key_hash ON api_keys(key_hash)`,
      `CREATE INDEX IF NOT EXISTS idx_app_settings_app_id ON app_settings(app_id)`,
      `CREATE INDEX IF NOT EXISTS idx_oauth_states_state ON oauth_states(state)`,
      `CREATE INDEX IF NOT EXISTS idx_oauth_states_expires_at ON oauth_states(expires_at)`,
      `CREATE INDEX IF NOT EXISTS idx_social_accounts_user_id ON social_accounts(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_social_accounts_provider ON social_accounts(provider, provider_id)`,
      `CREATE INDEX IF NOT EXISTS idx_user_mfa_setup_user_id ON user_mfa_setup(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_sms_verification_codes_user_id ON sms_verification_codes(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_sms_verification_codes_expires_at ON sms_verification_codes(expires_at)`,
      `CREATE INDEX IF NOT EXISTS idx_user_activity_user_id ON user_activity(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_user_activity_created_at ON user_activity(created_at)`,
      `CREATE INDEX IF NOT EXISTS idx_session_keys_session_id ON session_keys(session_id)`,
      `CREATE INDEX IF NOT EXISTS idx_session_keys_user_id ON session_keys(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_session_keys_app_id ON session_keys(app_id)`,
      `CREATE INDEX IF NOT EXISTS idx_session_keys_key_hash ON session_keys(key_hash)`,
      `CREATE INDEX IF NOT EXISTS idx_session_keys_expires_at ON session_keys(expires_at)`,
      `CREATE INDEX IF NOT EXISTS idx_session_keys_is_active ON session_keys(is_active)`,
      `CREATE INDEX IF NOT EXISTS idx_permission_definitions_name ON permission_definitions(name)`,
      `CREATE INDEX IF NOT EXISTS idx_permission_definitions_category ON permission_definitions(category)`,
      `CREATE INDEX IF NOT EXISTS idx_session_key_usage_session_key_id ON session_key_usage(session_key_id)`,
      `CREATE INDEX IF NOT EXISTS idx_session_key_usage_user_id ON session_key_usage(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_session_key_usage_created_at ON session_key_usage(created_at)`
    ];

    for (const stmt of indexStatements) {
      try {
        this.db.exec(stmt);
      } catch (err) {
        // Ignore index creation errors (e.g., legacy schemas missing columns pre-migration)
      }
    }
  }

  // runMigrations moved to separate file

  private async insertDefaultConfigs(): Promise<void> {
    if (!this.db) throw new Error("Database not initialized");

    // Insert default permission definitions
    const defaultPermissions = [
      { id: 'read_profile', name: 'read_profile', description: 'Read user profile information', category: 'user' },
      { id: 'update_profile', name: 'update_profile', description: 'Update user profile information', category: 'user' },
      { id: 'read_wallets', name: 'read_wallets', description: 'Read wallet information', category: 'wallet' },
      { id: 'create_wallet', name: 'create_wallet', description: 'Create new wallets', category: 'wallet' },
      { id: 'sign_transactions', name: 'sign_transactions', description: 'Sign transactions', category: 'wallet' },
      { id: 'transfer_assets', name: 'transfer_assets', description: 'Transfer assets between wallets', category: 'wallet' },
      { id: 'read_analytics', name: 'read_analytics', description: 'Read analytics data', category: 'analytics' },
      { id: 'manage_sessions', name: 'manage_sessions', description: 'Manage user sessions', category: 'session' },
      { id: 'manage_mfa', name: 'manage_mfa', description: 'Manage multi-factor authentication', category: 'security' },
      { id: 'admin_users', name: 'admin_users', description: 'Administrative user management', category: 'admin', is_system: true },
      { id: 'admin_apps', name: 'admin_apps', description: 'Administrative app management', category: 'admin', is_system: true }
    ];

    const insertPermissionStmt = this.db.prepare(`
      INSERT OR IGNORE INTO permission_definitions (id, name, description, category, is_system)
      VALUES (?, ?, ?, ?, ?)
    `);

    for (const permission of defaultPermissions) {
      const isSystemNumeric = permission.is_system ? 1 : 0;
      insertPermissionStmt.run(
        permission.id,
        permission.name,
        permission.description,
        permission.category,
        isSystemNumeric
      );
    }
    
    // Insert baseline system configs if missing
    const insertConfig = this.db.prepare(`
      INSERT OR IGNORE INTO system_configs (config_key, config_value, config_type, description)
      VALUES (?, ?, ?, ?)
    `);
    insertConfig.run('default_wallet_type', 'ethereum', 'string', 'Default network used for embedded wallet creation');
    insertConfig.run('auto_create_wallet', 'false', 'boolean', 'Auto-create a wallet on first login');
    insertConfig.run('max_wallets_per_user', '5', 'number', 'Maximum active wallets allowed per user');

    console.log("Default configurations and permissions inserted successfully");
  }

  getDatabase(): any {
    if (!this.db) {
      throw new Error("Database not initialized");
    }
    return this.db;
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
      logger.info("Database connection closed");
    }
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();
 