import { logger } from "../utils/logger";
import { v4 as uuidv4 } from 'uuid';

export type Migration = { id: string; name: string; up: (db: any) => void };

export async function runMigrations(db: any): Promise<void> {
  if (!db) throw new Error("Database not initialized");

  const migrations: Migration[] = [
    {
      id: '0005_create_app_allowed_origins',
      name: 'Create app_allowed_origins and seed localhost for existing apps',
      up: (db) => {
        db.exec(`CREATE TABLE IF NOT EXISTS app_allowed_origins (
          id TEXT PRIMARY KEY,
          app_id TEXT NOT NULL,
          origin TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(app_id, origin),
          FOREIGN KEY (app_id) REFERENCES apps (id) ON DELETE CASCADE
        )`);
        db.exec(`CREATE INDEX IF NOT EXISTS idx_app_allowed_origins_app_id ON app_allowed_origins(app_id)`);

        // Seed defaults for existing apps if none present
        const apps = db.prepare(`SELECT id FROM apps`).all() as { id: string }[];
        const existing = db.prepare(`SELECT 1 FROM app_allowed_origins WHERE app_id = ? LIMIT 1`);
        const insert = db.prepare(`INSERT OR IGNORE INTO app_allowed_origins (id, app_id, origin) VALUES (?, ?, ?)`);
        for (const a of apps) {
          const hasAny = existing.get(a.id);
          if (!hasAny) {
            try { insert.run(uuidv4(), a.id, 'http://localhost:3000'); } catch (e) { void e; }
            try { insert.run(uuidv4(), a.id, 'http://localhost:3002'); } catch (e) { void e; }
          }
        }
      },
    },
    {
      id: '0002_fix_wallets_and_autocreated_schema',
      name: 'Ensure wallets.user_id and auto_created_wallets.wallet_id/wallet_type exist',
      up: (db) => {
        try { db.exec(`ALTER TABLE wallets ADD COLUMN user_id TEXT`); } catch (e) { /* ignore if exists */ }
        try { db.exec(`ALTER TABLE auto_created_wallets ADD COLUMN wallet_id TEXT`); } catch (e) { /* ignore if exists */ }
        try { db.exec(`ALTER TABLE auto_created_wallets ADD COLUMN wallet_type TEXT DEFAULT 'embedded'`); } catch (e) { /* ignore if exists */ }
      },
    },
    {
      id: '0001_system_configs_and_indexes',
      name: 'Create system_configs and helpful composite indexes',
      up: (db) => {
        db.exec(`CREATE TABLE IF NOT EXISTS system_configs (
          config_key TEXT PRIMARY KEY,
          config_value TEXT,
          config_type TEXT DEFAULT 'string',
          description TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`);
        db.exec(`CREATE INDEX IF NOT EXISTS idx_system_configs_key ON system_configs(config_key)`);
        try { db.exec(`CREATE INDEX IF NOT EXISTS idx_wallets_user_id_is_active ON wallets(user_id, is_active)`); } catch (e) { /* ignore if legacy schema */ }
        try { db.exec(`CREATE INDEX IF NOT EXISTS idx_sessions_user_id_is_active ON sessions(user_id, is_active)`); } catch (e) { /* ignore if legacy schema */ }
      },
    },
    {
      id: '0003_add_email_verified_to_users',
      name: 'Add email_verified and email_verified_at columns to users',
      up: (db) => {
        try { db.exec(`ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT 0`); } catch (e) { /* ignore if exists */ }
        try { db.exec(`ALTER TABLE users ADD COLUMN email_verified_at DATETIME`); } catch (e) { /* ignore if exists */ }
      },
    },
    {
      id: '0004_add_auth_columns_to_app_settings',
      name: 'Ensure auth_type and email_verification_required exist in app_settings',
      up: (db) => {
        try { db.exec(`ALTER TABLE app_settings ADD COLUMN auth_type TEXT DEFAULT 'password'`); } catch (e) { /* ignore if exists */ }
        try { db.exec(`ALTER TABLE app_settings ADD COLUMN email_verification_required BOOLEAN DEFAULT FALSE`); } catch (e) { /* ignore if exists */ }
        try { db.exec(`ALTER TABLE app_settings ADD COLUMN default_wallet_network TEXT DEFAULT 'ethereum'`); } catch (e) { /* ignore if exists */ }
      },
    },
  ];

  const appliedIds = new Set<string>();
  const rows = db.prepare("SELECT id FROM schema_migrations").all() as { id: string }[];
  rows.forEach((r) => appliedIds.add(r.id));

  const insertStmt = db.prepare(`INSERT INTO schema_migrations (id, name) VALUES (?, ?)`);
  for (const m of migrations) {
    if (appliedIds.has(m.id)) continue;
    m.up(db);
    insertStmt.run(m.id, m.name);
    logger.info(`Applied migration: ${m.id} - ${m.name}`);
  }
}


