// Basic OpenAPI 3.0 spec for Tokai API. Extend as endpoints evolve.
export const openApiSpec: any = {
  openapi: "3.0.3",
  info: {
    title: "Tokai API",
    version: "1.0.0",
    description:
      "Comprehensive API documentation for Tokai. Authentication uses secure HTTP-only cookies. Some examples use Bear<PERSON> for service and session keys.",
  },
  servers: [
    {
      url: process.env.BASE_URL
        ? `${process.env.BASE_URL}/api`
        : "http://localhost:3001/api",
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
      },
    },
    schemas: {
      User: {
        type: "object",
        properties: {
          id: { type: "string" },
          email: { type: "string" },
          full_name: { type: "string", nullable: true },
          email_verified: { type: "boolean", nullable: true },
          email_verified_at: {
            type: "string",
            format: "date-time",
            nullable: true,
          },
          created_at: { type: "string", format: "date-time" },
        },
      },
      Wallet: {
        type: "object",
        properties: {
          id: { type: "string" },
          user_id: { type: "string" },
          network: { type: "string" },
          wallet_type: { type: "string" },
          wallet_address: { type: "string" },
          public_key: { type: "string" },
          derivation_path: { type: "string" },
          is_active: { type: "boolean" },
          metadata: { type: "string" },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
        },
      },
      ApiKey: {
        type: "object",
        properties: {
          id: { type: "string" },
          name: { type: "string" },
          permissions: { type: "string" },
          is_active: { type: "boolean" },
          last_used: { type: "string", format: "date-time", nullable: true },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
        },
      },
      App: {
        type: "object",
        properties: {
          id: { type: "string" },
          name: { type: "string" },
          description: { type: "string", nullable: true },
          api_key_preview: { type: "string", nullable: true },
          domain: { type: "string", nullable: true },
          status: { type: "string" },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
        },
      },
      SessionKey: {
        type: "object",
        properties: {
          id: { type: "string" },
          keyName: { type: "string" },
          permissions: { type: "array", items: { type: "string" } },
          scope: { type: "string", nullable: true },
          expiresAt: { type: "string", format: "date-time" },
          createdAt: { type: "string", format: "date-time" },
          lastUsedAt: { type: "string", format: "date-time", nullable: true },
          isActive: { type: "boolean" },
        },
      },
    },
  },
  security: [],
  paths: {
    "/auth/register": {
      post: {
        summary: "Register a new user",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  email: { type: "string" },
                  password: { type: "string" },
                  full_name: { type: "string" },
                },
                required: ["email", "password"],
              },
            },
          },
        },
        responses: {
          "200": {
            description: "User registered",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        user: { $ref: "#/components/schemas/User" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/auth/login": {
      post: {
        summary: "Login with email/password",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  email: { type: "string" },
                  password: { type: "string" },
                },
                required: ["email", "password"],
              },
            },
          },
        },
        responses: { "200": { description: "Logged in (cookies set)" } },
      },
    },
    "/auth/me": {
      get: {
        summary: "Get current authenticated user",
        responses: {
          "200": {
            description: "Current user",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        user: { $ref: "#/components/schemas/User" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/auth/logout": {
      post: {
        summary: "Logout current session",
        responses: { "200": { description: "Logged out" } },
      },
    },
    "/auth/logout-all": {
      post: {
        summary: "Logout all sessions",
        responses: { "200": { description: "Logged out all sessions" } },
      },
    },
    "/auth/refresh": {
      post: {
        summary: "Refresh access token via cookie",
        responses: { "200": { description: "Refreshed" } },
      },
    },

    "/wallets": {
      get: {
        summary: "List wallets for current user",
        responses: {
          "200": {
            description: "List of wallets",
            content: {
              "application/json": {
                schema: {
                  type: "array",
                  items: { $ref: "#/components/schemas/Wallet" },
                },
              },
            },
          },
        },
      },
      post: {
        summary: "Create a wallet",
        requestBody: {
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: { network: { type: "string" } },
              },
            },
          },
        },
        responses: {
          "200": {
            description: "Wallet created",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Wallet" },
              },
            },
          },
        },
      },
    },
    "/wallets/{walletId}": {
      get: {
        summary: "Get wallet by ID",
        parameters: [
          {
            name: "walletId",
            in: "path",
            required: true,
            schema: { type: "string" },
          },
        ],
        responses: {
          "200": {
            description: "Wallet",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Wallet" },
              },
            },
          },
        },
      },
    },

    "/apps": {
      get: {
        summary: "List apps",
        responses: {
          "200": {
            description: "Apps",
            content: {
              "application/json": {
                schema: {
                  type: "array",
                  items: { $ref: "#/components/schemas/App" },
                },
              },
            },
          },
        },
      },
      post: {
        summary: "Create app",
        requestBody: {
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  name: { type: "string" },
                  description: { type: "string" },
                  domain: { type: "string" },
                },
                required: ["name"],
              },
            },
          },
        },
        responses: {
          "200": {
            description: "Created",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/App" },
              },
            },
          },
        },
      },
    },
    "/apps/{appId}": {
      get: {
        summary: "Get app",
        parameters: [
          {
            name: "appId",
            in: "path",
            required: true,
            schema: { type: "string" },
          },
        ],
        responses: {
          "200": {
            description: "App",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/App" },
              },
            },
          },
        },
      },
      put: {
        summary: "Update app",
        parameters: [
          {
            name: "appId",
            in: "path",
            required: true,
            schema: { type: "string" },
          },
        ],
        requestBody: {
          content: { "application/json": { schema: { type: "object" } } },
        },
        responses: { "200": { description: "Updated" } },
      },
      delete: {
        summary: "Delete app",
        parameters: [
          {
            name: "appId",
            in: "path",
            required: true,
            schema: { type: "string" },
          },
        ],
        responses: { "200": { description: "Deleted" } },
      },
    },

    "/apps/{appId}/wallets": {
      get: {
        summary: "List wallets for app",
        parameters: [
          {
            name: "appId",
            in: "path",
            required: true,
            schema: { type: "string" },
          },
        ],
        responses: {
          "200": {
            description: "Wallets",
            content: {
              "application/json": {
                schema: {
                  type: "array",
                  items: { $ref: "#/components/schemas/Wallet" },
                },
              },
            },
          },
        },
      },
      post: {
        summary: "Create app wallet",
        parameters: [
          {
            name: "appId",
            in: "path",
            required: true,
            schema: { type: "string" },
          },
        ],
        requestBody: {
          content: { "application/json": { schema: { type: "object" } } },
        },
        responses: {
          "200": {
            description: "Created",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Wallet" },
              },
            },
          },
        },
      },
    },

    "/apps/{appId}/api-keys": {
      get: {
        summary: "List app API keys",
        parameters: [
          {
            name: "appId",
            in: "path",
            required: true,
            schema: { type: "string" },
          },
        ],
        responses: {
          "200": {
            description: "API Keys",
            content: {
              "application/json": {
                schema: {
                  type: "array",
                  items: { $ref: "#/components/schemas/ApiKey" },
                },
              },
            },
          },
        },
      },
      post: {
        summary: "Create app API key",
        parameters: [
          {
            name: "appId",
            in: "path",
            required: true,
            schema: { type: "string" },
          },
        ],
        requestBody: {
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  name: { type: "string" },
                  permissions: { type: "string" },
                },
                required: ["name"],
              },
            },
          },
        },
        responses: {
          "200": {
            description: "Created",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/ApiKey" },
              },
            },
          },
        },
      },
    },

    "/session-keys": {
      get: {
        summary: "List session keys",
        responses: {
          "200": {
            description: "Session keys",
            content: {
              "application/json": {
                schema: {
                  type: "array",
                  items: { $ref: "#/components/schemas/SessionKey" },
                },
              },
            },
          },
        },
      },
      post: {
        summary: "Create session key",
        requestBody: {
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  keyName: { type: "string" },
                  permissions: { type: "array", items: { type: "string" } },
                  scope: { type: "string" },
                  expiryDuration: { type: "number" },
                  metadata: { type: "object" },
                },
                required: ["keyName", "permissions"],
              },
            },
          },
        },
        responses: {
          "200": {
            description: "Created",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/SessionKey" },
              },
            },
          },
        },
      },
    },
    "/session-keys/{keyId}": {
      get: {
        summary: "Get session key",
        parameters: [
          {
            name: "keyId",
            in: "path",
            required: true,
            schema: { type: "string" },
          },
        ],
        responses: {
          "200": {
            description: "Session key",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/SessionKey" },
              },
            },
          },
        },
      },
      put: {
        summary: "Update session key",
        parameters: [
          {
            name: "keyId",
            in: "path",
            required: true,
            schema: { type: "string" },
          },
        ],
        requestBody: {
          content: { "application/json": { schema: { type: "object" } } },
        },
        responses: { "200": { description: "Updated" } },
      },
      delete: {
        summary: "Revoke session key",
        parameters: [
          {
            name: "keyId",
            in: "path",
            required: true,
            schema: { type: "string" },
          },
        ],
        responses: { "200": { description: "Revoked" } },
      },
    },

    "/analytics/dashboard": {
      get: {
        summary: "Dashboard metrics",
        responses: { "200": { description: "Metrics" } },
      },
    },
    "/analytics/users": {
      get: {
        summary: "User analytics",
        responses: { "200": { description: "User analytics" } },
      },
    },
    "/analytics/wallets": {
      get: {
        summary: "Wallet analytics",
        responses: { "200": { description: "Wallet analytics" } },
      },
    },
    "/analytics/security": {
      get: {
        summary: "Security analytics",
        responses: { "200": { description: "Security analytics" } },
      },
    },
  },
};
