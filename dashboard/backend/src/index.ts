import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import dotenv from 'dotenv';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { generalRateLimit } from './middleware/rateLimiting';
import { authRoutes } from './routes/auth';
import { walletRoutes } from './routes/wallets';
import { configRoutes } from './routes/config';
import { apiKeyRoutes } from './routes/apiKeys';
import { settingsRoutes } from './routes/settings';
import { appsRoutes } from './routes/apps';
import { appSettingsRoutes } from './routes/appSettings';
import { appWalletsRoutes } from './routes/appWallets';
import { appApiKeysRoutes } from './routes/appApiKeys';
import { appDomainsRoutes } from './routes/appDomains';
import mfaRoutes from './routes/mfa';
import { analyticsRoutes } from './routes/analytics';
import sessionKeysRoutes from './routes/sessionKeys';
import swaggerUi from 'swagger-ui-express';
import { openApiSpec } from './docs/openapi';
import { enforceAppOriginIfPresent } from './middleware/appOrigin';
import { databaseService } from './database/database';
import { scheduleService } from './services/scheduleService';

import { cacheService } from './services/cacheService';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet());

// CORS configuration
const allowedOrigins = process.env.FRONTEND_URL 
  ? process.env.FRONTEND_URL.split(',').map(url => url.trim())
  : ['http://localhost:3000', 'http://localhost:3002'];

app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Rate limiting - apply to all API routes unless disabled for local/dev
if (process.env.DISABLE_RATE_LIMIT !== '1' && process.env.NODE_ENV !== 'development') {
  app.use('/api/', generalRateLimit);
}

// Body parsing middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: process.env.pnpm_package_version || '1.0.0'
  });
});

// Redis health endpoint
app.get('/health/redis', async (req, res) => {
  try {
    const status = await cacheService.health();
    const httpCode = status.enabled && !status.connected ? 503 : 200;
    res.status(httpCode).json({
      success: true,
      redis: status,
      timestamp: new Date().toISOString(),
    });
  } catch (err: any) {
    res.status(500).json({ success: false, error: err?.message || 'Redis health check failed' });
  }
});

// API routes
app.use('/api/auth', enforceAppOriginIfPresent, authRoutes);
app.use('/api/wallets', enforceAppOriginIfPresent, walletRoutes);
app.use('/api/config', configRoutes);
app.use('/api/api-keys', apiKeyRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/apps', enforceAppOriginIfPresent, appsRoutes);
app.use('/api/app-settings', enforceAppOriginIfPresent, appSettingsRoutes);
app.use('/api/apps', enforceAppOriginIfPresent, appWalletsRoutes);
app.use('/api/apps', enforceAppOriginIfPresent, appApiKeysRoutes);
app.use('/api/apps', appDomainsRoutes);
app.use('/api/mfa', mfaRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/session-keys', sessionKeysRoutes);

// API docs (Swagger UI)
app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(openApiSpec));

// API routes only - frontend is served separately

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Error handling middleware
app.use(errorHandler);

// Initialize database and start server
async function startServer() {
  try {
    logger.info('Starting server initialization...');
    
    // Initialize database
    await databaseService.initialize();
    logger.info('Database initialized');

    // Start scheduled services
    scheduleService.start();
    logger.info('Scheduled services started');

    // Start server
    app.listen(PORT, () => {
      logger.info(`🚀 Server running on port ${PORT}`);
      logger.info(`📊 Health check: http://localhost:${PORT}/health`);
      logger.info(`🔗 API Base URL: http://localhost:${PORT}/api`);
    });
    
    logger.info('Server startup completed');
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  scheduleService.stop();
  await databaseService.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  scheduleService.stop();
  await databaseService.close();
  process.exit(0);
});

// Start the server
startServer(); 