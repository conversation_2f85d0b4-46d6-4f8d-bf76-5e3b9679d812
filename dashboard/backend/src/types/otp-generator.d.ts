declare module 'otp-generator' {
  interface OTPGeneratorOptions {
    digits?: boolean;
    alphabets?: boolean;
    upperCase?: boolean;
    specialChars?: boolean;
    upperCaseAlphabets?: boolean;
    lowerCaseAlphabets?: boolean;
  }

  interface OTPGenerator {
    generate(length: number, options?: OTPGeneratorOptions): string;
  }

  const otpGenerator: OTPGenerator;
  export default otpGenerator;
}