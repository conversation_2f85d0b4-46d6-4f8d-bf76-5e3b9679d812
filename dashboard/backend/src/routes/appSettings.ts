import express from 'express';
import { body, validationResult } from 'express-validator';
import { authMiddleware, AuthRequest } from '../middleware/combinedAuth';
import { asyncHandler } from '../utils/asyncHandler';
import { databaseService } from '../database/database';

import { v4 as uuidv4 } from 'uuid';
import { cacheService } from '../services/cacheService';

const router: import('express').Router = express.Router();

/**
 * GET /api/app-settings/:appId
 * Get app authentication settings
 */
router.get(
  "/:appId",
  authMiddleware,
  asyncHandler(async (req: AuthRequest, res) => {
    const { appId } = req.params;
    const db = databaseService.getDatabase();

    // Check if user owns this app
    const appStmt = db.prepare(`
      SELECT id, name, user_id FROM apps WHERE id = ?
    `);
    const app = appStmt.get(appId) as {
      id: string;
      name: string;
      user_id: string;
    } | undefined;

    if (!app) {
      return res.status(404).json({
        success: false,
        error: "App not found",
      });
    }

    if (app.user_id !== req.user?.id) {
      return res.status(403).json({
        success: false,
        error: "Access denied",
      });
    }

    // Get app settings with cache
    const cacheKey = `app_settings:${appId}`;
    const settings = await cacheService.wrap(cacheKey, 15_000, () => {
      const settingsStmt = db.prepare(`
        SELECT * FROM app_settings WHERE app_id = ?
      `);
      return settingsStmt.get(appId) as {
        id: string;
        app_id: string;
        google_client_id: string | null;
        google_client_secret: string | null;
        default_wallet_network: string;
        auto_create_wallets: number;
        email_otp_enabled: number;
        google_auth_enabled: number;
        auth_type: string;
        email_verification_required: number;
        created_at: string;
        updated_at: string;
      } | undefined;
    });

    res.json({
      success: true,
      data: {
        app: {
          id: app.id,
          name: app.name,
        },
        settings: settings ? {
          google_client_id: settings.google_client_id,
          google_client_secret: settings.google_client_secret,
          default_wallet_network: settings.default_wallet_network,
          auto_create_wallets: Boolean(settings.auto_create_wallets),
          email_otp_enabled: Boolean(settings.email_otp_enabled),
          google_auth_enabled: Boolean(settings.google_auth_enabled),
          auth_type: settings.auth_type,
          email_verification_required: Boolean(settings.email_verification_required),
        } : {
          google_client_id: null,
          google_client_secret: null,
          default_wallet_network: 'ethereum',
          auto_create_wallets: true,
          email_otp_enabled: true,
          google_auth_enabled: true,
          auth_type: 'password',
          email_verification_required: false,
        },
      },
    });
  })
);

/**
 * PUT /api/app-settings/:appId
 * Update app authentication settings
 */
router.put(
  "/:appId",
  authMiddleware,
  [
    body("auth_type").optional().isIn(['password', 'otp', 'both']).withMessage("Auth type must be password, otp, or both"),
    body("email_verification_required").optional().isBoolean().withMessage("Email verification required must be boolean"),
    body("email_otp_enabled").optional().isBoolean().withMessage("Email OTP enabled must be boolean"),
    body("google_auth_enabled").optional().isBoolean().withMessage("Google auth enabled must be boolean"),
    body("auto_create_wallets").optional().isBoolean().withMessage("Auto create wallets must be boolean"),
    body("default_wallet_network").optional().isIn(['ethereum', 'solana', 'polkadot', 'algorand', 'fivire', 'astar', 'avalanche', 'cardano', 'cosmos', 'near']).withMessage("Invalid wallet network"),
    body("google_client_id").optional().isString().withMessage("Google client ID must be string"),
    body("google_client_secret").optional().isString().withMessage("Google client secret must be string"),
  ],
  asyncHandler(async (req: AuthRequest, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { appId } = req.params;
    const {
      auth_type,
      email_verification_required,
      email_otp_enabled,
      google_auth_enabled,
      auto_create_wallets,
      default_wallet_network,
      google_client_id,
      google_client_secret,
    } = req.body;

    const db = databaseService.getDatabase();

    // Check if user owns this app
    const appStmt = db.prepare(`
      SELECT id, name, user_id FROM apps WHERE id = ?
    `);
    const app = appStmt.get(appId) as {
      id: string;
      name: string;
      user_id: string;
    } | undefined;

    if (!app) {
      return res.status(404).json({
        success: false,
        error: "App not found",
      });
    }

    if (app.user_id !== req.user?.id) {
      return res.status(403).json({
        success: false,
        error: "Access denied",
      });
    }

    // Check if settings exist
    const existingSettingsStmt = db.prepare(`
      SELECT id FROM app_settings WHERE app_id = ?
    `);
    const existingSettings = existingSettingsStmt.get(appId);

    if (existingSettings) {
      // Update existing settings
      const updateFields = [];
      const updateValues = [];

      if (auth_type !== undefined) {
        updateFields.push("auth_type = ?");
        updateValues.push(auth_type);
      }
      if (email_verification_required !== undefined) {
        updateFields.push("email_verification_required = ?");
        updateValues.push(email_verification_required ? 1 : 0);
      }
      if (email_otp_enabled !== undefined) {
        updateFields.push("email_otp_enabled = ?");
        updateValues.push(email_otp_enabled ? 1 : 0);
      }
      if (google_auth_enabled !== undefined) {
        updateFields.push("google_auth_enabled = ?");
        updateValues.push(google_auth_enabled ? 1 : 0);
      }
      if (auto_create_wallets !== undefined) {
        updateFields.push("auto_create_wallets = ?");
        updateValues.push(auto_create_wallets ? 1 : 0);
      }
      if (default_wallet_network !== undefined) {
        updateFields.push("default_wallet_network = ?");
        updateValues.push(default_wallet_network);
      }
      if (google_client_id !== undefined) {
        updateFields.push("google_client_id = ?");
        updateValues.push(google_client_id);
      }
      if (google_client_secret !== undefined) {
        updateFields.push("google_client_secret = ?");
        updateValues.push(google_client_secret);
      }

      if (updateFields.length > 0) {
        updateFields.push("updated_at = datetime('now')");
        updateValues.push(appId);

        const updateStmt = db.prepare(`
          UPDATE app_settings 
          SET ${updateFields.join(', ')}
          WHERE app_id = ?
        `);
        updateStmt.run(...updateValues);
      }
    } else {
      // Create new settings
      const insertStmt = db.prepare(`
        INSERT INTO app_settings (
          id, app_id, auth_type, email_verification_required, 
          email_otp_enabled, google_auth_enabled, auto_create_wallets,
          default_wallet_network, google_client_id, google_client_secret
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertStmt.run(
        uuidv4(),
        appId,
        auth_type || 'password',
        email_verification_required ? 1 : 0,
        email_otp_enabled !== undefined ? (email_otp_enabled ? 1 : 0) : 1,
        google_auth_enabled !== undefined ? (google_auth_enabled ? 1 : 0) : 1,
        auto_create_wallets !== undefined ? (auto_create_wallets ? 1 : 0) : 1,
        default_wallet_network || 'ethereum',
        google_client_id || null,
        google_client_secret || null
      );
    }

    // Invalidate cache after mutate
    await cacheService.del(`app_settings:${appId}`);

    // Get updated settings
    const settingsStmt = db.prepare(`
      SELECT * FROM app_settings WHERE app_id = ?
    `);
    const settings = settingsStmt.get(appId);

    res.json({
      success: true,
      data: {
        app: {
          id: app.id,
          name: app.name,
        },
        settings: {
          auth_type: settings.auth_type,
          email_verification_required: Boolean(settings.email_verification_required),
          email_otp_enabled: Boolean(settings.email_otp_enabled),
          google_auth_enabled: Boolean(settings.google_auth_enabled),
          auto_create_wallets: Boolean(settings.auto_create_wallets),
          default_wallet_network: settings.default_wallet_network,
          google_client_id: settings.google_client_id,
          google_client_secret: settings.google_client_secret,
        },
      },
      message: "App settings updated successfully",
    });
  })
);

/**
 * GET /api/app-settings/:appId/auth-config
 * Get app authentication configuration for frontend
 */
router.get(
  "/:appId/auth-config",
  asyncHandler(async (req, res) => {
    const { appId } = req.params;
    const db = databaseService.getDatabase();

    // Enforce domain whitelist if Origin is present
    const origin = req.headers.origin as string | undefined;
    if (origin) {
      const allowed = db
        .prepare('SELECT 1 FROM app_allowed_origins WHERE app_id = ? AND origin = ? LIMIT 1')
        .get(appId, origin);
      if (!allowed) {
        return res.status(403).json({ success: false, error: 'Origin not allowed for this app' });
      }
    }

    // Get app settings
    const settingsStmt = db.prepare(`
      SELECT auth_type, email_otp_enabled, google_auth_enabled, email_verification_required
      FROM app_settings WHERE app_id = ?
    `);
    const settings = settingsStmt.get(appId) as {
      auth_type: string;
      email_otp_enabled: number;
      google_auth_enabled: number;
      email_verification_required: number;
    } | undefined;

    // Get app info
    const appStmt = db.prepare(`
      SELECT id, name FROM apps WHERE id = ?
    `);
    const app = appStmt.get(appId) as {
      id: string;
      name: string;
    } | undefined;

    if (!app) {
      return res.status(404).json({
        success: false,
        error: "App not found",
      });
    }

    const defaultSettings = {
      auth_type: 'password',
      email_otp_enabled: true,
      google_auth_enabled: true,
      email_verification_required: false,
    };

    const config = settings ? {
      auth_type: settings.auth_type,
      email_otp_enabled: Boolean(settings.email_otp_enabled),
      google_auth_enabled: Boolean(settings.google_auth_enabled),
      email_verification_required: Boolean(settings.email_verification_required),
    } : defaultSettings;

    res.json({
      success: true,
      data: {
        app: {
          id: app.id,
          name: app.name,
        },
        auth_config: config,
      },
    });
  })
);

export { router as appSettingsRoutes }; 