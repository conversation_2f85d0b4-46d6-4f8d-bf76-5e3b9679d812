import { Router } from "express";
import { body, validationResult } from "express-validator";
import { databaseService } from "../database/database";
import { authMiddleware } from "../middleware/combinedAuth";
import { asyncHandler } from "../middleware/errorHandler";
import { logger } from "../utils/logger";

const router: import('express').Router = Router();

/**
 * GET /api/settings
 * Get current settings
 */
router.get(
  "/",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    // Get settings from database
    const settingsStmt = db.prepare(`
      SELECT 
        google_client_id,
        google_client_secret,
        default_wallet_network,
        auto_create_wallets,
        email_otp_enabled,
        google_auth_enabled,
        created_at,
        updated_at
      FROM settings 
      WHERE user_id = ?
    `);

    const settings = settingsStmt.get(req.user!.id) as any;

    // Return default settings if none exist
    const defaultSettings = {
      googleClientId: '',
      googleClientSecret: '',
      defaultWalletNetwork: 'ethereum',
      autoCreateWallets: true,
      emailOTPEnabled: true,
      googleAuthEnabled: true
    };

    if (!settings) {
      return res.json({
        success: true,
        data: defaultSettings,
      });
    }

    res.json({
      success: true,
      data: {
        googleClientId: settings.google_client_id || '',
        googleClientSecret: settings.google_client_secret || '',
        defaultWalletNetwork: settings.default_wallet_network || 'ethereum',
        autoCreateWallets: settings.auto_create_wallets ?? true,
        emailOTPEnabled: settings.email_otp_enabled ?? true,
        googleAuthEnabled: settings.google_auth_enabled ?? true,
        createdAt: settings.created_at,
        updatedAt: settings.updated_at
      },
    });
  }),
);

/**
 * PUT /api/settings
 * Update settings
 */
router.put(
  "/",
  authMiddleware,
  [
    body('googleClientId').optional().isString().trim(),
    body('googleClientSecret').optional().isString().trim(),
    body('defaultWalletNetwork').optional().isIn(['ethereum', 'solana']).withMessage('Default wallet network must be ethereum or solana (for embedded wallet creation)'),
    body('autoCreateWallets').optional().isBoolean(),
    body('emailOTPEnabled').optional().isBoolean(),
    body('googleAuthEnabled').optional().isBoolean(),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const {
      googleClientId,
      googleClientSecret,
      defaultWalletNetwork,
      autoCreateWallets,
      emailOTPEnabled,
      googleAuthEnabled
    } = req.body;

    const db = databaseService.getDatabase();

    try {
      // Check if settings already exist
      const existingStmt = db.prepare("SELECT id FROM settings WHERE user_id = ?");
      const existing = existingStmt.get(req.user!.id);

      const now = new Date().toISOString();

      if (existing) {
        // Update existing settings
        const updateStmt = db.prepare(`
          UPDATE settings SET 
            google_client_id = ?,
            google_client_secret = ?,
            default_wallet_network = ?,
            auto_create_wallets = ?,
            email_otp_enabled = ?,
            google_auth_enabled = ?,
            updated_at = ?
          WHERE user_id = ?
        `);

        updateStmt.run(
          googleClientId || null,
          googleClientSecret || null,
          defaultWalletNetwork || 'ethereum',
          autoCreateWallets ?? true,
          emailOTPEnabled ?? true,
          googleAuthEnabled ?? true,
          now,
          req.user!.id
        );
      } else {
        // Create new settings
        const insertStmt = db.prepare(`
          INSERT INTO settings (
            user_id,
            google_client_id,
            google_client_secret,
            default_wallet_network,
            auto_create_wallets,
            email_otp_enabled,
            google_auth_enabled,
            created_at,
            updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        insertStmt.run(
          req.user!.id,
          googleClientId || null,
          googleClientSecret || null,
          defaultWalletNetwork || 'ethereum',
          autoCreateWallets ?? true,
          emailOTPEnabled ?? true,
          googleAuthEnabled ?? true,
          now,
          now
        );
      }

      logger.info("Settings updated successfully", {
        userId: req.user!.id,
        defaultNetwork: defaultWalletNetwork,
        autoCreate: autoCreateWallets
      });

      res.json({
        success: true,
        message: "Settings updated successfully",
      });

    } catch (error) {
      logger.error("Error updating settings:", error);
      throw error;
    }
  }),
);

export { router as settingsRoutes }; 