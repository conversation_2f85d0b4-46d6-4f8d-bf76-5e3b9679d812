import { Router } from "express";
import { body, validationResult } from "express-validator";
import { v4 as uuidv4 } from "uuid";
import crypto from "crypto";

import { authMiddleware } from "../middleware/combinedAuth";
import { asyncHandler } from "../middleware/errorHandler";
import { logger } from "../utils/logger";
import { databaseService } from "../database/database";

const router: import('express').Router = Router();

/**
 * GET /api/apps/:appId/wallets
 * Get all wallets for a specific app
 */
router.get(
  "/:appId/wallets",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    // Verify app belongs to user
    const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
    const app = appStmt.get(req.params.appId, req.user!.id);

    if (!app) {
      return res.status(404).json({
        success: false,
        error: "App not found",
      });
    }

    const walletsStmt = db.prepare(`
      SELECT 
        id,
        end_user_id,
        network,
        wallet_type,
        wallet_address,
        public_key,
        derivation_path,
        is_active,
        metadata,
        created_at,
        updated_at
      FROM wallets 
      WHERE app_id = ? AND is_active = 1
      ORDER BY created_at DESC
    `);

    const wallets = walletsStmt.all(req.params.appId);

    res.json({
      success: true,
      data: wallets,
    });
  }),
);

/**
 * POST /api/apps/:appId/wallets
 * Create a new wallet for a specific app
 */
router.post(
  "/:appId/wallets",
  authMiddleware,
  [
    body('network').optional().isString().trim(),
    body('endUserId').optional().isString().trim(),
    body('walletType').optional().isIn(['embedded', 'custodial', 'non-custodial']),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { network, endUserId, walletType } = req.body;
    const db = databaseService.getDatabase();

    try {
      // Verify app belongs to user
      const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
      const app = appStmt.get(req.params.appId, req.user!.id);

      if (!app) {
        return res.status(404).json({
          success: false,
          error: "App not found",
        });
      }

      // Get app settings for default network
      const settingsStmt = db.prepare("SELECT default_wallet_network FROM app_settings WHERE app_id = ?");
      const settings = settingsStmt.get(req.params.appId) as any;
      const defaultNetwork = settings?.default_wallet_network || 'ethereum';

      // Create wallet directly in database since it now belongs to app
      const walletId = uuidv4();
      const walletAddress = `0x${crypto.randomBytes(20).toString('hex')}`; // Simplified for demo
      const encryptionKey = process.env.WALLET_ENCRYPTION_KEY || 'demo-key';
      
      // In a real implementation, you'd generate actual private keys and encrypt them
      const privateKey = crypto.randomBytes(32).toString('hex');
      const seedPhrase = 'demo seed phrase for ' + walletId;
      
      // Simple encryption (in production, use proper encryption)
      const encryptedPrivateKey = crypto.createHash('sha256').update(privateKey + encryptionKey).digest('hex');
      const encryptedSeedPhrase = crypto.createHash('sha256').update(seedPhrase + encryptionKey).digest('hex');

      const now = new Date().toISOString();

      const insertWalletStmt = db.prepare(`
        INSERT INTO wallets (
          id,
          app_id,
          end_user_id,
          network,
          wallet_type,
          wallet_address,
          encrypted_private_key,
          encrypted_seed_phrase,
          public_key,
          derivation_path,
          is_active,
          metadata,
          created_at,
          updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertWalletStmt.run(
        walletId,
        req.params.appId,
        endUserId || null,
        network || defaultNetwork,
        walletType || 'embedded',
        walletAddress,
        encryptedPrivateKey,
        encryptedSeedPhrase,
        walletAddress, // public key = address for demo
        "m/44'/60'/0'/0/0", // default derivation path
        true,
        JSON.stringify({ created_via: 'app_api' }),
        now,
        now
      );

      const wallet = {
        id: walletId,
        app_id: req.params.appId,
        end_user_id: endUserId || null,
        network: network || defaultNetwork,
        wallet_type: walletType || 'embedded',
        wallet_address: walletAddress,
        public_key: walletAddress,
        created_at: now
      };

      logger.info("App wallet created successfully", {
        appId: req.params.appId,
        walletId: wallet.id,
        network: wallet.network,
        endUserId
      });

      res.status(201).json({
        success: true,
        data: {
          id: wallet.id,
          app_id: req.params.appId,
          end_user_id: endUserId || null,
          network: wallet.network,
          wallet_type: wallet.wallet_type,
          wallet_address: wallet.wallet_address,
          public_key: wallet.public_key,
          created_at: wallet.created_at,
        },
        message: "Wallet created successfully",
      });

    } catch (error) {
      logger.error("Error creating app wallet:", error);
      throw error;
    }
  }),
);

/**
 * GET /api/apps/:appId/wallets/:walletId
 * Get a specific wallet for an app
 */
router.get(
  "/:appId/wallets/:walletId",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    // Verify app belongs to user
    const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
    const app = appStmt.get(req.params.appId, req.user!.id);

    if (!app) {
      return res.status(404).json({
        success: false,
        error: "App not found",
      });
    }

    const walletStmt = db.prepare(`
      SELECT 
        id,
        end_user_id,
        network,
        wallet_type,
        wallet_address,
        public_key,
        derivation_path,
        is_active,
        metadata,
        created_at,
        updated_at
      FROM wallets 
      WHERE id = ? AND app_id = ?
    `);

    const wallet = walletStmt.get(req.params.walletId, req.params.appId);

    if (!wallet) {
      return res.status(404).json({
        success: false,
        error: "Wallet not found",
      });
    }

    res.json({
      success: true,
      data: wallet,
    });
  }),
);

/**
 * DELETE /api/apps/:appId/wallets/:walletId
 * Delete a wallet for an app (soft delete)
 */
router.delete(
  "/:appId/wallets/:walletId",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    try {
      // Verify app belongs to user
      const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
      const app = appStmt.get(req.params.appId, req.user!.id);

      if (!app) {
        return res.status(404).json({
          success: false,
          error: "App not found",
        });
      }

      // Check if wallet exists and belongs to app
      const walletStmt = db.prepare("SELECT id FROM wallets WHERE id = ? AND app_id = ?");
      const wallet = walletStmt.get(req.params.walletId, req.params.appId);

      if (!wallet) {
        return res.status(404).json({
          success: false,
          error: "Wallet not found",
        });
      }

      // Soft delete the wallet
      const updateStmt = db.prepare(`
        UPDATE wallets SET 
          is_active = 0,
          updated_at = ?
        WHERE id = ? AND app_id = ?
      `);

      updateStmt.run(
        new Date().toISOString(),
        req.params.walletId,
        req.params.appId
      );

      logger.info("App wallet deleted successfully", {
        appId: req.params.appId,
        walletId: req.params.walletId
      });

      res.json({
        success: true,
        message: "Wallet deleted successfully",
      });

    } catch (error) {
      logger.error("Error deleting app wallet:", error);
      throw error;
    }
  }),
);

export { router as appWalletsRoutes }; 