import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import { v4 as uuidv4 } from 'uuid';
import { databaseService } from '../database/database';
import { authMiddleware } from '../middleware/combinedAuth';
import { asyncHand<PERSON> } from '../middleware/errorHandler';

const router: import('express').Router = Router();

// Utility to normalize origins
function normalizeOrigin(origin: string): string {
  const trimmed = origin.trim();
  if (!/^https?:\/\//i.test(trimmed)) return `http://${trimmed}`;
  return trimmed.replace(/\/$/, '');
}

/**
 * GET /api/apps/:appId/domains
 * List allowed origins for an app
 */
router.get(
  '/:appId/domains',
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    const app = db.prepare('SELECT id FROM apps WHERE id = ? AND user_id = ?').get(req.params.appId, req.user!.id);
    if (!app) {
      return res.status(404).json({ success: false, error: 'App not found' });
    }

    const rows = db
      .prepare('SELECT id, origin, created_at FROM app_allowed_origins WHERE app_id = ? ORDER BY created_at ASC')
      .all(req.params.appId);

    return res.json({ success: true, data: rows });
  })
);

/**
 * POST /api/apps/:appId/domains
 * Add a new allowed origin
 */
router.post(
  '/:appId/domains',
  authMiddleware,
  [body('origin').isString().trim().isLength({ min: 1, max: 255 })],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const db = databaseService.getDatabase();
    const app = db.prepare('SELECT id FROM apps WHERE id = ? AND user_id = ?').get(req.params.appId, req.user!.id);
    if (!app) {
      return res.status(404).json({ success: false, error: 'App not found' });
    }

    const normalized = normalizeOrigin(req.body.origin);
    const insert = db.prepare(
      'INSERT OR IGNORE INTO app_allowed_origins (id, app_id, origin) VALUES (?, ?, ?)'
    );
    insert.run(uuidv4(), req.params.appId, normalized);

    const row = db.prepare('SELECT id, origin, created_at FROM app_allowed_origins WHERE app_id = ? AND origin = ?').get(
      req.params.appId,
      normalized
    );
    return res.status(201).json({ success: true, data: row });
  })
);

/**
 * DELETE /api/apps/:appId/domains/:domainId
 * Remove an allowed origin
 */
router.delete(
  '/:appId/domains/:domainId',
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();
    const app = db.prepare('SELECT id FROM apps WHERE id = ? AND user_id = ?').get(req.params.appId, req.user!.id);
    if (!app) {
      return res.status(404).json({ success: false, error: 'App not found' });
    }

    const del = db.prepare('DELETE FROM app_allowed_origins WHERE id = ? AND app_id = ?');
    const info = del.run(req.params.domainId, req.params.appId);
    if (info.changes === 0) {
      return res.status(404).json({ success: false, error: 'Domain not found' });
    }
    return res.json({ success: true, message: 'Domain removed' });
  })
);

export { router as appDomainsRoutes };


