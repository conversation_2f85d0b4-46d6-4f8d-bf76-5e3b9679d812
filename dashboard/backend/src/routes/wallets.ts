import { Router } from "express";
import { body, param, validationResult } from "express-validator";
import { WalletService } from "../services/walletService";
import {
  authMiddleware as combinedAuthMiddleware,
  requirePermission,
} from "../middleware/combinedAuth";
import { asyncHand<PERSON> } from "../middleware/errorHandler";
import { logger } from "../utils/logger";
import { cacheService } from "../services/cacheService";

const router: import("express").Router = Router();

// Helper function to get user ID from authenticated user
const getUserId = (req: any): string => {
  return req.user?.id;
};

/**
 * GET /api/wallets
 * Get all wallets for the authenticated user
 */
router.get(
  "/",
  [combinedAuthMiddleware],
  requirePermission("read"),
  asyncHandler(async (req, res) => {
    const userId = getUserId(req);

    const cacheKey = `user_wallets:${userId}`;
    const wallets = await cacheService.wrap(cacheKey, 10_000, () =>
      WalletService.getUserWallets(userId)
    );

    res.json({
      success: true,
      data: wallets.map((wallet) => ({
        id: wallet.id,
        network: wallet.network,
        wallet_type: wallet.wallet_type,
        wallet_address: wallet.wallet_address,
        public_key: wallet.public_key,
        is_active: wallet.is_active,
        created_at: wallet.created_at,
        updated_at: wallet.updated_at,
      })),
    });
  })
);

/**
 * POST /api/wallets
 * Create a new wallet for the authenticated user
 */
router.post(
  "/",
  [
    combinedAuthMiddleware,
    requirePermission("write"),
    body("network")
      .optional()
      .isIn(["ethereum", "solana"])
      .withMessage(
        "Invalid network type. Only Ethereum and Solana are supported for embedded wallet creation"
      ),
  ],
  asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const userId = getUserId(req);
    const { network } = req.body;

    try {
      const { wallet, storedWallet } = await WalletService.createAndStoreWallet(
        userId,
        network,
        false // Not auto-created
      );

      logger.info(
        `User ${userId} created ${network || "default"} wallet: ${wallet.id}`
      );

      await cacheService.del(`user_wallets:${userId}`);
      res.status(201).json({
        success: true,
        data: {
          id: storedWallet.id,
          network: storedWallet.network,
          wallet_type: storedWallet.wallet_type,
          wallet_address: storedWallet.wallet_address,
          public_key: storedWallet.public_key,
          is_active: storedWallet.is_active,
          created_at: storedWallet.created_at,
        },
        message: `${storedWallet.network} wallet created successfully`,
      });
    } catch (error) {
      logger.error(`Failed to create wallet for user ${userId}:`, error);
      throw error;
    }
  })
);

/**
 * GET /api/wallets/:id
 * Get a specific wallet by ID
 */
router.get(
  "/:id",
  [param("id").isUUID().withMessage("Invalid wallet ID")],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const userId = req.user!.id;
    const walletId = req.params.id;

    const wallet = await WalletService.getWalletById(walletId, userId);

    if (!wallet) {
      return res.status(404).json({
        success: false,
        error: "Wallet not found",
      });
    }

    res.json({
      success: true,
      data: {
        id: wallet.id,
        network: wallet.network,
        wallet_type: wallet.wallet_type,
        wallet_address: wallet.wallet_address,
        public_key: wallet.public_key,
        is_active: wallet.is_active,
        created_at: wallet.created_at,
        updated_at: wallet.updated_at,
      },
    });
  })
);

/**
 * POST /api/wallets/:id/seed-phrase
 * Get the seed phrase for a wallet (requires additional verification)
 */
router.post(
  "/:id/seed-phrase",
  [
    param("id").isUUID().withMessage("Invalid wallet ID"),
    body("password")
      .isString()
      .isLength({ min: 1 })
      .withMessage("Password is required"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const userId = req.user!.id;
    const walletId = req.params.id;
    const { password: _password } = req.body;

    // TODO: Add additional verification (e.g., 2FA, email confirmation)
    // For now, we'll just verify the user owns the wallet

    try {
      const seedPhrase = await WalletService.decryptWalletSeedPhrase(
        walletId,
        userId
      );

      logger.info(`User ${userId} accessed seed phrase for wallet ${walletId}`);

      res.json({
        success: true,
        data: {
          seed_phrase: seedPhrase,
          warning:
            "Keep this seed phrase secure and never share it with anyone",
        },
      });
    } catch (error) {
      logger.error(
        `Failed to decrypt seed phrase for wallet ${walletId}:`,
        error
      );
      throw error;
    }
  })
);

/**
 * POST /api/wallets/:id/private-key
 * Get the private key for a wallet (requires additional verification)
 */
router.post(
  "/:id/private-key",
  [
    param("id").isUUID().withMessage("Invalid wallet ID"),
    body("password")
      .isString()
      .isLength({ min: 1 })
      .withMessage("Password is required"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const userId = req.user!.id;
    const walletId = req.params.id;
    const { password: _password } = req.body;

    // TODO: Add additional verification (e.g., 2FA, email confirmation)
    // For now, we'll just verify the user owns the wallet

    try {
      const privateKey = await WalletService.decryptWalletPrivateKey(
        walletId,
        userId
      );

      logger.info(`User ${userId} accessed private key for wallet ${walletId}`);

      res.json({
        success: true,
        data: {
          private_key: privateKey,
          warning:
            "Keep this private key secure and never share it with anyone",
        },
      });
    } catch (error) {
      logger.error(
        `Failed to decrypt private key for wallet ${walletId}:`,
        error
      );
      throw error;
    }
  })
);

/**
 * POST /api/wallets/auto-create
 * Trigger auto-creation of wallet for the authenticated user
 */
router.post(
  "/auto-create",
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;

    try {
      const shouldAutoCreate =
        await WalletService.shouldAutoCreateWallet(userId);

      if (!shouldAutoCreate) {
        return res.status(400).json({
          success: false,
          error: "Auto-creation not available for this user",
        });
      }

      const { wallet, storedWallet } = await WalletService.createAndStoreWallet(
        userId,
        undefined, // Use default from config
        true // Auto-created
      );

      logger.info(
        `Auto-created ${storedWallet.network} wallet for user ${userId}: ${wallet.id}`
      );

      res.status(201).json({
        success: true,
        data: {
          id: storedWallet.id,
          network: storedWallet.network,
          wallet_type: storedWallet.wallet_type,
          wallet_address: storedWallet.wallet_address,
          public_key: storedWallet.public_key,
          is_active: storedWallet.is_active,
          created_at: storedWallet.created_at,
        },
        message: `Auto-created ${storedWallet.network} wallet successfully`,
      });
    } catch (error) {
      logger.error(`Failed to auto-create wallet for user ${userId}:`, error);
      throw error;
    }
  })
);

/**
 * GET /api/wallets/supported-networks
 * Get list of supported networks
 */
router.get("/supported-networks", (req, res) => {
  const networks = WalletService.getSupportedNetworks();

  res.json({
    success: true,
    data: networks.map((network) => ({
      name: network,
      config: WalletService.getNetworkConfig(network),
    })),
  });
});

export { router as walletRoutes };
