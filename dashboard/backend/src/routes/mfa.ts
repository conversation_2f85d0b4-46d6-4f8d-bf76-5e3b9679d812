import { Router } from "express";
import { body, validationResult } from "express-validator";
import { async<PERSON>and<PERSON> } from "../middleware/errorHandler";
import { authMiddleware as combinedAuthMiddleware } from "../middleware/combinedAuth";
import MFAService from "../services/mfaService";
import { logger } from "../utils/logger";
import { databaseService } from '../database/database';

const router: import('express').Router = Router();

// Create MFA service instance
const mfaService = new MFAService();

/**
 * GET /api/mfa/config
 * Get user's MFA configuration
 */
router.get(
  "/config",
  combinedAuthMiddleware,
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    
    const config = await mfaService.getMFAConfig(userId);
    
    res.json({
      success: true,
      data: config,
    });
  }),
);

/**
 * POST /api/mfa/totp/setup
 * Initialize TOTP setup (generate secret and QR code)
 */
router.post(
  "/totp/setup",
  combinedAuthMiddleware,
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    
    // Get user email from database since it's not in the token payload
    const db = databaseService.getDatabase();
    const getUserStmt = db.prepare('SELECT email FROM users WHERE id = ?');
    const user = getUserStmt.get(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }
    
    const setup = await mfaService.setupTOTP(userId, user.email);
    
    res.json({
      success: true,
      data: {
        qrCodeUrl: setup.qrCodeUrl,
        backupCodes: setup.backupCodes,
        // Don't send the raw secret to the client
      },
    });
  }),
);

/**
 * POST /api/mfa/totp/verify
 * Verify TOTP code and enable TOTP
 */
router.post(
  "/totp/verify",
  combinedAuthMiddleware,
  [body("code").isLength({ min: 6, max: 6 }).withMessage("Invalid TOTP code")],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const userId = req.user!.id;
    const { code } = req.body;

    const verified = await mfaService.verifyAndEnableTOTP(userId, code);

    if (!verified) {
      return res.status(400).json({
        success: false,
        error: "Invalid TOTP code",
      });
    }

    logger.info(`TOTP enabled for user ${userId}`);

    res.json({
      success: true,
      message: "TOTP enabled successfully",
    });
  }),
);

/**
 * POST /api/mfa/sms/setup
 * Setup SMS MFA (send verification code)
 */
router.post(
  "/sms/setup",
  combinedAuthMiddleware,
  [
    body("phoneNumber")
      .isMobilePhone('any')
      .withMessage("Valid phone number is required"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const userId = req.user!.id;
    const { phoneNumber } = req.body;

    try {
      await mfaService.setupSMS(userId, phoneNumber);

      res.json({
        success: true,
        message: "SMS verification code sent",
      });
    } catch (error) {
      logger.error(`SMS setup failed for user ${userId}:`, error instanceof Error ? error.message : String(error));
      res.status(500).json({
        success: false,
        error: "Failed to send SMS verification code",
      });
    }
  }),
);

/**
 * POST /api/mfa/sms/verify
 * Verify SMS code and enable SMS MFA
 */
router.post(
  "/sms/verify",
  combinedAuthMiddleware,
  [body("code").isLength({ min: 6, max: 6 }).withMessage("Invalid SMS code")],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const userId = req.user!.id;
    const { code } = req.body;

    const verified = await mfaService.verifySMSAndEnable(userId, code);

    if (!verified) {
      return res.status(400).json({
        success: false,
        error: "Invalid or expired SMS code",
      });
    }

    logger.info(`SMS MFA enabled for user ${userId}`);

    res.json({
      success: true,
      message: "SMS MFA enabled successfully",
    });
  }),
);

/**
 * POST /api/mfa/send-sms
 * Send SMS verification code for login
 */
router.post(
  "/send-sms",
  combinedAuthMiddleware,
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;

    try {
      await mfaService.sendSMSCode(userId);

      res.json({
        success: true,
        message: "SMS verification code sent",
      });
    } catch (error: any) {
      logger.error(`SMS code send failed for user ${userId}:`, error?.message || String(error));
      res.status(500).json({
        success: false,
        error: error?.message || "Failed to send SMS code",
      });
    }
  }),
);

/**
 * POST /api/mfa/verify
 * Verify MFA code (TOTP, SMS, or backup)
 */
router.post(
  "/verify",
  combinedAuthMiddleware,
  [
    body("code").notEmpty().withMessage("Verification code is required"),
    body("method")
      .optional()
      .isIn(["totp", "sms", "backup"])
      .withMessage("Invalid verification method"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const userId = req.user!.id;
    const { code, method } = req.body;

    const verified = await mfaService.verifyMFA(userId, code, method);

    if (!verified) {
      return res.status(400).json({
        success: false,
        error: "Invalid verification code",
      });
    }

    res.json({
      success: true,
      message: "MFA verification successful",
    });
  }),
);

/**
 * POST /api/mfa/disable
 * Disable MFA for user
 */
router.post(
  "/disable",
  combinedAuthMiddleware,
  [
    body("method")
      .isIn(["totp", "sms", "all"])
      .withMessage("Invalid method. Must be 'totp', 'sms', or 'all'"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const userId = req.user!.id;
    const { method } = req.body;

    await mfaService.disableMFA(userId, method);

    logger.info(`${method} MFA disabled for user ${userId}`);

    res.json({
      success: true,
      message: `${method === 'all' ? 'All MFA methods' : method.toUpperCase() + ' MFA'} disabled successfully`,
    });
  }),
);

/**
 * POST /api/mfa/backup-codes/regenerate
 * Regenerate backup codes
 */
router.post(
  "/backup-codes/regenerate",
  combinedAuthMiddleware,
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;

    const newCodes = await mfaService.regenerateBackupCodes(userId);

    res.json({
      success: true,
      data: {
        backupCodes: newCodes,
      },
      message: "Backup codes regenerated successfully",
    });
  }),
);

export default router;