import express from 'express';
import { body, validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { authService, AuthService } from '../services/authService';
import { authMiddleware, AuthRequest } from '../middleware/combinedAuth';
import { asyncHandler } from '../utils/asyncHandler';
import jwt from 'jsonwebtoken';
import { databaseService } from '../database/database';
import { emailService } from '../services/emailService';
import { logger } from '../utils/logger';

const router: import('express').Router = express.Router();

// Helper function to verify credentials
async function verifyCredentials(email: string, password: string) {
  const db = databaseService.getDatabase();
  
  const userStmt = db.prepare(`
    SELECT id, email, password_hash, full_name, created_at
    FROM users
    WHERE email = ?
  `);

  const user = userStmt.get(email) as {
    id: string;
    email: string;
    password_hash: string;
    full_name?: string;
    created_at: string;
  } | undefined;

  if (!user) {
    throw new Error('Invalid email or password');
  }

  const isPasswordValid = await bcrypt.compare(password, user.password_hash);
  if (!isPasswordValid) {
    throw new Error('Invalid email or password');
  }

  return user;
}

/**
 * POST /api/auth/register
 * Register a new user
 */
router.post(
  "/register",
  [
    body("email").isEmail().withMessage("Valid email is required"),
    body("password").isLength({ min: 6 }).withMessage("Password must be at least 6 characters"),
    body("full_name").optional().isLength({ min: 2 }).withMessage("Full name must be at least 2 characters"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { email, password, full_name } = req.body;
    const db = databaseService.getDatabase();

    // Check if user already exists
    const existingUserStmt = db.prepare(`
      SELECT id FROM users WHERE email = ?
    `);
    const existingUser = existingUserStmt.get(email);

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: "User with this email already exists",
      });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const userId = uuidv4();
    const insertUserStmt = db.prepare(`
      INSERT INTO users (id, email, password_hash, full_name)
      VALUES (?, ?, ?, ?)
    `);

    insertUserStmt.run(userId, email, passwordHash, full_name || null);

    // Check if email verification is required
    const requiresEmailVerification = process.env.EMAIL_VERIFICATION_REQUIRED === 'true';
    
    if (requiresEmailVerification && emailService.isConfigured()) {
      try {
        // Send verification email
        await emailService.sendVerificationEmail(userId, email);
        
        res.status(201).json({
          success: true,
          data: {
            user: {
              id: userId,
              email,
              full_name: full_name || null,
              created_at: new Date().toISOString(),
              email_verified: false,
            },
            message: "Registration successful. Please check your email to verify your account.",
            requires_verification: true,
          },
        });
      } catch (error) {
        logger.error('Failed to send verification email during registration:', error);
        
        // Still create the user but inform about verification failure
        res.status(201).json({
          success: true,
          data: {
            user: {
              id: userId,
              email,
              full_name: full_name || null,
              created_at: new Date().toISOString(),
              email_verified: false,
            },
            message: "Registration successful. Please request email verification manually.",
            requires_verification: true,
          },
        });
      }
    } else {
      // Create session with token pair (no verification required)
      const deviceInfo = AuthService.extractDeviceInfo(req.headers['user-agent']);
      const ipAddress = req.ip || req.connection.remoteAddress;
      
      const tokenPair = await authService.createSession(
        userId,
        deviceInfo,
        ipAddress,
        req.headers['user-agent']
      );

      // Set secure cookies
      authService.setTokenCookies(res, tokenPair);

      res.status(201).json({
        success: true,
        data: {
          user: {
            id: userId,
            email,
            full_name: full_name || null,
            created_at: new Date().toISOString(),
            email_verified: !requiresEmailVerification,
          },
          message: "Registration successful"
        },
      });
    }
  }),
);

/**
 * POST /api/auth/login
 * Login user and create session
 */
router.post(
  "/login",
  [
    body("email").isEmail().withMessage("Valid email is required"),
    body("password").isLength({ min: 6 }).withMessage("Password must be at least 6 characters"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { email, password } = req.body;
    const deviceInfo = AuthService.extractDeviceInfo(req.headers['user-agent']);
    const ipAddress = req.ip || req.connection.remoteAddress;

    // Verify credentials and create session
    const user = await verifyCredentials(email, password);
    const tokenPair = await authService.createSession(
      user.id,
      deviceInfo,
      ipAddress,
      req.headers['user-agent']
    );

    // Set secure cookies
    authService.setTokenCookies(res, tokenPair);

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          created_at: user.created_at,
        },
        message: "Login successful"
      },
    });
  }),
);

/**
 * POST /api/auth/refresh
 * Refresh access token using refresh token from cookies
 */
router.post(
  "/refresh",
  asyncHandler(async (req, res) => {
    const { refreshToken } = authService.getTokensFromCookies(req);
    
    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        error: "No refresh token provided",
      });
    }

    try {
      const newTokens = await authService.refreshAccessToken(refreshToken);
      
      // Set new secure cookies
      authService.setTokenCookies(res, newTokens);

      res.json({
        success: true,
        data: {
          message: "Tokens refreshed successfully"
        },
      });
    } catch (error) {
      // Clear invalid cookies
      authService.clearTokenCookies(res);
      
      return res.status(401).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to refresh token',
      });
    }
  }),
);

/**
 * POST /api/auth/logout
 * Logout current session
 */
router.post(
  "/logout",
  authMiddleware,
  asyncHandler(async (req: AuthRequest, res) => {
    const userId = req.user?.id;
    
    if (userId) {
      // Invalidate current session
      const { accessToken } = authService.getTokensFromCookies(req);
      if (accessToken) {
        try {
          const payload = authService.verifyAccessToken(accessToken);
          await authService.invalidateSession(payload.sessionId);
        } catch (error) {
          // Token is invalid, just clear cookies
        }
      }
    }

    // Clear cookies
    authService.clearTokenCookies(res);

    res.json({
      success: true,
      message: "Logged out successfully",
    });
  }),
);

/**
 * POST /api/auth/logout-all
 * Logout from all devices (invalidate all sessions)
 */
router.post(
  "/logout-all",
  authMiddleware,
  asyncHandler(async (req: AuthRequest, res) => {
    const userId = req.user?.id;

    if (userId) {
      await authService.invalidateAllUserSessions(userId);
    }

    // Clear cookies
    authService.clearTokenCookies(res);

    res.json({
      success: true,
      message: "Logged out from all devices successfully",
    });
  }),
);

/**
 * GET /api/auth/sessions
 * Get all active sessions for current user
 */
router.get(
  "/sessions",
  authMiddleware,
  asyncHandler(async (req: AuthRequest, res) => {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: "User not authenticated",
      });
    }

    const sessions = await authService.getUserSessions(userId);
    const currentSessionId = req.user?.sessionId;

    res.json({
      success: true,
      data: {
        sessions: sessions.map(session => ({
          id: session.id,
          deviceInfo: session.deviceInfo,
          ipAddress: session.ipAddress,
          lastUsedAt: session.lastUsedAt,
          createdAt: session.createdAt,
          isCurrent: session.id === currentSessionId,
        })),
      },
    });
  }),
);

/**
 * DELETE /api/auth/sessions/:sessionId
 * Invalidate a specific session
 */
router.delete(
  "/sessions/:sessionId",
  authMiddleware,
  asyncHandler(async (req: AuthRequest, res) => {
    const { sessionId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: "User not authenticated",
      });
    }

    // Verify the session belongs to the user
    const sessions = await authService.getUserSessions(userId);
    const sessionExists = sessions.some(session => session.id === sessionId);

    if (!sessionExists) {
      return res.status(404).json({
        success: false,
        error: "Session not found",
      });
    }

    await authService.invalidateSession(sessionId);

    res.json({
      success: true,
      message: "Session invalidated successfully",
    });
  }),
);

/**
 * GET /api/auth/me
 * Get current user info
 */
router.get(
  "/me",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    // Get user from database using the authenticated user ID
    const userStmt = db.prepare(`
    SELECT id, email, full_name, created_at
    FROM users
    WHERE id = ?
  `);

    const user = userStmt.get(req.user!.id) as
      | {
          id: string;
          email: string;
          full_name?: string;
          created_at: string;
        }
      | undefined;

    if (!user) {
      return res.status(404).json({
        success: false,
        error: "User not found",
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          full_name: user.full_name || null,
          created_at: user.created_at,
        },
      },
    });
  }),
);

/**
 * GET /api/auth/session-stats
 * Get session statistics for current user
 */
router.get(
  "/session-stats",
  authMiddleware,
  asyncHandler(async (req: AuthRequest, res) => {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: "User not authenticated",
      });
    }

    const stats = await authService.getSessionStats(userId);

    res.json({
      success: true,
      data: {
        stats,
      },
    });
  }),
);

/**
 * POST /api/auth/social/initiate
 * Initiate social OAuth flow
 */
router.post(
  "/social/initiate",
  [
    body("provider")
      .isIn(["google", "discord", "twitter", "github", "telegram", "farcaster"])
      .withMessage("Invalid OAuth provider"),
    body("redirectUrl")
      .isURL()
      .withMessage("Valid redirect URL is required"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { provider, redirectUrl } = req.body;
    const state = uuidv4();

    // Store state for CSRF protection
    const db = databaseService.getDatabase();
    const storeStateStmt = db.prepare(`
      INSERT INTO oauth_states (state, provider, redirect_url, created_at, expires_at)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    storeStateStmt.run(state, provider, redirectUrl, new Date().toISOString(), expiresAt.toISOString());

    // Generate OAuth URLs based on provider
    let authUrl = "";
    const baseRedirectUri = `${process.env.BASE_URL || 'http://localhost:3001'}/api/auth/social/callback`;

    switch (provider) {
      case "google":
        const googleClientId = process.env.GOOGLE_CLIENT_ID;
        if (!googleClientId) {
          return res.status(500).json({
            success: false,
            error: "Google OAuth not configured",
          });
        }
        authUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
          `client_id=${googleClientId}&` +
          `redirect_uri=${encodeURIComponent(baseRedirectUri)}&` +
          `response_type=code&` +
          `scope=email profile&` +
          `state=${state}`;
        break;

      case "discord":
        const discordClientId = process.env.DISCORD_CLIENT_ID;
        if (!discordClientId) {
          return res.status(500).json({
            success: false,
            error: "Discord OAuth not configured",
          });
        }
        authUrl = `https://discord.com/api/oauth2/authorize?` +
          `client_id=${discordClientId}&` +
          `redirect_uri=${encodeURIComponent(baseRedirectUri)}&` +
          `response_type=code&` +
          `scope=identify email&` +
          `state=${state}`;
        break;

      case "twitter":
        const twitterClientId = process.env.TWITTER_CLIENT_ID;
        if (!twitterClientId) {
          return res.status(500).json({
            success: false,
            error: "Twitter OAuth not configured",
          });
        }
        authUrl = `https://twitter.com/i/oauth2/authorize?` +
          `client_id=${twitterClientId}&` +
          `redirect_uri=${encodeURIComponent(baseRedirectUri)}&` +
          `response_type=code&` +
          `scope=tweet.read users.read&` +
          `state=${state}&` +
          `code_challenge=challenge&` +
          `code_challenge_method=plain`;
        break;

      case "github":
        const githubClientId = process.env.GITHUB_CLIENT_ID;
        if (!githubClientId) {
          return res.status(500).json({
            success: false,
            error: "GitHub OAuth not configured",
          });
        }
        authUrl = `https://github.com/login/oauth/authorize?` +
          `client_id=${githubClientId}&` +
          `redirect_uri=${encodeURIComponent(baseRedirectUri)}&` +
          `scope=user:email&` +
          `state=${state}`;
        break;

      case "telegram":
        const telegramBotToken = process.env.TELEGRAM_BOT_TOKEN;
        const telegramBotUsername = process.env.TELEGRAM_BOT_USERNAME;
        if (!telegramBotToken || !telegramBotUsername) {
          return res.status(500).json({
            success: false,
            error: "Telegram OAuth not configured",
          });
        }
        // For Telegram, we'll use the widget approach
        // The actual authentication happens client-side with the widget
        // We'll return a special URL that signals the frontend to use the widget
        authUrl = `telegram-widget://${telegramBotUsername}?state=${state}`;
        break;

      case "farcaster":
        const farcasterClientId = process.env.FARCASTER_CLIENT_ID;
        if (!farcasterClientId) {
          return res.status(500).json({
            success: false,
            error: "Farcaster OAuth not configured",
          });
        }
        // Farcaster uses Sign In With Farcaster (SIWF)
        authUrl = `https://warpcast.com/~/sign-in-with-farcaster?` +
          `client_id=${farcasterClientId}&` +
          `redirect_uri=${encodeURIComponent(baseRedirectUri)}&` +
          `state=${state}&` +
          `scope=public`;
        break;
    }

    res.json({
      success: true,
      data: {
        authUrl,
        state,
      },
    });
  }),
);

/**
 * GET /api/auth/social/callback
 * Handle OAuth callback from providers
 */
router.get("/social/callback", asyncHandler(async (req, res) => {
  const { code, state, error } = req.query;

  if (error) {
    return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/auth/error?error=${error}`);
  }

  if (!code || !state) {
    return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/auth/error?error=missing_parameters`);
  }

  const db = databaseService.getDatabase();
  
  // Verify state for CSRF protection
  const stateStmt = db.prepare(`
    SELECT * FROM oauth_states 
    WHERE state = ? AND expires_at > ?
  `);
  const stateRecord = stateStmt.get(state as string, new Date().toISOString());

  if (!stateRecord) {
    return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/auth/error?error=invalid_state`);
  }

  try {
    // Exchange code for tokens based on provider
    let userInfo: any = null;
    const baseRedirectUri = `${process.env.BASE_URL || 'http://localhost:3001'}/api/auth/social/callback`;

    switch (stateRecord.provider) {
      case "google":
        userInfo = await exchangeGoogleCode(code as string, baseRedirectUri);
        break;
      case "discord":
        userInfo = await exchangeDiscordCode(code as string, baseRedirectUri);
        break;
      case "twitter":
        userInfo = await exchangeTwitterCode(code as string, baseRedirectUri);
        break;
      case "github":
        userInfo = await exchangeGithubCode(code as string, baseRedirectUri);
        break;
      case "telegram":
        userInfo = await exchangeTelegramCode(code as string, stateRecord.state);
        break;
      case "farcaster":
        userInfo = await exchangeFarcasterCode(code as string, baseRedirectUri);
        break;
      default:
        throw new Error("Unsupported provider");
    }

    // Check if user exists with this email
    const existingUserStmt = db.prepare("SELECT * FROM users WHERE email = ?");
    let user = existingUserStmt.get(userInfo.email);

    if (!user) {
      // Create new user
      const userId = uuidv4();
      const createUserStmt = db.prepare(`
        INSERT INTO users (id, email, full_name, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `);
      const now = new Date().toISOString();
      createUserStmt.run(userId, userInfo.email, userInfo.name, now, now);
      
      user = { id: userId, email: userInfo.email, full_name: userInfo.name };
    }

    // Store social account connection
    const socialAccountStmt = db.prepare(`
      INSERT OR REPLACE INTO social_accounts 
      (id, user_id, provider, provider_id, email, name, avatar_url, verified, connected_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    socialAccountStmt.run(
      uuidv4(),
      user.id,
      stateRecord.provider,
      userInfo.id,
      userInfo.email,
      userInfo.name,
      userInfo.avatar_url || null,
      true,
      new Date().toISOString()
    );

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET!,
      { expiresIn: "7d" }
    );

    // Clean up state
    const deleteStateStmt = db.prepare("DELETE FROM oauth_states WHERE state = ?");
    deleteStateStmt.run(state as string);

    // Redirect with token
    res.redirect(`${stateRecord.redirect_url}?token=${token}&success=true`);

  } catch (error) {
    logger.error("OAuth callback error:", error);
    res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/auth/error?error=oauth_failed`);
  }
}));

/**
 * POST /api/auth/social/link
 * Link social account to existing user
 */
router.post(
  "/social/link",
  authMiddleware,
  [
    body("provider")
      .isIn(["google", "discord", "twitter", "github", "telegram", "farcaster"])
      .withMessage("Invalid OAuth provider"),
    body("code").notEmpty().withMessage("Authorization code is required"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { provider, code } = req.body;
    const userId = req.user!.id;

    try {
      // Exchange code for user info (similar to callback logic)
      let userInfo: any = null;
      const baseRedirectUri = `${process.env.BASE_URL || 'http://localhost:3001'}/api/auth/social/callback`;

      switch (provider) {
        case "google":
          userInfo = await exchangeGoogleCode(code, baseRedirectUri);
          break;
        case "discord":
          userInfo = await exchangeDiscordCode(code, baseRedirectUri);
          break;
        case "twitter":
          userInfo = await exchangeTwitterCode(code, baseRedirectUri);
          break;
        case "github":
          userInfo = await exchangeGithubCode(code, baseRedirectUri);
          break;
        case "telegram":
          userInfo = await exchangeTelegramCode(code, "");
          break;
        case "farcaster":
          userInfo = await exchangeFarcasterCode(code, baseRedirectUri);
          break;
      }

      const db = databaseService.getDatabase();
      
      // Check if this social account is already linked
      const existingLinkStmt = db.prepare(`
        SELECT user_id FROM social_accounts 
        WHERE provider = ? AND provider_id = ?
      `);
      const existingLink = existingLinkStmt.get(provider, userInfo.id);

      if (existingLink && existingLink.user_id !== userId) {
        return res.status(400).json({
          success: false,
          error: "This social account is already linked to another user",
        });
      }

      // Link the account
      const linkStmt = db.prepare(`
        INSERT OR REPLACE INTO social_accounts 
        (id, user_id, provider, provider_id, email, name, avatar_url, verified, connected_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      linkStmt.run(
        uuidv4(),
        userId,
        provider,
        userInfo.id,
        userInfo.email,
        userInfo.name,
        userInfo.avatar_url || null,
        true,
        new Date().toISOString()
      );

      res.json({
        success: true,
        data: {
          provider,
          linked: true,
        },
      });

    } catch (error) {
      logger.error("Social link error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to link social account",
      });
    }
  }),
);

/**
 * DELETE /api/auth/social/unlink/:provider
 * Unlink social account from user
 */
router.delete(
  "/social/unlink/:provider",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const { provider } = req.params;
    const userId = req.user!.id;

    if (!["google", "discord", "twitter", "github", "telegram", "farcaster"].includes(provider)) {
      return res.status(400).json({
        success: false,
        error: "Invalid provider",
      });
    }

    const db = databaseService.getDatabase();
    
    const unlinkStmt = db.prepare(`
      DELETE FROM social_accounts 
      WHERE user_id = ? AND provider = ?
    `);
    const result = unlinkStmt.run(userId, provider);

    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        error: "Social account not found",
      });
    }

    res.json({
      success: true,
      data: {
        provider,
        unlinked: true,
      },
    });
  }),
);

/**
 * GET /api/auth/social/accounts
 * Get linked social accounts for user
 */
router.get(
  "/social/accounts",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    const db = databaseService.getDatabase();

    const accountsStmt = db.prepare(`
      SELECT provider, email, name, avatar_url, verified, connected_at
      FROM social_accounts 
      WHERE user_id = ?
      ORDER BY connected_at DESC
    `);
    const accounts = accountsStmt.all(userId);

    res.json({
      success: true,
      data: {
        accounts: accounts || [],
      },
    });
  }),
);

// Helper functions for OAuth token exchange
async function exchangeGoogleCode(code: string, redirectUri: string) {
  const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: new URLSearchParams({
      client_id: process.env.GOOGLE_CLIENT_ID!,
      client_secret: process.env.GOOGLE_CLIENT_SECRET!,
      code,
      grant_type: "authorization_code",
      redirect_uri: redirectUri,
    }),
  });

  const tokens = await tokenResponse.json();
  
  const userResponse = await fetch("https://www.googleapis.com/oauth2/v2/userinfo", {
    headers: { Authorization: `Bearer ${tokens.access_token}` },
  });

  const userInfo = await userResponse.json();
  return {
    id: userInfo.id,
    email: userInfo.email,
    name: userInfo.name,
    avatar_url: userInfo.picture,
  };
}

async function exchangeDiscordCode(code: string, redirectUri: string) {
  const tokenResponse = await fetch("https://discord.com/api/oauth2/token", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: new URLSearchParams({
      client_id: process.env.DISCORD_CLIENT_ID!,
      client_secret: process.env.DISCORD_CLIENT_SECRET!,
      code,
      grant_type: "authorization_code",
      redirect_uri: redirectUri,
    }),
  });

  const tokens = await tokenResponse.json();
  
  const userResponse = await fetch("https://discord.com/api/users/@me", {
    headers: { Authorization: `Bearer ${tokens.access_token}` },
  });

  const userInfo = await userResponse.json();
  return {
    id: userInfo.id,
    email: userInfo.email,
    name: userInfo.username,
    avatar_url: userInfo.avatar ? `https://cdn.discordapp.com/avatars/${userInfo.id}/${userInfo.avatar}.png` : null,
  };
}

async function exchangeTwitterCode(code: string, redirectUri: string) {
  const tokenResponse = await fetch("https://api.twitter.com/2/oauth2/token", {
    method: "POST",
    headers: { 
      "Content-Type": "application/x-www-form-urlencoded",
      "Authorization": `Basic ${Buffer.from(`${process.env.TWITTER_CLIENT_ID}:${process.env.TWITTER_CLIENT_SECRET}`).toString('base64')}`
    },
    body: new URLSearchParams({
      code,
      grant_type: "authorization_code",
      redirect_uri: redirectUri,
      code_verifier: "challenge",
    }),
  });

  const tokens = await tokenResponse.json();
  
  const userResponse = await fetch("https://api.twitter.com/2/users/me?user.fields=profile_image_url", {
    headers: { Authorization: `Bearer ${tokens.access_token}` },
  });

  const { data: userInfo } = await userResponse.json();
  return {
    id: userInfo.id,
    email: userInfo.email || `${userInfo.username}@twitter.placeholder`,
    name: userInfo.name,
    avatar_url: userInfo.profile_image_url,
  };
}

async function exchangeGithubCode(code: string, _redirectUri: string) {
  const tokenResponse = await fetch("https://github.com/login/oauth/access_token", {
    method: "POST",
    headers: { 
      "Content-Type": "application/x-www-form-urlencoded",
      "Accept": "application/json"
    },
    body: new URLSearchParams({
      client_id: process.env.GITHUB_CLIENT_ID!,
      client_secret: process.env.GITHUB_CLIENT_SECRET!,
      code,
    }),
  });

  const tokens = await tokenResponse.json();
  
  const userResponse = await fetch("https://api.github.com/user", {
    headers: { Authorization: `Bearer ${tokens.access_token}` },
  });

  const userInfo = await userResponse.json();
  
  // Get primary email
  const emailResponse = await fetch("https://api.github.com/user/emails", {
    headers: { Authorization: `Bearer ${tokens.access_token}` },
  });
  const emails = await emailResponse.json();
  const primaryEmail = emails.find((email: any) => email.primary)?.email || userInfo.email;

  return {
    id: userInfo.id.toString(),
    email: primaryEmail,
    name: userInfo.name || userInfo.login,
    avatar_url: userInfo.avatar_url,
  };
}

/**
 * POST /api/auth/send-verification-email
 * Send email verification
 */
router.post(
  "/send-verification-email",
  [
    body("email").isEmail().withMessage("Valid email is required"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { email } = req.body;
    const db = databaseService.getDatabase();

    // Check if user exists
    const userStmt = db.prepare(`
      SELECT id, email_verified FROM users WHERE email = ?
    `);
    const user = userStmt.get(email) as {
      id: string;
      email_verified: number;
    } | undefined;

    if (!user) {
      return res.status(404).json({
        success: false,
        error: "User not found",
      });
    }

    if (user.email_verified) {
      return res.status(400).json({
        success: false,
        error: "Email already verified",
      });
    }

    try {
      await emailService.sendVerificationEmail(user.id, email);
      
      res.json({
        success: true,
        message: "Verification email sent",
      });
    } catch (error) {
      logger.error('Failed to send verification email:', error);
      res.status(500).json({
        success: false,
        error: "Failed to send verification email",
      });
    }
  })
);

/**
 * POST /api/auth/telegram/callback
 * Handle Telegram authentication callback
 */
router.post(
  "/telegram/callback",
  [
    body("auth_data").notEmpty().withMessage("Authentication data is required"),
    body("state").optional().isString().withMessage("State must be a string"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { auth_data, state } = req.body;
    const db = databaseService.getDatabase();

    try {
      // Verify state if provided
      if (state) {
        const stateStmt = db.prepare(`
          SELECT * FROM oauth_states 
          WHERE state = ? AND expires_at > ?
        `);
        const stateRecord = stateStmt.get(state, new Date().toISOString());
        
        if (!stateRecord || stateRecord.provider !== 'telegram') {
          return res.status(401).json({
            success: false,
            error: "Invalid or expired state",
          });
        }
      }

      // Exchange Telegram auth data for user info
      const userInfo = await exchangeTelegramCode(JSON.stringify(auth_data), state || '');

      // Check if user exists with this email
      const existingUserStmt = db.prepare("SELECT * FROM users WHERE email = ?");
      let user = existingUserStmt.get(userInfo.email);

      if (!user) {
        // Create new user
        const userId = uuidv4();
        const insertUserStmt = db.prepare(`
          INSERT INTO users (id, email, full_name, email_verified)
          VALUES (?, ?, ?, ?)
        `);
        insertUserStmt.run(userId, userInfo.email, userInfo.name, true);
        user = { id: userId, email: userInfo.email };
      }

      // Link social account
      const socialStmt = db.prepare(`
        INSERT OR REPLACE INTO social_accounts 
        (id, user_id, provider, provider_id, email, name, avatar_url, verified)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);
      socialStmt.run(
        uuidv4(),
        user.id,
        'telegram',
        userInfo.id,
        userInfo.email,
        userInfo.name,
        userInfo.avatar_url,
        true
      );

      // Generate auth token
      const tokenPair = await authService.createSession(
        user.id,
        req.headers['user-agent'] as string,
        req.ip,
        req.headers['user-agent'] as string
      );

      // Clean up state if used
      if (state) {
        const deleteStateStmt = db.prepare("DELETE FROM oauth_states WHERE state = ?");
        deleteStateStmt.run(state);
      }

      res.json({
        success: true,
        data: {
          tokenPair,
          user: {
            id: user.id,
            email: user.email,
            name: userInfo.name,
            avatar_url: userInfo.avatar_url,
          },
        },
      });
    } catch (error) {
      logger.error("Telegram authentication error:", error);
      res.status(401).json({
        success: false,
        error: error instanceof Error ? error.message : "Authentication failed",
      });
    }
  })
);

/**
 * POST /api/auth/verify-email
 * Verify email with token
 */
router.post(
  "/verify-email",
  [
    body("token").notEmpty().withMessage("Token is required"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { token } = req.body;

    try {
      const result = await emailService.verifyEmailToken(token);
      
      if (!result) {
        return res.status(400).json({
          success: false,
          error: "Invalid or expired token",
        });
      }

      res.json({
        success: true,
        message: "Email verified successfully",
        user: {
          id: result.userId,
          email: result.email,
        },
      });
    } catch (error) {
      logger.error('Failed to verify email:', error);
      res.status(500).json({
        success: false,
        error: "Failed to verify email",
      });
    }
  })
);

/**
 * POST /api/auth/send-otp
 * Send OTP for email login (works for both new and existing users)
 */
router.post(
  "/send-otp",
  [
    body("email").isEmail().withMessage("Valid email is required"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { email } = req.body;
    const db = databaseService.getDatabase();

    // Check if user exists
    const userStmt = db.prepare(`
      SELECT id FROM users WHERE email = ?
    `);
    const user = userStmt.get(email) as {
      id: string;
    } | undefined;

    let userId: string;

    if (!user) {
      // Create new user for OTP-based registration
      userId = uuidv4();
      const insertUserStmt = db.prepare(`
        INSERT INTO users (id, email, password_hash, email_verified, created_at)
        VALUES (?, ?, ?, ?, datetime('now'))
      `);
      
      // Create a placeholder password hash (will be updated if user later sets password)
      const placeholderHash = await bcrypt.hash('temp_password_' + Date.now(), 10);
      insertUserStmt.run(userId, email, placeholderHash, 1); // Email verified by default for OTP users
      
      logger.info(`Created new user via OTP: ${email}`);
    } else {
      userId = user.id;
    }

    try {
      await emailService.sendOTPEmail(userId, email);
      
      res.json({
        success: true,
        message: "OTP sent to your email",
        isNewUser: !user, // Indicate if this is a new user
      });
    } catch (error) {
      logger.error('Failed to send OTP:', error);
      res.status(500).json({
        success: false,
        error: "Failed to send OTP",
      });
    }
  })
);

/**
 * POST /api/auth/verify-otp
 * Verify OTP and login (with embedded wallet creation for new users)
 */
router.post(
  "/verify-otp",
  [
    body("email").isEmail().withMessage("Valid email is required"),
    body("otp").isLength({ min: 6, max: 6 }).withMessage("OTP must be 6 digits"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { email, otp } = req.body;

    try {
      const result = await emailService.verifyOTP(email, otp);
      
      if (!result) {
        return res.status(400).json({
          success: false,
          error: "Invalid or expired OTP",
        });
      }

      // Create session with token pair
      const deviceInfo = AuthService.extractDeviceInfo(req.headers['user-agent']);
      const ipAddress = req.ip || req.connection.remoteAddress;
      
      const tokenPair = await authService.createSession(
        result.userId,
        deviceInfo,
        ipAddress,
        req.headers['user-agent']
      );

      // Set cookies
      authService.setTokenCookies(res, tokenPair);

      // Get user info
      const db = databaseService.getDatabase();
      const userStmt = db.prepare(`
        SELECT id, email, full_name, created_at, email_verified
        FROM users
        WHERE id = ?
      `);
      const user = userStmt.get(result.userId);

      // Check if user has embedded wallet, create if not
      const walletStmt = db.prepare(`
        SELECT id FROM wallets WHERE user_id = ? AND is_embedded = 1
      `);
      const existingWallet = walletStmt.get(result.userId);

      let autoCreatedWallet = null;
      if (!existingWallet) {
        // Create embedded wallet for new user
        const walletId = uuidv4();
        const walletAddress = `0x${crypto.randomBytes(20).toString('hex')}`;
        const publicKey = `0x${crypto.randomBytes(32).toString('hex')}`;
        
        const insertWalletStmt = db.prepare(`
          INSERT INTO wallets (id, user_id, network, wallet_type, wallet_address, public_key, derivation_path, is_active, is_embedded, metadata, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        `);
        
        insertWalletStmt.run(
          walletId,
          result.userId,
          'ethereum', // Default network
          'embedded',
          walletAddress,
          publicKey,
          "m/44'/60'/0'/0/0",
          1, // is_active
          1, // is_embedded
          JSON.stringify({ autoCreated: true, createdVia: 'otp' })
        );

        autoCreatedWallet = {
          id: walletId,
          network: 'ethereum',
          wallet_address: walletAddress,
          is_embedded: true,
        };

        logger.info(`Created embedded wallet for user ${result.userId} via OTP`);
      }

      res.json({
        success: true,
        message: "Login successful",
        user: {
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          created_at: user.created_at,
          email_verified: user.email_verified,
        },
        auto_created_wallet: autoCreatedWallet,
      });
    } catch (error) {
      logger.error('Failed to verify OTP:', error);
      res.status(500).json({
        success: false,
        error: "Failed to verify OTP",
      });
    }
  })
);

async function exchangeTelegramCode(authData: string, _state: string) {
  // Telegram uses a different authentication mechanism
  // The authData contains the authentication data from the Telegram widget
  try {
    const data = JSON.parse(authData);
    
    // Verify the authentication data
    const checkString = Object.keys(data)
      .filter(key => key !== 'hash')
      .sort()
      .map(key => `${key}=${data[key]}`)
      .join('\n');
    
    const secretKey = crypto
      .createHash('sha256')
      .update(process.env.TELEGRAM_BOT_TOKEN!)
      .digest();
    
    const hash = crypto
      .createHmac('sha256', secretKey)
      .update(checkString)
      .digest('hex');
    
    if (hash !== data.hash) {
      throw new Error('Invalid Telegram authentication');
    }
    
    // Check if auth date is not too old (5 minutes)
    if ((Date.now() / 1000) - data.auth_date > 300) {
      throw new Error('Telegram authentication expired');
    }
    
    return {
      id: data.id.toString(),
      email: data.email || `${data.username || data.id}@telegram.placeholder`,
      name: `${data.first_name} ${data.last_name || ''}`.trim() || data.username,
      avatar_url: data.photo_url || null,
    };
  } catch (error) {
    logger.error('Telegram authentication error:', error);
    throw new Error('Failed to authenticate with Telegram');
  }
}

async function exchangeFarcasterCode(code: string, redirectUri: string) {
  try {
    // Exchange authorization code for access token
    const tokenResponse = await fetch("https://api.warpcast.com/v2/oauth/token", {
      method: "POST",
      headers: { 
        "Content-Type": "application/json",
        "Accept": "application/json"
      },
      body: JSON.stringify({
        client_id: process.env.FARCASTER_CLIENT_ID!,
        client_secret: process.env.FARCASTER_CLIENT_SECRET!,
        code,
        grant_type: "authorization_code",
        redirect_uri: redirectUri,
      }),
    });

    if (!tokenResponse.ok) {
      throw new Error(`Failed to exchange code: ${tokenResponse.statusText}`);
    }

    const tokens = await tokenResponse.json();
    
    // Get user information
    const userResponse = await fetch("https://api.warpcast.com/v2/me", {
      headers: { 
        Authorization: `Bearer ${tokens.access_token}`,
        "Accept": "application/json"
      },
    });

    if (!userResponse.ok) {
      throw new Error(`Failed to get user info: ${userResponse.statusText}`);
    }

    const userInfo = await userResponse.json();
    
    return {
      id: userInfo.fid.toString(),
      email: userInfo.email || `${userInfo.username}@farcaster.placeholder`,
      name: userInfo.display_name || userInfo.username,
      avatar_url: userInfo.pfp_url || null,
    };
  } catch (error) {
    logger.error('Farcaster authentication error:', error);
    throw new Error('Failed to authenticate with Farcaster');
  }
}

export { router as authRoutes };
