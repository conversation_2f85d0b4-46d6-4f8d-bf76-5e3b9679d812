import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import { v4 as uuidv4 } from 'uuid';
import { databaseService } from '../database/database';
import { EncryptionService } from '../services/encryptionService';
import { authMiddleware } from '../middleware/combinedAuth';
import { asyncHandler } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router: import('express').Router = Router();

/**
 * GET /api/api-keys
 * Get user's API keys
 */
router.get('/', authMiddleware, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const db = databaseService.getDatabase();
  
  const stmt = db.prepare(`
    SELECT id, name, permissions, is_active, last_used, created_at, updated_at
    FROM api_keys 
    WHERE user_id = ? 
    ORDER BY created_at DESC
  `);
  
  const apiKeys = stmt.all(userId) as Array<{
    id: string;
    name: string;
    permissions: string;
    is_active: boolean;
    last_used: string | null;
    created_at: string;
    updated_at: string;
  }>;

  res.json({
    success: true,
    data: apiKeys
  });
}));

/**
 * POST /api/api-keys
 * Create a new API key
 */
router.post('/', [
  authMiddleware,
  body('name').isString().trim().isLength({ min: 1, max: 100 }).withMessage('Name is required (1-100 characters)'),
  body('permissions').optional().isString().withMessage('Permissions must be a string')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const userId = req.user!.id;
  const { name, permissions = 'read' } = req.body;
  const db = databaseService.getDatabase();

  // Generate API key
  const apiKey = `tokai_${EncryptionService.generateToken(32)}`;
  const keyHash = EncryptionService.hash(apiKey);

  // Insert API key
  const insertStmt = db.prepare(`
    INSERT INTO api_keys (id, user_id, name, key_hash, permissions)
    VALUES (?, ?, ?, ?, ?)
  `);

  const apiKeyId = uuidv4();
  insertStmt.run(apiKeyId, userId, name, keyHash, permissions);

  logger.info(`API key created: ${name} for user ${userId}`);

  res.status(201).json({
    success: true,
    data: {
      id: apiKeyId,
      name,
      permissions,
      api_key: apiKey, // Only returned once
      created_at: new Date().toISOString()
    },
    message: 'API key created successfully. Store it securely as it won\'t be shown again.'
  });
}));

/**
 * DELETE /api/api-keys/:id
 * Delete an API key
 */
router.delete('/:id', authMiddleware, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const apiKeyId = req.params.id;
  const db = databaseService.getDatabase();

  // Check if API key belongs to user
  const checkStmt = db.prepare('SELECT name FROM api_keys WHERE id = ? AND user_id = ?');
  const apiKey = checkStmt.get(apiKeyId, userId) as { name: string } | undefined;

  if (!apiKey) {
    return res.status(404).json({
      success: false,
      error: 'API key not found'
    });
  }

  // Delete API key
  const deleteStmt = db.prepare('DELETE FROM api_keys WHERE id = ? AND user_id = ?');
  deleteStmt.run(apiKeyId, userId);

  logger.info(`API key deleted: ${apiKey.name} for user ${userId}`);

  res.json({
    success: true,
    message: 'API key deleted successfully'
  });
}));

/**
 * PUT /api/api-keys/:id
 * Update API key (name, permissions, active status)
 */
router.put('/:id', [
  authMiddleware,
  body('name').optional().isString().trim().isLength({ min: 1, max: 100 }).withMessage('Name must be 1-100 characters'),
  body('permissions').optional().isString().withMessage('Permissions must be a string'),
  body('is_active').optional().isBoolean().withMessage('is_active must be boolean')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const userId = req.user!.id;
  const apiKeyId = req.params.id;
  const { name, permissions, is_active } = req.body;
  const db = databaseService.getDatabase();

  // Check if API key belongs to user
  const checkStmt = db.prepare('SELECT name FROM api_keys WHERE id = ? AND user_id = ?');
  const apiKey = checkStmt.get(apiKeyId, userId) as { name: string } | undefined;

  if (!apiKey) {
    return res.status(404).json({
      success: false,
      error: 'API key not found'
    });
  }

  // Build update query
  const updates: string[] = [];
  const values: any[] = [];

  if (name !== undefined) {
    updates.push('name = ?');
    values.push(name);
  }
  if (permissions !== undefined) {
    updates.push('permissions = ?');
    values.push(permissions);
  }
  if (is_active !== undefined) {
    updates.push('is_active = ?');
    values.push(is_active ? 1 : 0);
  }

  if (updates.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'No fields to update'
    });
  }

  updates.push('updated_at = datetime(\'now\')');
  values.push(apiKeyId, userId);

  const updateStmt = db.prepare(`
    UPDATE api_keys 
    SET ${updates.join(', ')}
    WHERE id = ? AND user_id = ?
  `);

  updateStmt.run(...values);

  logger.info(`API key updated: ${apiKey.name} for user ${userId}`);

  res.json({
    success: true,
    message: 'API key updated successfully'
  });
}));

export { router as apiKeyRoutes }; 