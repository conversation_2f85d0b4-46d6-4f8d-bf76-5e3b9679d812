import { Router } from "express";
import { body, validationResult } from "express-validator";
import { databaseService } from "../database/database";
import { authMiddleware } from "../middleware/combinedAuth";
import { asyncHandler } from "../middleware/errorHandler";
import { logger } from "../utils/logger";

const router: import('express').Router = Router();

// Apply authentication middleware to all config routes
router.use(authMiddleware);

/**
 * GET /api/config
 * Get all system configurations
 */
router.get(
  "/",
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    const stmt = db.prepare(
      "SELECT config_key, config_value, config_type, description FROM system_configs ORDER BY config_key",
    );
    const configs = stmt.all() as Array<{
      config_key: string;
      config_value: string;
      config_type: string;
      description: string;
    }>;

    // Transform configs into a more usable format
    const configMap: Record<string, any> = {};
    configs.forEach((config) => {
      let value: any = config.config_value;

      // Parse value based on type
      switch (config.config_type) {
        case "boolean":
          value = config.config_value === "true";
          break;
        case "number":
          value = parseInt(config.config_value);
          break;
        case "json":
          try {
            value = JSON.parse(config.config_value);
          } catch {
            value = config.config_value;
          }
          break;
        case "array":
          value = config.config_value.split(",");
          break;
        default:
          value = config.config_value;
      }

      configMap[config.config_key] = value;
    });

    res.json({
      success: true,
      data: configMap,
    });
  }),
);

/**
 * GET /api/config/:key
 * Get a specific configuration
 */
router.get(
  "/:key",
  asyncHandler(async (req, res) => {
    const { key } = req.params;
    const db = databaseService.getDatabase();

    const stmt = db.prepare(
      "SELECT config_key, config_value, config_type, description FROM system_configs WHERE config_key = ?",
    );
    const config = stmt.get(key) as
      | {
          config_key: string;
          config_value: string;
          config_type: string;
          description: string;
        }
      | undefined;

    if (!config) {
      return res.status(404).json({
        success: false,
        error: "Configuration not found",
      });
    }

    // Parse value based on type
    let value: any = config.config_value;
    switch (config.config_type) {
      case "boolean":
        value = config.config_value === "true";
        break;
      case "number":
        value = parseInt(config.config_value);
        break;
      case "json":
        try {
          value = JSON.parse(config.config_value);
        } catch {
          value = config.config_value;
        }
        break;
      case "array":
        value = config.config_value.split(",");
        break;
      default:
        value = config.config_value;
    }

    res.json({
      success: true,
      data: {
        key: config.config_key,
        value,
        type: config.config_type,
        description: config.description,
      },
    });
  }),
);

/**
 * PUT /api/config/:key
 * Update a configuration
 */
router.put(
  "/:key",
  [
    body("value").notEmpty().withMessage("Value is required"),
    body("type")
      .optional()
      .isIn(["string", "number", "boolean", "json", "array"])
      .withMessage("Invalid type"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { key } = req.params;
    const { value, type } = req.body;
    const db = databaseService.getDatabase();

    // Check if config exists
    const existingStmt = db.prepare(
      "SELECT config_key FROM system_configs WHERE config_key = ?",
    );
    const existing = existingStmt.get(key);

    if (!existing) {
      return res.status(404).json({
        success: false,
        error: "Configuration not found",
      });
    }

    // Serialize value based on type
    let serializedValue: string;
    switch (type || "string") {
      case "boolean":
        serializedValue = value ? "true" : "false";
        break;
      case "number":
        serializedValue = value.toString();
        break;
      case "json":
        serializedValue = JSON.stringify(value);
        break;
      case "array":
        serializedValue = Array.isArray(value) ? value.join(",") : value;
        break;
      default:
        serializedValue = value.toString();
    }

    // Update config
    const updateStmt = db.prepare(`
    UPDATE system_configs 
    SET config_value = ?, config_type = ?, updated_at = datetime('now')
    WHERE config_key = ?
  `);

    updateStmt.run(serializedValue, type || "string", key);

    logger.info(`Configuration updated: ${key} = ${serializedValue}`);

    res.json({
      success: true,
      data: {
        key,
        value:
          type === "boolean"
            ? serializedValue === "true"
            : type === "number"
              ? parseInt(serializedValue)
              : type === "array"
                ? serializedValue.split(",")
                : serializedValue,
        type: type || "string",
      },
      message: "Configuration updated successfully",
    });
  }),
);

/**
 * GET /api/config/wallet/settings
 * Get wallet-specific configuration
 */
router.get(
  "/wallet/settings",
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    const walletConfigs = [
      "default_wallet_type",
      "auto_create_wallet",
      "allowed_networks",
      "max_wallets_per_user",
      "require_backup",
    ];

    const stmt = db.prepare(
      "SELECT config_key, config_value, config_type FROM system_configs WHERE config_key IN (?, ?, ?, ?, ?)",
    );
    const configs = stmt.all(...walletConfigs) as Array<{
      config_key: string;
      config_value: string;
      config_type: string;
    }>;

    const walletSettings: Record<string, any> = {};
    configs.forEach((config) => {
      let value: any = config.config_value;

      switch (config.config_type) {
        case "boolean":
          value = config.config_value === "true";
          break;
        case "number":
          value = parseInt(config.config_value);
          break;
        case "array":
          value = config.config_value.split(",");
          break;
        default:
          value = config.config_value;
      }

      walletSettings[config.config_key] = value;
    });

    res.json({
      success: true,
      data: {
        defaultWalletType: walletSettings.default_wallet_type || "ethereum",
        autoCreateWallet: walletSettings.auto_create_wallet || false,
        allowedNetworks: walletSettings.allowed_networks || [
          "ethereum",
          "solana",
        ],
        maxWalletsPerUser: walletSettings.max_wallets_per_user || 5,
        requireBackup: walletSettings.require_backup || true,
      },
    });
  }),
);

/**
 * PUT /api/config/wallet/settings
 * Update wallet-specific configuration
 */
router.put(
  "/wallet/settings",
  [
    body("defaultWalletType")
      .optional()
      .isIn([
        "ethereum",
        "polygon",
        "bsc",
        "avalanche",
        "arbitrum",
        "optimism",
        "solana",
      ])
      .withMessage("Invalid default wallet type"),
    body("autoCreateWallet")
      .optional()
      .isBoolean()
      .withMessage("Auto create wallet must be boolean"),
    body("allowedNetworks")
      .optional()
      .isArray()
      .withMessage("Allowed networks must be an array"),
    body("maxWalletsPerUser")
      .optional()
      .isInt({ min: 1, max: 20 })
      .withMessage("Max wallets per user must be between 1 and 20"),
    body("requireBackup")
      .optional()
      .isBoolean()
      .withMessage("Require backup must be boolean"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const {
      defaultWalletType,
      autoCreateWallet,
      allowedNetworks,
      maxWalletsPerUser,
      requireBackup,
    } = req.body;
    const db = databaseService.getDatabase();

    const updates = [];

    if (defaultWalletType !== undefined) {
      const stmt = db.prepare(
        "UPDATE system_configs SET config_value = ?, updated_at = datetime('now') WHERE config_key = ?",
      );
      stmt.run(defaultWalletType, "default_wallet_type");
      updates.push("default_wallet_type");
    }

    if (autoCreateWallet !== undefined) {
      const stmt = db.prepare(
        "UPDATE system_configs SET config_value = ?, updated_at = datetime('now') WHERE config_key = ?",
      );
      stmt.run(autoCreateWallet.toString(), "auto_create_wallet");
      updates.push("auto_create_wallet");
    }

    if (allowedNetworks !== undefined) {
      const stmt = db.prepare(
        "UPDATE system_configs SET config_value = ?, updated_at = datetime('now') WHERE config_key = ?",
      );
      stmt.run(allowedNetworks.join(","), "allowed_networks");
      updates.push("allowed_networks");
    }

    if (maxWalletsPerUser !== undefined) {
      const stmt = db.prepare(
        "UPDATE system_configs SET config_value = ?, updated_at = datetime('now') WHERE config_key = ?",
      );
      stmt.run(maxWalletsPerUser.toString(), "max_wallets_per_user");
      updates.push("max_wallets_per_user");
    }

    if (requireBackup !== undefined) {
      const stmt = db.prepare(
        "UPDATE system_configs SET config_value = ?, updated_at = datetime('now') WHERE config_key = ?",
      );
      stmt.run(requireBackup.toString(), "require_backup");
      updates.push("require_backup");
    }

    logger.info(`Wallet settings updated: ${updates.join(", ")}`);

    res.json({
      success: true,
      data: {
        defaultWalletType,
        autoCreateWallet,
        allowedNetworks,
        maxWalletsPerUser,
        requireBackup,
      },
      message: "Wallet settings updated successfully",
    });
  }),
);

export { router as configRoutes };
