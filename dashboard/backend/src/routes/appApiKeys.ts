import { Router } from "express";
import { body, validationResult } from "express-validator";
import { v4 as uuidv4 } from "uuid";
import crypto from "crypto";
import { databaseService } from "../database/database";
import { authMiddleware } from "../middleware/combinedAuth";
import { asyncHandler } from "../middleware/errorHandler";
import { logger } from "../utils/logger";

const router: import('express').Router = Router();

/**
 * GET /api/apps/:appId/api-keys
 * Get all secondary API keys for a specific app
 */
router.get(
  "/:appId/api-keys",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
    const app = appStmt.get(req.params.appId, req.user!.id);

    if (!app) {
      return res.status(404).json({
        success: false,
        error: "App not found",
      });
    }

    const apiKeysStmt = db.prepare(`
      SELECT 
        id,
        name,
        permissions,
        is_active,
        last_used,
        created_at,
        updated_at
      FROM api_keys 
      WHERE app_id = ? AND is_active = 1
      ORDER BY created_at DESC
    `);

    const apiKeys = apiKeysStmt.all(req.params.appId);

    res.json({
      success: true,
      data: apiKeys,
    });
  }),
);

/**
 * POST /api/apps/:appId/api-keys
 * Create a new secondary API key for a specific app
 */
router.post(
  "/:appId/api-keys",
  authMiddleware,
  [
    body('name').isString().trim().isLength({ min: 1, max: 100 }).withMessage('Name is required (1-100 characters)'),
    body('permissions').optional().isIn(['read', 'read,write', 'admin']).withMessage('Invalid permissions'),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, permissions } = req.body;
    const db = databaseService.getDatabase();

    try {
      const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
      const app = appStmt.get(req.params.appId, req.user!.id);

      if (!app) {
        return res.status(404).json({
          success: false,
          error: "App not found",
        });
      }

      const apiKeyId = uuidv4();
      const apiKey = `tk_${crypto.randomBytes(32).toString('hex')}`;
      const apiKeyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT INTO api_keys (
          id,
          app_id,
          name,
          key_hash,
          permissions,
          is_active,
          created_at,
          updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertStmt.run(
        apiKeyId,
        req.params.appId,
        name,
        apiKeyHash,
        permissions || 'read',
        1,
        now,
        now
      );

      logger.info("App API key created successfully", {
        appId: req.params.appId,
        apiKeyId,
        name,
        permissions: permissions || 'read'
      });

      res.status(201).json({
        success: true,
        data: {
          id: apiKeyId,
          name,
          api_key: apiKey, // Return full key only once
          permissions: permissions || 'read',
          is_active: true,
          created_at: now
        },
        message: "API key created successfully. Save it - it won't be shown again!"
      });

    } catch (error) {
      logger.error("Error creating app API key:", error);
      throw error;
    }
  }),
);

/**
 * GET /api/apps/:appId/api-keys/:keyId
 * Get a specific API key for an app (without the actual key)
 */
router.get(
  "/:appId/api-keys/:keyId",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
    const app = appStmt.get(req.params.appId, req.user!.id);

    if (!app) {
      return res.status(404).json({
        success: false,
        error: "App not found",
      });
    }

    const apiKeyStmt = db.prepare(`
      SELECT 
        id,
        name,
        permissions,
        is_active,
        last_used,
        created_at,
        updated_at
      FROM api_keys 
      WHERE id = ? AND app_id = ?
    `);

    const apiKey = apiKeyStmt.get(req.params.keyId, req.params.appId);

    if (!apiKey) {
      return res.status(404).json({
        success: false,
        error: "API key not found",
      });
    }

    res.json({
      success: true,
      data: apiKey,
    });
  }),
);

/**
 * PUT /api/apps/:appId/api-keys/:keyId
 * Update an API key for an app
 */
router.put(
  "/:appId/api-keys/:keyId",
  authMiddleware,
  [
    body('name').optional().isString().trim().isLength({ min: 1, max: 100 }),
    body('permissions').optional().isIn(['read', 'read,write', 'admin']),
    body('is_active').optional().isBoolean(),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, permissions, is_active } = req.body;
    const db = databaseService.getDatabase();

    try {
      // Verify app belongs to user
      const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
      const app = appStmt.get(req.params.appId, req.user!.id);

      if (!app) {
        return res.status(404).json({
          success: false,
          error: "App not found",
        });
      }

      // Check if API key exists and belongs to app
      const apiKeyStmt = db.prepare("SELECT id FROM api_keys WHERE id = ? AND app_id = ?");
      const apiKey = apiKeyStmt.get(req.params.keyId, req.params.appId);

      if (!apiKey) {
        return res.status(404).json({
          success: false,
          error: "API key not found",
        });
      }

      // Update API key
      const updateStmt = db.prepare(`
        UPDATE api_keys SET 
          name = COALESCE(?, name),
          permissions = COALESCE(?, permissions),
          is_active = COALESCE(?, is_active),
          updated_at = ?
        WHERE id = ? AND app_id = ?
      `);

      updateStmt.run(
        name || null,
        permissions || null,
        is_active !== undefined ? is_active : null,
        new Date().toISOString(),
        req.params.keyId,
        req.params.appId
      );

      logger.info("App API key updated successfully", {
        appId: req.params.appId,
        apiKeyId: req.params.keyId
      });

      res.json({
        success: true,
        message: "API key updated successfully",
      });

    } catch (error) {
      logger.error("Error updating app API key:", error);
      throw error;
    }
  }),
);

/**
 * DELETE /api/apps/:appId/api-keys/:keyId
 * Delete an API key for an app
 */
router.delete(
  "/:appId/api-keys/:keyId",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    try {
      // Verify app belongs to user
      const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
      const app = appStmt.get(req.params.appId, req.user!.id);

      if (!app) {
        return res.status(404).json({
          success: false,
          error: "App not found",
        });
      }

      // Check if API key exists and belongs to app
      const apiKeyStmt = db.prepare("SELECT id FROM api_keys WHERE id = ? AND app_id = ?");
      const apiKey = apiKeyStmt.get(req.params.keyId, req.params.appId);

      if (!apiKey) {
        return res.status(404).json({
          success: false,
          error: "API key not found",
        });
      }

      // Soft delete the API key
      const updateStmt = db.prepare(`
        UPDATE api_keys SET 
          is_active = 0,
          updated_at = ?
        WHERE id = ? AND app_id = ?
      `);

      updateStmt.run(
        new Date().toISOString(),
        req.params.keyId,
        req.params.appId
      );

      logger.info("App API key deleted successfully", {
        appId: req.params.appId,
        apiKeyId: req.params.keyId
      });

      res.json({
        success: true,
        message: "API key deleted successfully",
      });

    } catch (error) {
      logger.error("Error deleting app API key:", error);
      throw error;
    }
  }),
);

export { router as appApiKeysRoutes }; 