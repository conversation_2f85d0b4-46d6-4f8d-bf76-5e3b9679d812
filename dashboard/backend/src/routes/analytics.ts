import { Router } from "express";
import { authMiddleware } from "../middleware/combinedAuth";
import { async<PERSON>and<PERSON> } from "../middleware/errorHandler";
import { analyticsService } from "../services/analyticsService";

const router: import('express').Router = Router();

/**
 * GET /api/analytics/dashboard
 * Get comprehensive dashboard metrics
 */
router.get(
  "/dashboard",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const metrics = await analyticsService.getDashboardMetrics();
    
    res.json({
      success: true,
      data: metrics,
    });
  }),
);

/**
 * GET /api/analytics/users
 * Get detailed user analytics
 */
router.get(
  "/users",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const userAnalytics = await analyticsService.getUserAnalytics();
    
    res.json({
      success: true,
      data: userAnalytics,
    });
  }),
);

/**
 * GET /api/analytics/wallets
 * Get detailed wallet analytics
 */
router.get(
  "/wallets",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const walletAnalytics = await analyticsService.getWalletAnalytics();
    
    res.json({
      success: true,
      data: walletAnalytics,
    });
  }),
);

/**
 * GET /api/analytics/security
 * Get security and MFA analytics
 */
router.get(
  "/security",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const securityAnalytics = await analyticsService.getSecurityAnalytics();
    
    res.json({
      success: true,
      data: securityAnalytics,
    });
  }),
);

/**
 * GET /api/analytics/apps
 * Get app usage analytics
 */
router.get(
  "/apps",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const appAnalytics = await analyticsService.getAppAnalytics();
    
    res.json({
      success: true,
      data: appAnalytics,
    });
  }),
);

/**
 * GET /api/analytics/realtime
 * Get real-time metrics for dashboard
 */
router.get(
  "/realtime",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const realtimeMetrics = await analyticsService.getRealtimeMetrics();
    
    res.json({
      success: true,
      data: realtimeMetrics,
    });
  }),
);

/**
 * GET /api/analytics/activity/:userId
 * Get user activity timeline
 */
router.get(
  "/activity/:userId",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const { userId } = req.params;
    const limit = parseInt(req.query.limit as string) || 50;
    
    const activity = await analyticsService.getUserActivity(userId, limit);
    
    res.json({
      success: true,
      data: {
        userId,
        activity,
      },
    });
  }),
);

/**
 * POST /api/analytics/track
 * Track user activity (for API clients)
 */
router.post(
  "/track",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const { activity, metadata } = req.body;
    const userId = req.user!.id;
    
    if (!activity) {
      return res.status(400).json({
        success: false,
        error: "Activity is required",
      });
    }
    
    await analyticsService.trackUserActivity(userId, activity, metadata);
    
    res.json({
      success: true,
      data: {
        message: "Activity tracked successfully",
      },
    });
  }),
);

/**
 * GET /api/analytics/export
 * Export analytics data (CSV format)
 */
router.get(
  "/export",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const { type, startDate, endDate } = req.query;
    
    if (!type) {
      return res.status(400).json({
        success: false,
        error: "Export type is required (users, wallets, activity)",
      });
    }
    
    // This would generate CSV data based on the type
    // For now, return a simple response
    res.json({
      success: true,
      data: {
        message: "Export functionality coming soon",
        type,
        startDate,
        endDate,
      },
    });
  }),
);

/**
 * GET /api/analytics/system-health
 * Get system health and performance metrics
 */
router.get(
  "/system-health",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const systemHealth = await analyticsService.getSystemHealth();
    
    res.json({
      success: true,
      data: systemHealth,
    });
  }),
);

export { router as analyticsRoutes };