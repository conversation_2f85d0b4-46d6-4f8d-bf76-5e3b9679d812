import { Router, Response } from 'express';
import { sessionKeysService, CreateSessionKeyRequest } from '../services/sessionKeysService';
import { authMiddleware, AuthRequest } from '../middleware/combinedAuth';
import { sessionKeyAuthMiddleware, hybridAuthMiddleware, SessionKeyRequest } from '../middleware/sessionKeys';
import { logger } from '../utils/logger';

const router: import('express').Router = Router();

/**
 * Create a new session key
 * POST /api/session-keys
 */
router.post('/', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const { keyName, permissions, scope, expiryDuration, metadata } = req.body;

    // Validate required fields
    if (!keyName || !permissions || !Array.isArray(permissions)) {
      return res.status(400).json({
        success: false,
        error: 'Key name and permissions array are required'
      });
    }

    if (permissions.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'At least one permission is required'
      });
    }

    // Create session key request
    const createRequest: CreateSessionKeyRequest = {
      sessionId: req.user!.sessionId,
      userId: req.user!.id,
      keyName,
      permissions,
      scope,
      expiryDuration,
      metadata
    };

    const result = await sessionKeysService.createSessionKey(createRequest);

    // Don't return the raw key in the response for security
    const responseSessionKey = { ...result.sessionKey };
    delete (responseSessionKey as any).keyHash;

    res.json({
      success: true,
      data: {
        sessionKey: responseSessionKey,
        rawKey: result.rawKey, // Only returned once
        warning: 'Store this key securely. It will not be shown again.'
      }
    });

  } catch (error: any) {
    logger.error('Create session key error:', error?.message || String(error));
    res.status(500).json({
      success: false,
      error: error?.message || 'Failed to create session key'
    });
  }
});

/**
 * List user's session keys
 * GET /api/session-keys
 */
router.get('/', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const includeRevoked = req.query.include_revoked === 'true';
    const sessionKeys = await sessionKeysService.getUserSessionKeys(req.user!.id, includeRevoked);

    // Remove sensitive information from response
    const sanitizedKeys = sessionKeys.map(key => {
      const sanitized = { ...key };
      delete (sanitized as any).keyHash; // Never expose key hash
      return sanitized;
    });

    res.json({
      success: true,
      data: {
        sessionKeys: sanitizedKeys,
        total: sanitizedKeys.length
      }
    });

  } catch (error: any) {
    logger.error('List session keys error:', error?.message || String(error));
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve session keys'
    });
  }
});

/**
 * Get session key details by ID
 * GET /api/session-keys/:id
 */
router.get('/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const sessionKeyId = req.params.id;
    const sessionKeys = await sessionKeysService.getUserSessionKeys(req.user!.id, true);
    const sessionKey = sessionKeys.find(key => key.id === sessionKeyId);

    if (!sessionKey) {
      return res.status(404).json({
        success: false,
        error: 'Session key not found'
      });
    }

    // Remove sensitive information
    const sanitized = { ...sessionKey };
    delete (sanitized as any).keyHash;

    res.json({
      success: true,
      data: { sessionKey: sanitized }
    });

  } catch (error: any) {
    logger.error('Get session key error:', error?.message || String(error));
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve session key'
    });
  }
});

/**
 * Revoke a session key
 * DELETE /api/session-keys/:id
 */
router.delete('/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const sessionKeyId = req.params.id;
    
    // First verify the session key belongs to the user
    const userSessionKeys = await sessionKeysService.getUserSessionKeys(req.user!.id, false);
    const sessionKey = userSessionKeys.find(key => key.id === sessionKeyId);

    if (!sessionKey) {
      return res.status(404).json({
        success: false,
        error: 'Session key not found'
      });
    }

    const revoked = await sessionKeysService.revokeSessionKey(sessionKeyId, req.user!.id);

    if (revoked) {
      res.json({
        success: true,
        message: 'Session key revoked successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Session key could not be revoked'
      });
    }

  } catch (error: any) {
    logger.error('Revoke session key error:', error?.message || String(error));
    res.status(500).json({
      success: false,
      error: 'Failed to revoke session key'
    });
  }
});

/**
 * Revoke all session keys for the current user
 * DELETE /api/session-keys
 */
router.delete('/', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const revokedCount = await sessionKeysService.revokeAllUserSessionKeys(req.user!.id, req.user!.id);

    res.json({
      success: true,
      message: `${revokedCount} session keys revoked successfully`,
      data: { revokedCount }
    });

  } catch (error: any) {
    logger.error('Revoke all session keys error:', error?.message || String(error));
    res.status(500).json({
      success: false,
      error: 'Failed to revoke session keys'
    });
  }
});

/**
 * Test session key permissions
 * POST /api/session-keys/test-permission
 */
router.post('/test-permission', sessionKeyAuthMiddleware, async (req: SessionKeyRequest, res: Response) => {
  try {
    const { permission, resource } = req.body;

    if (!permission) {
      return res.status(400).json({
        success: false,
        error: 'Permission is required'
      });
    }

    const permissionCheck = sessionKeysService.checkPermission(
      req.sessionKey!,
      permission,
      resource
    );

    res.json({
      success: true,
      data: {
        hasPermission: permissionCheck.hasPermission,
        reason: permissionCheck.reason,
        sessionKey: {
          id: req.sessionKey!.id,
          keyName: req.sessionKey!.keyName,
          permissions: req.sessionKey!.permissions,
          scope: req.sessionKey!.scope
        }
      }
    });

  } catch (error: any) {
    logger.error('Test permission error:', error?.message || String(error));
    res.status(500).json({
      success: false,
      error: 'Failed to test permission'
    });
  }
});

/**
 * Get available permissions
 * GET /api/session-keys/permissions
 */
router.get('/permissions/available', hybridAuthMiddleware, async (req: SessionKeyRequest, res: Response) => {
  try {
    const category = req.query.category as string;
    const permissions = await sessionKeysService.getAvailablePermissions(category);

    res.json({
      success: true,
      data: { permissions }
    });

  } catch (error: any) {
    logger.error('Get permissions error:', error?.message || String(error));
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve permissions'
    });
  }
});

/**
 * Get session key usage logs
 * GET /api/session-keys/:id/usage
 */
router.get('/:id/usage', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const sessionKeyId = req.params.id;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    // First verify the session key belongs to the user
    const userSessionKeys = await sessionKeysService.getUserSessionKeys(req.user!.id, true);
    const sessionKey = userSessionKeys.find(key => key.id === sessionKeyId);

    if (!sessionKey) {
      return res.status(404).json({
        success: false,
        error: 'Session key not found'
      });
    }

    // Get usage logs from database (placeholder until implemented)
    const usageLogs: Array<{ id: number; action: string; resource?: string; created_at: string }> = [];

    res.json({
      success: true,
      data: {
        usage: usageLogs,
        total: usageLogs.length,
        limit,
        offset,
      },
    });
    return;
  } catch (error: any) {
    logger.error('Get usage logs error:', error?.message || String(error));
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve usage logs'
    });
  }
});

export default router;