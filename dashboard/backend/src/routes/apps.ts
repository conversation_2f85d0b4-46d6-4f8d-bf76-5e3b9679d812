import { Router } from "express";
import { body, validationResult } from "express-validator";
import { v4 as uuidv4 } from "uuid";
import crypto from "crypto";
import { databaseService } from "../database/database";
import { authMiddleware } from "../middleware/combinedAuth";
import { asyncHandler } from "../middleware/errorHandler";
import { logger } from "../utils/logger";

const router: import('express').Router = Router();

/**
 * GET /api/apps
 * Get all apps for the authenticated user
 */
router.get(
  "/",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    const appsStmt = db.prepare(`
      SELECT 
        id,
        name,
        description,
        api_key_preview,
        domain,
        status,
        created_at,
        updated_at
      FROM apps 
      WHERE user_id = ? 
      ORDER BY created_at DESC
    `);

    const apps = appsStmt.all(req.user!.id);

    res.json({
      success: true,
      data: apps,
    });
  }),
);

/**
 * POST /api/apps
 * Create a new app
 */
router.post(
  "/",
  authMiddleware,
  [
    body('name').isString().trim().isLength({ min: 1, max: 100 }).withMessage('Name is required (1-100 characters)'),
    body('description').optional().isString().trim().isLength({ max: 500 }).withMessage('Description max 500 characters'),
    body('domain').optional().isString().trim().isLength({ max: 255 }).withMessage('Domain max 255 characters'),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, description, domain } = req.body;
    const db = databaseService.getDatabase();

    try {
      // Generate unique app ID and API key
      const appId = uuidv4();
      const apiKey = `tk_${crypto.randomBytes(32).toString('hex')}`;
      const apiKeyHash = crypto.createHash('sha256').update(apiKey).digest('hex');
      const apiKeyPreview = `${apiKey.substring(0, 8)}...${apiKey.slice(-8)}`;

      const now = new Date().toISOString();

      // Insert new app
      const insertStmt = db.prepare(`
        INSERT INTO apps (
          id,
          user_id,
          name,
          description,
          api_key_hash,
          api_key_preview,
          domain,
          status,
          created_at,
          updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertStmt.run(
        appId,
        req.user!.id,
        name,
        description || null,
        apiKeyHash,
        apiKeyPreview,
        domain || null,
        'active',
        now,
        now
      );

      const defaultSettingsStmt = db.prepare(`
        INSERT INTO app_settings (
          id,
          app_id,
          google_client_id,
          google_client_secret,
          default_wallet_network,
          auto_create_wallets,
          email_otp_enabled,
          google_auth_enabled,
          created_at,
          updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const uniqueId = uuidv4();

      defaultSettingsStmt.run(
        uniqueId,
        appId,
        null, // No Google credentials by default
        null,
        'ethereum',
        1, // auto_create_wallets
        1, // email_otp_enabled
        1, // google_auth_enabled
        now,
        now
      );

      // Seed default allowed origins for the new app
      const insertOrigin = db.prepare(`
        INSERT OR IGNORE INTO app_allowed_origins (id, app_id, origin)
        VALUES (?, ?, ?)
      `);
      insertOrigin.run(uuidv4(), appId, 'http://localhost:3000');
      insertOrigin.run(uuidv4(), appId, 'http://localhost:3002');
      
      logger.info("App created successfully", {
        appId,
        userId: req.user!.id,
        appName: name
      });

      res.status(201).json({
        success: true,
        data: {
          id: appId,
          name,
          description,
          apiKey, // Return the full API key only once
          apiKeyPreview,
          domain,
          status: 'active',
          createdAt: now
        },
        message: "App created successfully. Save the API key - it won't be shown again!"
      });

    } catch (error) {
      logger.error("Error creating app:", error);
      throw error;
    }
  }),
);

/**
 * GET /api/apps/:id
 * Get a specific app
 */
router.get(
  "/:id",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    const appStmt = db.prepare(`
      SELECT 
        id,
        name,
        description,
        api_key_preview,
        domain,
        status,
        created_at,
        updated_at
      FROM apps 
      WHERE id = ? AND user_id = ?
    `);

    const app = appStmt.get(req.params.id, req.user!.id);

    if (!app) {
      return res.status(404).json({
        success: false,
        error: "App not found",
      });
    }

    res.json({
      success: true,
      data: app,
    });
  }),
);

/**
 * PUT /api/apps/:id
 * Update an app
 */
router.put(
  "/:id",
  authMiddleware,
  [
    body('name').optional().isString().trim().isLength({ min: 1, max: 100 }),
    body('description').optional().isString().trim().isLength({ max: 500 }),
    body('domain').optional().isString().trim().isLength({ max: 255 }),
    body('status').optional().isIn(['active', 'inactive']),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, description, domain, status } = req.body;
    const db = databaseService.getDatabase();

    try {
      const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
      const app = appStmt.get(req.params.id, req.user!.id);

      if (!app) {
        return res.status(404).json({
          success: false,
          error: "App not found",
        });
      }

      const updateStmt = db.prepare(`
        UPDATE apps SET 
          name = COALESCE(?, name),
          description = COALESCE(?, description),
          domain = COALESCE(?, domain),
          status = COALESCE(?, status),
          updated_at = ?
        WHERE id = ? AND user_id = ?
      `);

      updateStmt.run(
        name || null,
        description || null,
        domain || null,
        status || null,
        new Date().toISOString(),
        req.params.id,
        req.user!.id
      );

      logger.info("App updated successfully", {
        appId: req.params.id,
        userId: req.user!.id
      });

      res.json({
        success: true,
        message: "App updated successfully",
      });

    } catch (error) {
      logger.error("Error updating app:", error);
      throw error;
    }
  }),
);

/**
 * DELETE /api/apps/:id
 * Delete an app
 */
router.delete(
  "/:id",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    try {
      const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
      const app = appStmt.get(req.params.id, req.user!.id);

      if (!app) {
        return res.status(404).json({
          success: false,
          error: "App not found",
        });
      }

      const deleteStmt = db.prepare("DELETE FROM apps WHERE id = ? AND user_id = ?");
      deleteStmt.run(req.params.id, req.user!.id);

      logger.info("App deleted successfully", {
        appId: req.params.id,
        userId: req.user!.id
      });

      res.json({
        success: true,
        message: "App deleted successfully",
      });

    } catch (error) {
      logger.error("Error deleting app:", error);
      throw error;
    }
  }),
);

/**
 * POST /api/apps/:id/regenerate-key
 * Regenerate API key for an app
 */
router.post(
  "/:id/regenerate-key",
  authMiddleware,
  asyncHandler(async (req, res) => {
    const db = databaseService.getDatabase();

    try {
      const appStmt = db.prepare("SELECT id FROM apps WHERE id = ? AND user_id = ?");
      const app = appStmt.get(req.params.id, req.user!.id);

      if (!app) {
        return res.status(404).json({
          success: false,
          error: "App not found",
        });
      }

      const newApiKey = `tk_${crypto.randomBytes(32).toString('hex')}`;
      const newApiKeyHash = crypto.createHash('sha256').update(newApiKey).digest('hex');
      const newApiKeyPreview = `${newApiKey.substring(0, 8)}...${newApiKey.substring(-8)}`;
      const updateStmt = db.prepare(`
        UPDATE apps SET 
          api_key_hash = ?,
          api_key_preview = ?,
          updated_at = ?
        WHERE id = ? AND user_id = ?
      `);

      updateStmt.run(
        newApiKeyHash,
        newApiKeyPreview,
        new Date().toISOString(),
        req.params.id,
        req.user!.id
      );

      logger.info("App API key regenerated", {
        appId: req.params.id,
        userId: req.user!.id
      });

      res.json({
        success: true,
        data: {
          apiKey: newApiKey, // Return new key only once
          apiKeyPreview: newApiKeyPreview
        },
        message: "API key regenerated successfully. Save it - it won't be shown again!"
      });

    } catch (error) {
      logger.error("Error regenerating API key:", error);
      throw error;
    }
  }),
);

export { router as appsRoutes }; 