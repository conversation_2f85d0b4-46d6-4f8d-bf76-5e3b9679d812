import { Request, Response, NextFunction } from 'express';
import { authService } from '../services/authService';

export interface AuthRequest extends Request {
  user?: {
    id: string;
    sessionId: string;
  };
}

export const authMiddleware = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // 1) Try cookies first (dashboard/web clients)
    try {
      const { accessToken } = authService.getTokensFromCookies(req);
      if (accessToken) {
        const payload = authService.verifyAccessToken(accessToken);
        await authService.updateSessionLastUsed(payload.sessionId);
        req.user = { id: payload.userId, sessionId: payload.sessionId };
        next();
        return;
      }
    } catch (_) {
      // fall through to header flow
    }

    // 2) Fallback: Authorization header (programmatic clients)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const accessToken = authHeader.substring(7);
      try {
        const payload = authService.verifyAccessToken(accessToken);
        await authService.updateSessionLastUsed(payload.sessionId);
        req.user = { id: payload.userId, sessionId: payload.sessionId };
        next();
        return;
      } catch (error: any) {
        res.status(401).json({
          success: false,
          error: 'Invalid or expired access token',
          code: 'INVALID_TOKEN'
        });
        return;
      }
    }

    // 3) No token found
    res.status(401).json({
      success: false,
      error: 'Access token required',
      code: 'MISSING_TOKEN'
    });
  } catch (error: any) {
    console.error('Auth middleware error:', (error && error.message) ? error.message : String(error));
    res.status(500).json({
      success: false,
      error: 'Authentication error'
    });
  }
};

export const optionalAuthMiddleware = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Try cookies first
    try {
      const { accessToken } = authService.getTokensFromCookies(req);
      if (accessToken) {
        const payload = authService.verifyAccessToken(accessToken);
        await authService.updateSessionLastUsed(payload.sessionId);
        req.user = { id: payload.userId, sessionId: payload.sessionId };
        next();
        return;
      }
    } catch (_) {
      // ignore and fall back to header
    }

    // Fallback to Authorization header
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const accessToken = authHeader.substring(7);
      try {
        const payload = authService.verifyAccessToken(accessToken);
        await authService.updateSessionLastUsed(payload.sessionId);
        req.user = { id: payload.userId, sessionId: payload.sessionId };
      } catch (error: any) {
        console.warn('Optional auth failed:', (error && error.message) ? error.message : String(error));
      }
    }

    next();
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    next(); // Continue even if there's an error
  }
};
