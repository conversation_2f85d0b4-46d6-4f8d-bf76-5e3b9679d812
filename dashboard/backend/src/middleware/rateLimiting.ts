import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';

// Custom key generator for user-specific rate limiting
const userKeyGenerator = (req: Request): string => {
  // Use user ID if authenticated, otherwise use IP
  const userId = (req as any).user?.id;
  return userId || req.ip || 'unknown';
};

// Custom key generator for IP-based rate limiting
const ipKeyGenerator = (req: Request): string => {
  return req.ip || 'unknown';
};

// Custom error handler for rate limit responses
const rateLimitHandler = (req: Request, res: Response): void => {
  res.status(429).json({
    success: false,
    error: 'Too many requests',
    message: 'You have exceeded the rate limit. Please try again later.',
    // retryAfter intentionally omitted to avoid typing issues
  });
};

/**
 * General API rate limiting - applies to all endpoints
 */
export const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Max 1000 requests per window per IP
  message: 'Too many requests from this IP, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: ipKeyGenerator,
  handler: rateLimitHandler,
});

/**
 * Strict rate limiting for authentication endpoints
 */
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Max 10 auth attempts per window per IP
  message: 'Too many authentication attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: ipKeyGenerator,
  handler: rateLimitHandler,
  skipSuccessfulRequests: true, // Don't count successful requests
});

/**
 * Very strict rate limiting for password reset and sensitive operations
 */
export const sensitiveRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Max 3 attempts per hour per IP
  message: 'Too many sensitive operation attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: ipKeyGenerator,
  handler: rateLimitHandler,
});

/**
 * Rate limiting for MFA operations
 */
export const mfaRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // Max 20 MFA attempts per window per user
  message: 'Too many MFA attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: userKeyGenerator,
  handler: rateLimitHandler,
});

/**
 * Rate limiting for wallet operations
 */
export const walletRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 50, // Max 50 wallet operations per window per user
  message: 'Too many wallet operations, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: userKeyGenerator,
  handler: rateLimitHandler,
});

/**
 * Rate limiting for analytics endpoints (less strict)
 */
export const analyticsRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // Max 100 analytics requests per minute per user
  message: 'Too many analytics requests, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: userKeyGenerator,
  handler: rateLimitHandler,
});

/**
 * Rate limiting for file uploads or heavy operations
 */
export const uploadRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Max 10 uploads per hour per user
  message: 'Too many upload attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: userKeyGenerator,
  handler: rateLimitHandler,
});

/**
 * Progressive rate limiting that gets stricter with repeated violations
 */
export const progressiveRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: (req: Request): number => {
    // Check if IP has been rate limited recently (simplified logic)
    const ip = req.ip || 'unknown';
    // In a real implementation, you'd check a database or cache
    // For now, use a simple in-memory store
    if (!progressiveStore[ip]) {
      progressiveStore[ip] = { violations: 0, lastViolation: 0 };
    }
    
    const store = progressiveStore[ip];
    const now = Date.now();
    
    // Reset violations if last violation was more than 1 hour ago
    if (now - store.lastViolation > 60 * 60 * 1000) {
      store.violations = 0;
    }
    
    // Decrease limits based on violations
    const baseLimit = 100;
    const penaltyMultiplier = Math.pow(0.5, store.violations);
    return Math.max(10, Math.floor(baseLimit * penaltyMultiplier));
  },
  keyGenerator: ipKeyGenerator,
  handler: (req: Request, res: Response) => {
    const ip = req.ip || 'unknown';
    if (!progressiveStore[ip]) {
      progressiveStore[ip] = { violations: 0, lastViolation: 0 };
    }
    progressiveStore[ip].violations++;
    progressiveStore[ip].lastViolation = Date.now();
    
    rateLimitHandler(req, res);
  },
});

// Simple in-memory store for progressive rate limiting
// In production, use Redis or a proper database
const progressiveStore: { [ip: string]: { violations: number; lastViolation: number } } = {};

/**
 * Clean up old entries from progressive store (should be called periodically)
 */
export const cleanupProgressiveStore = (): void => {
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours
  
  Object.keys(progressiveStore).forEach(ip => {
    if (now - progressiveStore[ip].lastViolation > maxAge) {
      delete progressiveStore[ip];
    }
  });
};

/**
 * Rate limiting for registration endpoint (prevents spam accounts)
 */
export const registrationRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Max 5 registrations per hour per IP
  message: 'Too many registration attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: ipKeyGenerator,
  handler: rateLimitHandler,
});

/**
 * Rate limiting for password reset requests
 */
export const passwordResetRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Max 3 password reset requests per hour per IP
  message: 'Too many password reset attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: ipKeyGenerator,
  handler: rateLimitHandler,
});

/**
 * Rate limiting for OAuth operations
 */
export const oauthRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // Max 20 OAuth attempts per window per IP
  message: 'Too many OAuth attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: ipKeyGenerator,
  handler: rateLimitHandler,
});

/**
 * Rate limiting for API key operations
 */
export const apiKeyRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Max 10 API key operations per hour per user
  message: 'Too many API key operations, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: userKeyGenerator,
  handler: rateLimitHandler,
});