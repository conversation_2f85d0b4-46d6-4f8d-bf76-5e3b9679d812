import { Request, Response, NextFunction } from 'express';
import { authService } from '../services/authService';

export interface AuthRequest extends Request {
  user?: {
    id: string;
    sessionId: string;
    permissions?: string;
  };
}

export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { accessToken } = authService.getTokensFromCookies(req);
    
    if (!accessToken) {
      return res.status(401).json({
        success: false,
        error: "No access token provided",
      });
    }

    const payload = authService.verifyAccessToken(accessToken);
    
    await authService.updateSessionLastUsed(payload.sessionId);
    
    (req as AuthRequest).user = {
      id: payload.userId,
      sessionId: payload.sessionId,
    };
    
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: "Invalid or expired token",
    });
  }
};

export const requirePermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    if (req.user.permissions) {
      const permissions = req.user.permissions.split(',');
      
      if (!permissions.includes(permission) && !permissions.includes('admin')) {
        return res.status(403).json({
          success: false,
          error: `Permission '${permission}' required`
        });
      }
    }

    next();
  };
}; 