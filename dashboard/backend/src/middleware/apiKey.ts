import { Request, Response, NextFunction } from 'express';
import { databaseService } from '../database/database';
import { EncryptionService } from '../services/encryptionService';
import { logger } from '../utils/logger';
import { CustomError } from './errorHandler';

export interface ApiKeyUser {
  id: string;
  email: string;
  full_name?: string;
  permissions: string;
}

declare global {
  namespace Express {
    interface Request {
      apiKeyUser?: ApiKeyUser;
    }
  }
}

export const apiKeyMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const apiKey = req.headers['x-api-key'] as string;
    
    if (!apiKey) {
      throw new CustomError('API key required', 401);
    }

    const db = databaseService.getDatabase();
    
    // Hash the provided API key
    const keyHash = EncryptionService.hash(apiKey);
    
    // Find the API key in database
    const apiKeyStmt = db.prepare(`
      SELECT ak.id, ak.user_id, ak.name, ak.permissions, ak.is_active,
             u.email, u.full_name
      FROM api_keys ak
      JOIN users u ON ak.user_id = u.id
      WHERE ak.key_hash = ? AND ak.is_active = 1
    `);
    
    const apiKeyRecord = apiKeyStmt.get(keyHash) as {
      id: string;
      user_id: string;
      name: string;
      permissions: string;
      is_active: boolean;
      email: string;
      full_name?: string;
    } | undefined;

    if (!apiKeyRecord) {
      throw new CustomError('Invalid API key', 401);
    }

    const updateStmt = db.prepare('UPDATE api_keys SET last_used = datetime(\'now\') WHERE id = ?');
    updateStmt.run(apiKeyRecord.id);

    // Attach user info to request
    req.apiKeyUser = {
      id: apiKeyRecord.user_id,
      email: apiKeyRecord.email,
      full_name: apiKeyRecord.full_name,
      permissions: apiKeyRecord.permissions
    };

    logger.info(`API key used: ${apiKeyRecord.name} by ${apiKeyRecord.email}`);

    next();
  } catch (error) {
    if (error instanceof CustomError) {
      return res.status(error.statusCode).json({
        success: false,
        error: error.message
      });
    }

    logger.error('API key authentication error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
};

export const requirePermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.apiKeyUser) {
      return res.status(401).json({
        success: false,
        error: 'API key authentication required'
      });
    }

    const permissions = req.apiKeyUser.permissions.split(',');
    
    if (!permissions.includes(permission) && !permissions.includes('admin')) {
      return res.status(403).json({
        success: false,
        error: `Permission '${permission}' required`
      });
    }

    next();
  };
}; 