import { Response, NextFunction } from 'express';
import { sessionKeysService, SessionKey } from '../services/sessionKeysService';
import { AuthRequest } from './auth';

export interface SessionKeyRequest extends AuthRequest {
  sessionKey?: SessionKey;
  hasPermission?: (permission: string, resource?: string) => boolean;
}

/**
 * Middleware to validate session keys for authentication
 * This can be used as an alternative to regular JWT authentication
 */
export const sessionKeyAuthMiddleware = async (
  req: SessionKeyRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('SessionKey ')) {
      res.status(401).json({
        success: false,
        error: 'Session key required',
        code: 'MISSING_SESSION_KEY'
      });
      return;
    }

    const sessionKey = authHeader.substring(11); // Remove 'SessionKey ' prefix

    try {
      const validationResult = await sessionKeysService.validateSessionKey(sessionKey);
      
      if (!validationResult.isValid || !validationResult.sessionKey) {
        res.status(401).json({
          success: false,
          error: validationResult.error || 'Invalid session key',
          code: 'INVALID_SESSION_KEY'
        });
        return;
      }
      
      // Log usage
      await sessionKeysService.logUsage(
        validationResult.sessionKey.id,
        validationResult.sessionKey.userId,
        `${req.method} ${req.path}`,
        req.path,
        req.ip,
        req.get('User-Agent')
      );
      
      // Attach session key and user info to request
      req.sessionKey = validationResult.sessionKey;
      req.user = {
        id: validationResult.sessionKey.userId,
        sessionId: validationResult.sessionKey.sessionId
      };

      // Add helper function for permission checking
      req.hasPermission = (permission: string, resource?: string) => {
        if (!req.sessionKey) return false;
        const result = sessionKeysService.checkPermission(req.sessionKey, permission, resource);
        return result.hasPermission;
      };

      next();
    } catch (error) {
      console.error('Session key validation error:', error instanceof Error ? error.message : String(error));
      res.status(401).json({
        success: false,
        error: 'Session key validation failed',
        code: 'SESSION_KEY_ERROR'
      });
      return;
    }
  } catch (error) {
    console.error('Session key auth middleware error:', error instanceof Error ? error.message : String(error));
    res.status(500).json({
      success: false,
      error: 'Authentication error'
    });
  }
};

/**
 * Middleware to check if the current session key has a specific permission
 * Must be used after sessionKeyAuthMiddleware
 */
export const requirePermission = (permission: string, resource?: string) => {
  return async (req: SessionKeyRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.sessionKey) {
        res.status(401).json({
          success: false,
          error: 'Session key authentication required',
          code: 'NO_SESSION_KEY'
        });
        return;
      }

      const permissionCheck = sessionKeysService.checkPermission(
        req.sessionKey, 
        permission, 
        resource || req.path
      );

      if (!permissionCheck.hasPermission) {
        // Log failed permission check
        await sessionKeysService.logUsage(
          req.sessionKey.id,
          req.sessionKey.userId,
          `PERMISSION_DENIED: ${permission}`,
          resource || req.path,
          req.ip,
          req.get('User-Agent'),
          false,
          permissionCheck.reason
        );

        res.status(403).json({
          success: false,
          error: 'Insufficient permissions',
          code: 'PERMISSION_DENIED',
          details: permissionCheck.reason
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error instanceof Error ? error.message : String(error));
      res.status(500).json({
        success: false,
        error: 'Permission validation error'
      });
    }
  };
};

/**
 * Middleware that accepts both JWT tokens and session keys
 * Tries JWT first, then falls back to session key
 */
export const hybridAuthMiddleware = async (
  req: SessionKeyRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
        code: 'MISSING_AUTH'
      });
      return;
    }

    if (authHeader.startsWith('Bearer ')) {
      // Use regular JWT authentication
      const { authMiddleware } = await import('./auth');
      return authMiddleware(req, res, next);
    } else if (authHeader.startsWith('SessionKey ')) {
      // Use session key authentication
      return sessionKeyAuthMiddleware(req, res, next);
    } else {
      res.status(401).json({
        success: false,
        error: 'Invalid authentication format. Use "Bearer <token>" or "SessionKey <key>"',
        code: 'INVALID_AUTH_FORMAT'
      });
      return;
    }
  } catch (error) {
    console.error('Hybrid auth middleware error:', error instanceof Error ? error.message : String(error));
    res.status(500).json({
      success: false,
      error: 'Authentication error'
    });
  }
};

/**
 * Optional session key middleware - continues even if no session key
 */
export const optionalSessionKeyMiddleware = async (
  req: SessionKeyRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('SessionKey ')) {
      // No session key provided, continue without auth
      next();
      return;
    }

    const sessionKey = authHeader.substring(11);

    try {
      const validationResult = await sessionKeysService.validateSessionKey(sessionKey);
      
      if (validationResult.isValid && validationResult.sessionKey) {
        // Log usage
        await sessionKeysService.logUsage(
          validationResult.sessionKey.id,
          validationResult.sessionKey.userId,
          `${req.method} ${req.path}`,
          req.path,
          req.ip,
          req.get('User-Agent')
        );
        
        req.sessionKey = validationResult.sessionKey;
        req.user = {
          id: validationResult.sessionKey.userId,
          sessionId: validationResult.sessionKey.sessionId
        };

        req.hasPermission = (permission: string, resource?: string) => {
          if (!req.sessionKey) return false;
          const result = sessionKeysService.checkPermission(req.sessionKey, permission, resource);
          return result.hasPermission;
        };
      }
    } catch (error) {
      // Invalid session key, but continue without auth
      console.warn('Optional session key validation failed:', (error as any)?.message || String(error));
    }

    next();
  } catch (error) {
    console.error('Optional session key middleware error:', error instanceof Error ? error.message : String(error));
    next(); // Continue even if there's an error
  }
};

/**
 * Middleware to require specific scope access
 */
export const requireScope = (requiredScope: string) => {
  return async (req: SessionKeyRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.sessionKey) {
        res.status(401).json({
          success: false,
          error: 'Session key authentication required',
          code: 'NO_SESSION_KEY'
        });
        return;
      }

      const sessionKeyScope = req.sessionKey.scope;
      
      if (!sessionKeyScope) {
        res.status(403).json({
          success: false,
          error: 'Session key has no scope restrictions, but scope is required',
          code: 'NO_SCOPE'
        });
        return;
      }

      if (!requiredScope.startsWith(sessionKeyScope)) {
        await sessionKeysService.logUsage(
          req.sessionKey.id,
          req.sessionKey.userId,
          `SCOPE_DENIED: ${requiredScope}`,
          requiredScope,
          req.ip,
          req.get('User-Agent'),
          false,
          `Required scope '${requiredScope}' not within allowed scope '${sessionKeyScope}'`
        );

        res.status(403).json({
          success: false,
          error: 'Insufficient scope access',
          code: 'SCOPE_DENIED',
          details: `Required scope '${requiredScope}' not within allowed scope '${sessionKeyScope}'`
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Scope check error:', error instanceof Error ? error.message : String(error));
      res.status(500).json({
        success: false,
        error: 'Scope validation error'
      });
    }
  };
};