import { Request, Response, NextFunction } from 'express';
import { databaseService } from '../database/database';

/**
 * If an app id is provided (via header `x-app-id` or route param `appId`),
 * enforce that the request Origin is whitelisted for that app.
 *
 * This is intended for SDK requests coming from browsers.
 */
export function enforceAppOriginIfPresent(req: Request, res: Response, next: NextFunction) {
  try {
    const origin = req.headers.origin as string | undefined;
    // Only enforce in browser-originated requests
    if (!origin) return next();

    const headerAppId = (req.headers['x-app-id'] as string | undefined)?.trim();
    const paramAppId = (req.params?.appId as string | undefined)?.trim();
    const appId = headerAppId || paramAppId;

    if (!appId) return next();

    const db = databaseService.getDatabase();
    const allowed = db
      .prepare('SELECT 1 FROM app_allowed_origins WHERE app_id = ? AND origin = ? LIMIT 1')
      .get(appId, origin);

    if (!allowed) {
      return res
        .status(460)
        .json({ success: false, error: 'Origin not allowed for this app', code: 'ORIGIN_NOT_ALLOWED' });
    }

    return next();
  } catch (err: any) {
    return res.status(500).json({ success: false, error: err?.message || 'Origin enforcement failed' });
  }
}


