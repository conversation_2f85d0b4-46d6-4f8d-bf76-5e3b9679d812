import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { authService, AuthService } from '../services/authService';
import { databaseService } from '../database/database';
import express from 'express';
import request from 'supertest';
import cookieParser from 'cookie-parser';

describe('Cookie-Based Authentication', () => {
  let app: express.Application;

  beforeEach(async () => {
    await databaseService.initialize();
    
    // Create a simple Express app for testing
    app = express();
    app.use(express.json());
    app.use(cookieParser());
    
    // Add auth routes
    app.post('/login', async (req, res) => {
      try {
        const { email, password } = req.body;
        
        // Simple mock user verification
        if (email === '<EMAIL>' && password === 'password123') {
          const userId = 'test-user-id';
          const deviceInfo = AuthService.extractDeviceInfo(req.headers['user-agent']);
          const ipAddress = req.ip || '127.0.0.1';
          
          const tokenPair = await authService.createSession(
            userId,
            deviceInfo,
            ipAddress,
            req.headers['user-agent']
          );
          
          authService.setTokenCookies(res, tokenPair);
          
          res.json({
            success: true,
            data: {
              user: {
                id: userId,
                email: email,
                full_name: 'Test User',
                created_at: new Date().toISOString(),
              },
              message: "Login successful"
            },
          });
        } else {
          res.status(401).json({
            success: false,
            error: "Invalid credentials"
          });
        }
      } catch (error) {
        res.status(500).json({
          success: false,
          error: "Internal server error"
        });
      }
    });
    
    app.get('/me', async (req, res) => {
      try {
        const { accessToken } = authService.getTokensFromCookies(req);
        
        if (!accessToken) {
          return res.status(401).json({
            success: false,
            error: "No access token provided"
          });
        }
        
        const payload = authService.verifyAccessToken(accessToken);
        
        res.json({
          success: true,
          data: {
            user: {
              id: payload.userId,
              email: '<EMAIL>',
              full_name: 'Test User',
              created_at: new Date().toISOString(),
            }
          }
        });
      } catch (error) {
        res.status(401).json({
          success: false,
          error: "Invalid or expired token"
        });
      }
    });
    
    app.post('/logout', async (req, res) => {
      try {
        const { accessToken } = authService.getTokensFromCookies(req);
        
        if (accessToken) {
          try {
            const payload = authService.verifyAccessToken(accessToken);
            await authService.invalidateSession(payload.sessionId);
          } catch (error) {
            // Token is invalid, just clear cookies
          }
        }
        
        authService.clearTokenCookies(res);
        
        res.json({
          success: true,
          message: "Logged out successfully"
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: "Internal server error"
        });
      }
    });
  });

  afterEach(async () => {
    await databaseService.close();
  });

  it('should set secure cookies on login', async () => {
    const response = await request(app)
      .post('/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      })
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.user.email).toBe('<EMAIL>');
    
    // Check that cookies are set
    const cookies = response.headers['set-cookie'];
    expect(cookies).toBeDefined();
    expect(cookies).toHaveLength(2);
    
    // Check access token cookie
    const accessTokenCookie = cookies?.find(cookie => cookie.includes('accessToken'));
    expect(accessTokenCookie).toBeDefined();
    expect(accessTokenCookie).toContain('HttpOnly');
    expect(accessTokenCookie).toContain('SameSite=Strict');
    
    // Check refresh token cookie
    const refreshTokenCookie = cookies?.find(cookie => cookie.includes('refreshToken'));
    expect(refreshTokenCookie).toBeDefined();
    expect(refreshTokenCookie).toContain('HttpOnly');
    expect(refreshTokenCookie).toContain('SameSite=Strict');
  });

  it('should authenticate with cookies', async () => {
    // First login to get cookies
    const loginResponse = await request(app)
      .post('/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      })
      .expect(200);

    const cookies = loginResponse.headers['set-cookie'];
    
    // Use cookies to access protected endpoint
    const meResponse = await request(app)
      .get('/me')
      .set('Cookie', cookies)
      .expect(200);

    expect(meResponse.body.success).toBe(true);
    expect(meResponse.body.data.user.email).toBe('<EMAIL>');
  });

  it('should reject requests without cookies', async () => {
    const response = await request(app)
      .get('/me')
      .expect(401);

    expect(response.body.success).toBe(false);
    expect(response.body.error).toBe('No access token provided');
  });

  it('should clear cookies on logout', async () => {
    // First login to get cookies
    const loginResponse = await request(app)
      .post('/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      })
      .expect(200);

    const cookies = loginResponse.headers['set-cookie'];
    
    // Logout
    const logoutResponse = await request(app)
      .post('/logout')
      .set('Cookie', cookies)
      .expect(200);

    expect(logoutResponse.body.success).toBe(true);
    
    // Check that cookies are cleared
    const clearedCookies = logoutResponse.headers['set-cookie'];
    expect(clearedCookies).toBeDefined();
    
    // Check that cookies are set to expire in the past
    const accessTokenCookie = clearedCookies?.find(cookie => cookie.includes('accessToken'));
    const refreshTokenCookie = clearedCookies?.find(cookie => cookie.includes('refreshToken'));
    
    expect(accessTokenCookie).toContain('Expires=Thu, 01 Jan 1970 00:00:00 GMT');
    expect(refreshTokenCookie).toContain('Expires=Thu, 01 Jan 1970 00:00:00 GMT');
  });

  it('should reject invalid credentials', async () => {
    const response = await request(app)
      .post('/login')
      .send({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
      .expect(401);

    expect(response.body.success).toBe(false);
    expect(response.body.error).toBe('Invalid credentials');
    
    // Should not set any cookies
    const cookies = response.headers['set-cookie'];
    expect(cookies).toBeUndefined();
  });
});