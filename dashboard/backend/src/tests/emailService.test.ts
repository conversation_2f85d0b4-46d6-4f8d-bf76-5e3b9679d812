import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { emailService } from '../services/emailService';
import { databaseService } from '../database/database';

describe('EmailService', () => {
  beforeEach(async () => {
    // Initialize database
    await databaseService.initialize();
  });

  afterEach(async () => {
    // Clean up database
    await databaseService.close();
  });

  describe('OTP Generation', () => {
    it('should generate 6-digit OTP', () => {
      const otp = emailService['generateOTP']();
      expect(otp).toHaveLength(6);
      expect(parseInt(otp)).toBeGreaterThanOrEqual(100000);
      expect(parseInt(otp)).toBeLessThanOrEqual(999999);
    });
  });

  describe('Verification Token Generation', () => {
    it('should generate secure verification token', () => {
      const token = emailService['generateVerificationToken']();
      expect(token).toHaveLength(32);
      expect(token).toMatch(/^[a-f0-9]+$/);
    });
  });

  describe('Email Configuration', () => {
    it('should detect when email is not configured', () => {
      // Clear email environment variables
      delete process.env.EMAIL_HOST;
      delete process.env.EMAIL_PORT;
      delete process.env.EMAIL_USER;
      delete process.env.EMAIL_PASS;

      const newEmailService = new (emailService.constructor as any)();
      expect(newEmailService.isConfigured()).toBe(false);
    });

    it('should detect when email is configured', () => {
      // Set email environment variables
      process.env.EMAIL_HOST = 'smtp.gmail.com';
      process.env.EMAIL_PORT = '587';
      process.env.EMAIL_USER = '<EMAIL>';
      process.env.EMAIL_PASS = 'password';

      const newEmailService = new (emailService.constructor as any)();
      expect(newEmailService.isConfigured()).toBe(true);
    });
  });

  describe('Database Operations', () => {
    it('should store and verify OTP', async () => {
      const userId = 'test-user-id';
      const email = '<EMAIL>';
      const otp = '123456';
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      // Store OTP
      await emailService['storeOTP'](userId, email, otp, expiresAt);

      // Verify OTP
      const result = await emailService.verifyOTP(email, otp);
      expect(result).toBeDefined();
      expect(result?.userId).toBe(userId);
      expect(result?.email).toBe(email);
    });

    it('should store and verify email verification token', async () => {
      const userId = 'test-user-id';
      const email = '<EMAIL>';
      const token = 'test-token-123';
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Store verification token
      await emailService['storeVerificationToken'](userId, email, token, expiresAt);

      // Verify token
      const result = await emailService.verifyEmailToken(token);
      expect(result).toBeDefined();
      expect(result?.userId).toBe(userId);
      expect(result?.email).toBe(email);
    });

    it('should reject expired OTP', async () => {
      const userId = 'test-user-id';
      const email = '<EMAIL>';
      const otp = '123456';
      const expiresAt = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago

      // Store expired OTP
      await emailService['storeOTP'](userId, email, otp, expiresAt);

      // Try to verify expired OTP
      const result = await emailService.verifyOTP(email, otp);
      expect(result).toBeNull();
    });

    it('should reject expired verification token', async () => {
      const userId = 'test-user-id';
      const email = '<EMAIL>';
      const token = 'test-token-123';
      const expiresAt = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

      // Store expired verification token
      await emailService['storeVerificationToken'](userId, email, token, expiresAt);

      // Try to verify expired token
      const result = await emailService.verifyEmailToken(token);
      expect(result).toBeNull();
    });
  });

  describe('Cleanup Operations', () => {
    it('should clean up expired tokens', async () => {
      const db = databaseService.getDatabase();

      // Insert expired OTP
      const expiredOtpStmt = db.prepare(`
        INSERT INTO email_otps (id, user_id, email, otp, expires_at)
        VALUES (?, ?, ?, ?, ?)
      `);
      expiredOtpStmt.run(
        'test-id-1',
        'test-user',
        '<EMAIL>',
        '123456',
        new Date(Date.now() - 60 * 60 * 1000).toISOString() // 1 hour ago
      );

      // Insert expired verification token
      const expiredTokenStmt = db.prepare(`
        INSERT INTO email_verification_tokens (id, user_id, email, token, expires_at)
        VALUES (?, ?, ?, ?, ?)
      `);
      expiredTokenStmt.run(
        'test-id-2',
        'test-user',
        '<EMAIL>',
        'expired-token',
        new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 24 hours ago
      );

      // Clean up expired tokens
      const cleanedCount = await emailService.cleanupExpiredTokens();
      expect(cleanedCount).toBeGreaterThan(0);
    });
  });
});