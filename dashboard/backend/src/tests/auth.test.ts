import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { authService, AuthService } from '../services/authService';
import { databaseService } from '../database/database';

describe('AuthService - Refresh Token Mechanism', () => {
  beforeEach(async () => {
    await databaseService.initialize();
  });

  afterEach(async () => {
    await databaseService.close();
  });

  describe('Session Creation', () => {
    it('should create a session with access and refresh tokens', async () => {
      const userId = 'test-user-123';
      const deviceInfo = 'Test Device';
      const ipAddress = '***********';
      const userAgent = 'Mozilla/5.0 (Test Browser)';

      const tokenPair = await authService.createSession(userId, deviceInfo, ipAddress, userAgent);

      expect(tokenPair.accessToken).toBeDefined();
      expect(tokenPair.refreshToken).toBeDefined();
      expect(tokenPair.accessTokenExpiresAt).toBeInstanceOf(Date);
      expect(tokenPair.refreshTokenExpiresAt).toBeInstanceOf(Date);

      // Verify tokens are valid
      const accessPayload = authService.verifyAccessToken(tokenPair.accessToken);
      const refreshPayload = authService.verifyRefreshToken(tokenPair.refreshToken);

      expect(accessPayload.userId).toBe(userId);
      expect(refreshPayload.userId).toBe(userId);
      expect(refreshPayload.sessionId).toBeDefined();
    });

    it('should limit sessions per user', async () => {
      const userId = 'test-user-limit';
      
      // Create maximum sessions
      const sessions = [];
      for (let i = 0; i < 12; i++) {
        const tokenPair = await authService.createSession(userId, `Device ${i}`);
        sessions.push(tokenPair);
      }

      // Verify only 10 sessions are active
      const activeSessions = await authService.getUserSessions(userId);
      expect(activeSessions.length).toBeLessThanOrEqual(10);
    });
  });

  describe('Token Refresh', () => {
    it('should refresh access token successfully', async () => {
      const userId = 'test-user-refresh';
      const originalTokens = await authService.createSession(userId);

      // Wait a moment to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 100));

      const newTokens = await authService.refreshAccessToken(originalTokens.refreshToken);

      expect(newTokens.accessToken).not.toBe(originalTokens.accessToken);
      expect(newTokens.refreshToken).not.toBe(originalTokens.refreshToken);
      expect(newTokens.accessTokenExpiresAt.getTime()).toBeGreaterThan(originalTokens.accessTokenExpiresAt.getTime());
      expect(newTokens.refreshTokenExpiresAt.getTime()).toBeGreaterThan(originalTokens.refreshTokenExpiresAt.getTime());

      // Verify new tokens are valid
      const accessPayload = authService.verifyAccessToken(newTokens.accessToken);
      const refreshPayload = authService.verifyRefreshToken(newTokens.refreshToken);

      expect(accessPayload.userId).toBe(userId);
      expect(refreshPayload.userId).toBe(userId);
    });

    it('should reject invalid refresh tokens', async () => {
      await expect(authService.refreshAccessToken('invalid-token')).rejects.toThrow('Invalid refresh token');
    });

    it('should reject expired refresh tokens', async () => {
      const userId = 'test-user-expired';
      const tokens = await authService.createSession(userId);

      // Manually expire the refresh token in database
      const db = databaseService.getDatabase();
      const expireStmt = db.prepare(`
        UPDATE sessions 
        SET refresh_token_expires_at = datetime('now', '-1 day') 
        WHERE user_id = ?
      `);
      expireStmt.run(userId);

      await expect(authService.refreshAccessToken(tokens.refreshToken)).rejects.toThrow('Refresh token expired');
    });

    it('should prevent token replay attacks', async () => {
      const userId = 'test-user-replay';
      const tokens = await authService.createSession(userId);

      // First refresh should work
      await authService.refreshAccessToken(tokens.refreshToken);

      // Second refresh with old token should fail
      await expect(authService.refreshAccessToken(tokens.refreshToken)).rejects.toThrow('Refresh token mismatch');
    });
  });

  describe('Session Management', () => {
    it('should get user sessions', async () => {
      const userId = 'test-user-sessions';
      
      // Create multiple sessions
      await authService.createSession(userId, 'Device 1', '***********');
      await authService.createSession(userId, 'Device 2', '***********');

      const sessions = await authService.getUserSessions(userId);
      expect(sessions.length).toBeGreaterThanOrEqual(2);
      expect(sessions[0].userId).toBe(userId);
    });

    it('should invalidate specific sessions', async () => {
      const userId = 'test-user-invalidate';
      const tokens = await authService.createSession(userId);

      // Get session ID from refresh token
      const refreshPayload = authService.verifyRefreshToken(tokens.refreshToken);
      
      await authService.invalidateSession(refreshPayload.sessionId);

      // Verify session is invalidated
      const sessions = await authService.getUserSessions(userId);
      const invalidatedSession = sessions.find(s => s.id === refreshPayload.sessionId);
      expect(invalidatedSession).toBeUndefined();
    });

    it('should invalidate all user sessions', async () => {
      const userId = 'test-user-invalidate-all';
      
      // Create multiple sessions
      await authService.createSession(userId, 'Device 1');
      await authService.createSession(userId, 'Device 2');

      await authService.invalidateAllUserSessions(userId);

      const sessions = await authService.getUserSessions(userId);
      expect(sessions.length).toBe(0);
    });

    it('should get session statistics', async () => {
      const userId = 'test-user-stats';
      
      // Create sessions
      await authService.createSession(userId, 'Device 1');
      await authService.createSession(userId, 'Device 2');

      const stats = await authService.getSessionStats(userId);
      expect(stats.totalSessions).toBeGreaterThanOrEqual(2);
      expect(stats.activeSessions).toBeGreaterThanOrEqual(2);
      expect(stats.oldestSession).toBeInstanceOf(Date);
      expect(stats.newestSession).toBeInstanceOf(Date);
    });
  });

  describe('Token Verification', () => {
    it('should verify valid access tokens', async () => {
      const userId = 'test-user-verify';
      const tokens = await authService.createSession(userId);

      const payload = authService.verifyAccessToken(tokens.accessToken);
      expect(payload.userId).toBe(userId);
    });

    it('should reject invalid access tokens', async () => {
      try {
        authService.verifyAccessToken('invalid-token');
        expect(true).toBe(false); // Should not reach here
      } catch (error) {
        expect(error.message).toBe('Invalid access token');
      }
    });

    it('should reject access tokens for invalidated sessions', async () => {
      const userId = 'test-user-verify-invalid';
      const tokens = await authService.createSession(userId);

      // Invalidate the session
      const refreshPayload = authService.verifyRefreshToken(tokens.refreshToken);
      await authService.invalidateSession(refreshPayload.sessionId);

      try {
        authService.verifyAccessToken(tokens.accessToken);
        expect(true).toBe(false); // Should not reach here
      } catch (error) {
        expect(error.message).toBe('Invalid access token');
      }
    });
  });

  describe('Device Detection', () => {
    it('should detect mobile devices', () => {
      const mobileUA = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15';
      const deviceInfo = AuthService.extractDeviceInfo(mobileUA);
      expect(deviceInfo).toBe('iPhone');
    });

    it('should detect desktop devices', () => {
      const desktopUA = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36';
      const deviceInfo = AuthService.extractDeviceInfo(desktopUA);
      expect(deviceInfo).toBe('Mac');
    });

    it('should handle unknown user agents', () => {
      const deviceInfo = AuthService.extractDeviceInfo();
      expect(deviceInfo).toBe('Unknown Device');
    });
  });

  describe('Session Cleanup', () => {
    it('should cleanup expired sessions', async () => {
      const userId = 'test-user-cleanup';
      
      // Create a session and manually expire it
      await authService.createSession(userId);
      const db = databaseService.getDatabase();
      const expireStmt = db.prepare(`
        UPDATE sessions 
        SET refresh_token_expires_at = datetime('now', '-1 day') 
        WHERE user_id = ?
      `);
      expireStmt.run(userId);

      const cleanedCount = await authService.cleanupExpiredSessions();
      expect(cleanedCount).toBeGreaterThan(0);

      const sessions = await authService.getUserSessions(userId);
      expect(sessions.length).toBe(0);
    });
  });
});