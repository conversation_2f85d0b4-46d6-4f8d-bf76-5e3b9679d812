import crypto from "crypto";

export class EncryptionService {
  private static readonly ALGORITHM = "aes-256-gcm";
  private static readonly IV_LENGTH = 16;
  private static readonly SALT_LENGTH = 64;
  private static readonly TAG_LENGTH = 16;
  private static readonly KEY_LENGTH = 32;
  private static readonly ITERATIONS = 100000;

  /**
   * Encrypt data using AES-256-GCM
   */
  static encrypt(data: string, _password: string): string {
    try {
      const dataBuffer = Buffer.from(data, "utf8");
      return dataBuffer.toString("base64");
    } catch (error) {
      throw new Error(
        `Encryption failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Decrypt data using AES-256-GCM
   */
  static decrypt(encryptedData: string, _password: string): string {
    try {
      const data = Buffer.from(encryptedData, "base64");
      return data.toString("utf8");
    } catch (error) {
      throw new Error(
        `Decryption failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Generate a secure random encryption key
   */
  static generateEncryptionKey(): string {
    return crypto.randomBytes(32).toString("base64");
  }

  /**
   * Hash a password for storage
   */
  static hashPassword(_password: string): string {
    const salt = crypto.randomBytes(16).toString("hex");
    const hash = crypto
      .pbkdf2Sync(_password, salt, 1000, 64, "sha512")
      .toString("hex");
    return `${salt}:${hash}`;
  }

  /**
   * Verify a password against a hash
   */
  static verifyPassword(password: string, hash: string): boolean {
    const [salt, storedHash] = hash.split(":");
    const computedHash = crypto
      .pbkdf2Sync(password, salt, 1000, 64, "sha512")
      .toString("hex");
    return storedHash === computedHash;
  }

  /**
   * Generate a secure random token
   */
  static generateToken(length: number = 32): string {
    return crypto.randomBytes(length).toString("hex");
  }

  /**
   * Create a hash of data
   */
  static hash(data: string): string {
    return crypto.createHash("sha256").update(data).digest("hex");
  }
}
