import nodemailer from "nodemailer";
import { v4 as uuidv4 } from "uuid";
import otpGenerator from "otp-generator";
import { databaseService } from "../database/database";
import { logger } from "../utils/logger";

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

export interface EmailVerificationToken {
  id: string;
  userId: string;
  email: string;
  token: string;
  expiresAt: Date;
  used: boolean;
  createdAt: Date;
}

export interface EmailOTP {
  id: string;
  userId: string;
  email: string;
  otp: string;
  expiresAt: Date;
  used: boolean;
  createdAt: Date;
}

export class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private readonly otpLength = 6;
  private readonly otpExpiryMinutes = 10;
  private readonly verificationTokenExpiryHours = 24;

  constructor() {
    this.initializeTransporter();
  }

  private initializeTransporter(): void {
    // Allow selecting transport via env vars
    // EMAIL_TRANSPORT: 'smtp' | 'json' | 'console'
    const transportMode = (process.env.EMAIL_TRANSPORT || process.env.EMAIL_MODE || "").toLowerCase();

    try {
      if (transportMode === "json") {
        this.transporter = nodemailer.createTransport({ jsonTransport: true });
        logger.warn("Email transporter initialized in JSON mode (no emails are actually sent)");
        return;
      }

      if (["console", "stream", "log"].includes(transportMode)) {
        this.transporter = nodemailer.createTransport({ streamTransport: true, newline: "unix", buffer: true });
        logger.warn("Email transporter initialized in console/stream mode (emails are written to logs, not sent)");
        return;
      }

      // Default to SMTP if config present
      const emailConfig = this.getEmailConfig();
      if (emailConfig) {
        this.transporter = nodemailer.createTransport(emailConfig);
        logger.info("Email transporter initialized (SMTP)");
        return;
      }

      // Dev-friendly fallback: if not in production, default to JSON transport
      if ((process.env.NODE_ENV || "development") !== "production") {
        this.transporter = nodemailer.createTransport({ jsonTransport: true });
        logger.warn(
          "Email SMTP config not found. Using JSON transport in non-production (emails will be logged, not sent). Set EMAIL_TRANSPORT=smtp and EMAIL_HOST/PORT/USER/PASS to enable real sending."
        );
        return;
      }

      logger.warn("Email configuration not found, email service will be disabled");
    } catch (error) {
      logger.error("Failed to initialize email transporter:", error);
      this.transporter = null;
    }
  }

  private getEmailConfig(): EmailConfig | null {
    const host = process.env.EMAIL_HOST;
    const port = process.env.EMAIL_PORT;
    const user = process.env.EMAIL_USER;
    const pass = process.env.EMAIL_PASS;
    const secure = process.env.EMAIL_SECURE === "true";

    if (!host || !port || !user || !pass) {
      return null;
    }

    return {
      host,
      port: parseInt(port),
      secure,
      auth: { user, pass },
    };
  }

  /**
   * Generate a secure OTP using otp-generator
   */
  private generateOTP(): string {
    return otpGenerator.generate(this.otpLength, {
      digits: true,
      alphabets: false,
      upperCase: false,
      specialChars: false,
    });
  }

  /**
   * Generate a secure verification token
   */
  private generateVerificationToken(): string {
    return uuidv4().replace(/-/g, "");
  }

  /**
   * Send email verification
   */
  async sendVerificationEmail(
    userId: string,
    email: string,
    appName?: string
  ): Promise<string> {
    const token = this.generateVerificationToken();
    const expiresAt = new Date(
      Date.now() + this.verificationTokenExpiryHours * 60 * 60 * 1000
    );

    // Store verification token
    await this.storeVerificationToken(userId, email, token, expiresAt);

    // Send email
    await this.sendEmail({
      to: email,
      subject: `Verify your email address${appName ? ` - ${appName}` : ""}`,
      html: this.getVerificationEmailTemplate(token, appName),
      text: this.getVerificationEmailTextTemplate(token, appName),
    });

    return token;
  }

  /**
   * Send OTP email
   */
  async sendOTPEmail(
    userId: string,
    email: string,
    appName?: string
  ): Promise<string> {
    const otp = this.generateOTP();
    const expiresAt = new Date(Date.now() + this.otpExpiryMinutes * 60 * 1000);

    // Store OTP
    await this.storeOTP(userId, email, otp, expiresAt);

    // Send email
    await this.sendEmail({
      to: email,
      subject: `Your login code${appName ? ` - ${appName}` : ""}`,
      html: this.getOTPEmailTemplate(otp, appName),
      text: this.getOTPEmailTextTemplate(otp, appName),
    });

    return otp;
  }

  /**
   * Verify email verification token
   */
  async verifyEmailToken(
    token: string
  ): Promise<{ userId: string; email: string } | null> {
    const db = databaseService.getDatabase();

    const stmt = db.prepare(`
      SELECT user_id, email, used, expires_at
      FROM email_verification_tokens
      WHERE token = ? AND used = 0 AND expires_at > datetime('now')
    `);

    const result = stmt.get(token) as
      | {
          user_id: string;
          email: string;
          used: number;
          expires_at: string;
        }
      | undefined;

    if (!result) {
      return null;
    }

    // Mark token as used
    const updateStmt = db.prepare(`
      UPDATE email_verification_tokens
      SET used = 1, verified_at = datetime('now')
      WHERE token = ?
    `);
    updateStmt.run(token);

    // Update user email verification status
    const userUpdateStmt = db.prepare(`
      UPDATE users
      SET email_verified = 1, email_verified_at = datetime('now')
      WHERE id = ?
    `);
    userUpdateStmt.run(result.user_id);

    return {
      userId: result.user_id,
      email: result.email,
    };
  }

  /**
   * Verify OTP
   */
  async verifyOTP(
    email: string,
    otp: string
  ): Promise<{ userId: string; email: string } | null> {
    const db = databaseService.getDatabase();

    const stmt = db.prepare(`
      SELECT user_id, email, used, expires_at
      FROM email_otps
      WHERE email = ? AND otp = ? AND used = 0 AND expires_at > datetime('now')
      ORDER BY created_at DESC
      LIMIT 1
    `);

    const result = stmt.get(email, otp) as
      | {
          user_id: string;
          email: string;
          used: number;
          expires_at: string;
        }
      | undefined;

    if (!result) {
      return null;
    }

    // Mark OTP as used
    const updateStmt = db.prepare(`
      UPDATE email_otps
      SET used = 1, verified_at = datetime('now')
      WHERE user_id = ? AND otp = ?
    `);
    updateStmt.run(result.user_id, otp);

    return {
      userId: result.user_id,
      email: result.email,
    };
  }

  /**
   * Store verification token in database
   */
  private async storeVerificationToken(
    userId: string,
    email: string,
    token: string,
    expiresAt: Date
  ): Promise<void> {
    const db = databaseService.getDatabase();

    const stmt = db.prepare(`
      INSERT INTO email_verification_tokens (id, user_id, email, token, expires_at)
      VALUES (?, ?, ?, ?, ?)
    `);

    stmt.run(uuidv4(), userId, email, token, expiresAt.toISOString());
  }

  /**
   * Store OTP in database
   */
  private async storeOTP(
    userId: string,
    email: string,
    otp: string,
    expiresAt: Date
  ): Promise<void> {
    const db = databaseService.getDatabase();

    const stmt = db.prepare(`
      INSERT INTO email_otps (id, user_id, email, otp, expires_at)
      VALUES (?, ?, ?, ?, ?)
    `);

    stmt.run(uuidv4(), userId, email, otp, expiresAt.toISOString());
  }

  /**
   * Send email using nodemailer
   */
  private async sendEmail(options: {
    to: string;
    subject: string;
    html: string;
    text: string;
  }): Promise<void> {
    if (!this.transporter) {
      throw new Error("Email transporter not initialized");
    }

    try {
      const info = await this.transporter.sendMail({
        from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
        ...options,
      });

      // Log success with additional hints for non-SMTP transports
      logger.info(`Email sent to ${options.to}`);

      // If using JSON/stream transports, include message preview in logs for convenience
      const mode = (process.env.EMAIL_TRANSPORT || process.env.EMAIL_MODE || "").toLowerCase();
      if (mode === "json" || ["console", "stream", "log"].includes(mode) || (process.env.NODE_ENV !== "production" && !process.env.EMAIL_HOST)) {
        try {
          const maybeBuffer: unknown = (info as any).message;
          if (typeof maybeBuffer === "string") {
            logger.debug("Email message (string):" + maybeBuffer);
          } else if (maybeBuffer && typeof (maybeBuffer as any).toString === "function") {
            logger.debug("Email message (buffer):" + (maybeBuffer as any).toString());
          } else if (info) {
            logger.debug("Email info:", info);
          }
        } catch (_) {
          // best-effort logging only
        }
      }
    } catch (error) {
      logger.error("Failed to send email:", error);
      throw new Error("Failed to send email");
    }
  }

  /**
   * Get verification email HTML template
   */
  private getVerificationEmailTemplate(
    token: string,
    appName?: string
  ): string {
    const appTitle = appName || "Tokai";
    const verificationUrl = `${process.env.FRONTEND_URL || "http://localhost:3000"}/verify-email?token=${token}`;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Verify your email</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #6366f1; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .button { display: inline-block; padding: 12px 24px; background: #6366f1; color: white; text-decoration: none; border-radius: 6px; }
          .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${appTitle}</h1>
          </div>
          <div class="content">
            <h2>Verify your email address</h2>
            <p>Thanks for signing up! Please verify your email address by clicking the button below:</p>
            <p style="text-align: center;">
              <a href="${verificationUrl}" class="button">Verify Email Address</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #6366f1;">${verificationUrl}</p>
            <p>This link will expire in 24 hours.</p>
          </div>
          <div class="footer">
            <p>If you didn't create an account, you can safely ignore this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Get verification email text template
   */
  private getVerificationEmailTextTemplate(
    token: string,
    appName?: string
  ): string {
    const appTitle = appName || "Tokai";
    const verificationUrl = `${process.env.FRONTEND_URL || "http://localhost:3000"}/verify-email?token=${token}`;

    return `
Verify your email address - ${appTitle}

Thanks for signing up! Please verify your email address by visiting this link:

${verificationUrl}

This link will expire in 24 hours.

If you didn't create an account, you can safely ignore this email.
    `;
  }

  /**
   * Get OTP email HTML template
   */
  private getOTPEmailTemplate(otp: string, appName?: string): string {
    const appTitle = appName || "Tokai";

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Your login code</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #6366f1; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .otp { font-size: 32px; font-weight: bold; text-align: center; color: #6366f1; padding: 20px; background: white; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${appTitle}</h1>
          </div>
          <div class="content">
            <h2>Your login code</h2>
            <p>Here's your login code:</p>
            <div class="otp">${otp}</div>
            <p>This code will expire in 10 minutes.</p>
            <p>If you didn't request this code, you can safely ignore this email.</p>
          </div>
          <div class="footer">
            <p>For security reasons, this code will expire soon.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Get OTP email text template
   */
  private getOTPEmailTextTemplate(otp: string, appName?: string): string {
    const appTitle = appName || "Tokai";

    return `
Your login code - ${appTitle}

Here's your login code: ${otp}

This code will expire in 10 minutes.

If you didn't request this code, you can safely ignore this email.
    `;
  }

  /**
   * Check if email service is configured
   */
  isConfigured(): boolean {
    return this.transporter !== null;
  }

  /**
   * Clean up expired tokens and OTPs
   */
  async cleanupExpiredTokens(): Promise<number> {
    const db = databaseService.getDatabase();

    // Clean up expired verification tokens
    const tokenStmt = db.prepare(`
      DELETE FROM email_verification_tokens
      WHERE expires_at < datetime('now')
    `);
    const tokenResult = tokenStmt.run();

    // Clean up expired OTPs
    const otpStmt = db.prepare(`
      DELETE FROM email_otps
      WHERE expires_at < datetime('now')
    `);
    const otpResult = otpStmt.run();

    const totalDeleted = tokenResult.changes + otpResult.changes;
    logger.info(`Cleaned up ${totalDeleted} expired email tokens/OTPs`);

    return totalDeleted;
  }
}

export const emailService = new EmailService();
