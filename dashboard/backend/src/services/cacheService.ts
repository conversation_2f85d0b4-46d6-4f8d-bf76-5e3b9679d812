type CacheEntry<T> = {
  value: T;
  expiresAt: number; // epoch ms
};

import Redis from 'ioredis';

export class CacheService {
  private static instance: CacheService;
  private store: Map<string, CacheEntry<any>> = new Map();
  private redis: Redis | null = null;

  private defaultTtlMs = 30_000; // 30s default

  private constructor() {
    // Initialize Redis if configured
    const url = process.env.REDIS_URL;
    const host = process.env.REDIS_HOST;
    const port = process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT, 10) : undefined;
    const password = process.env.REDIS_PASSWORD;
    try {
      if (url) {
        this.redis = new Redis(url);
      } else if (host) {
        this.redis = new Redis({ host, port, password });
      }
      if (this.redis) {
        this.redis.on('error', (err) => {
          console.warn('[CacheService] Redis error, falling back to in-memory cache:', err?.message || String(err));
          this.redis = null;
        });
      }
    } catch (err) {
      console.warn('[CacheService] Failed to initialize Redis, using in-memory cache:', (err as any)?.message || String(err));
      this.redis = null;
    }
  }

  static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }

  async set<T>(key: string, value: T, ttlMs?: number): Promise<void> {
    const ttl = ttlMs ?? this.defaultTtlMs;
    if (this.redis) {
      const payload = JSON.stringify({ v: value });
      const seconds = Math.max(1, Math.floor(ttl / 1000));
      await this.redis.set(key, payload, 'EX', seconds);
      return;
    }
    const expiresAt = Date.now() + ttl;
    this.store.set(key, { value, expiresAt });
  }

  async get<T>(key: string): Promise<T | undefined> {
    if (this.redis) {
      const data = await this.redis.get(key);
      if (!data) return undefined;
      try {
        const parsed = JSON.parse(data);
        return parsed?.v as T;
      } catch {
        return undefined;
      }
    }
    const entry = this.store.get(key);
    if (!entry) return undefined;
    if (Date.now() > entry.expiresAt) {
      this.store.delete(key);
      return undefined;
    }
    return entry.value as T;
  }

  async del(key: string): Promise<void> {
    if (this.redis) {
      await this.redis.del(key);
      return;
    }
    this.store.delete(key);
  }

  async wrap<T>(key: string, ttlMs: number, producer: () => Promise<T> | T): Promise<T> {
    const cached = await this.get<T>(key);
    if (cached !== undefined) {
      return cached;
    }
    const value = await Promise.resolve(producer());
    await this.set(key, value, ttlMs);
    return value;
  }

  async health(): Promise<{ enabled: boolean; connected: boolean; message?: string }> {
    if (!this.redis) {
      return { enabled: false, connected: false, message: 'Redis not configured; using in-memory cache' };
    }
    try {
      const pong = await this.redis.ping();
      return { enabled: true, connected: pong === 'PONG' };
    } catch (err: any) {
      return { enabled: true, connected: false, message: err?.message || String(err) };
    }
  }
}

export const cacheService = CacheService.getInstance();
