import { databaseService } from "../database/database";
import { logger } from "../utils/logger";
import { cacheService } from "./cacheService";

export interface UserAnalytics {
  totalUsers: number;
  activeUsers24h: number;
  activeUsers7d: number;
  activeUsers30d: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
  userGrowthRate: number;
}

export interface WalletAnalytics {
  totalWallets: number;
  walletsCreatedToday: number;
  walletsCreatedThisWeek: number;
  walletsCreatedThisMonth: number;
  walletsByNetwork: Array<{ network: string; count: number }>;
  averageWalletsPerUser: number;
  embeddedWalletUsage: number;
  externalWalletUsage: number;
}

export interface TransactionAnalytics {
  totalTransactions: number;
  transactionsToday: number;
  transactionsThisWeek: number;
  transactionsThisMonth: number;
  successfulTransactions: number;
  failedTransactions: number;
  averageTransactionValue: string;
  transactionsByNetwork: Record<string, number>;
  transactionVolumeByDay: Array<{
    date: string;
    count: number;
    volume: string;
  }>;
}

export interface SecurityAnalytics {
  mfaEnabledUsers: number;
  socialLoginUsage: Record<string, number>;
  failedLoginAttempts24h: number;
  suspiciousActivity: number;
  accountsWithBackupCodes: number;
}

export interface AppAnalytics {
  totalApps: number;
  activeApps: number;
  appsCreatedThisMonth: number;
  apiCallsToday: number;
  apiCallsThisWeek: number;
  apiCallsThisMonth: number;
  topApps: Array<{
    appId: string;
    name: string;
    userCount: number;
    apiCalls: number;
  }>;
}

export interface DashboardMetrics {
  users: UserAnalytics;
  wallets: WalletAnalytics;
  transactions: TransactionAnalytics;
  security: SecurityAnalytics;
  apps: AppAnalytics;
  systemHealth: {
    uptime: number;
    responseTime: number;
    errorRate: number;
    databaseSize: number;
  };
}

export class AnalyticsService {
  private get db() {
    return databaseService.getDatabase();
  }

  /**
   * Get comprehensive dashboard metrics
   */
  async getDashboardMetrics(): Promise<DashboardMetrics> {
    try {
      const [users, wallets, transactions, security, apps, systemHealth] =
        await Promise.all([
          this.getUserAnalytics(),
          this.getWalletAnalytics(),
          this.getTransactionAnalytics(),
          this.getSecurityAnalytics(),
          this.getAppAnalytics(),
          this.getSystemHealth(),
        ]);

      return {
        users,
        wallets,
        transactions,
        security,
        apps,
        systemHealth,
      };
    } catch (error) {
      logger.error("Failed to get dashboard metrics:", error);
      throw error;
    }
  }

  /**
   * Get user analytics
   */
  async getUserAnalytics(): Promise<UserAnalytics> {
    const totalUsers = await cacheService.wrap<number>(
      "analytics:totalUsers",
      15_000,
      () => {
        return (
          this.db.prepare("SELECT COUNT(*) as count FROM users").get()?.count ||
          0
        );
      }
    );

    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const startOfToday = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate()
    );
    const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const activeUsers24h =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM users 
      WHERE updated_at > ?
    `
        )
        .get(yesterday.toISOString())?.count || 0;

    const activeUsers7d =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM users 
      WHERE updated_at > ?
    `
        )
        .get(weekAgo.toISOString())?.count || 0;

    const activeUsers30d =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM users 
      WHERE updated_at > ?
    `
        )
        .get(monthAgo.toISOString())?.count || 0;

    const newUsersToday =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM users 
      WHERE created_at >= ?
    `
        )
        .get(startOfToday.toISOString())?.count || 0;

    const newUsersThisWeek =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM users 
      WHERE created_at >= ?
    `
        )
        .get(startOfWeek.toISOString())?.count || 0;

    const newUsersThisMonth =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM users 
      WHERE created_at >= ?
    `
        )
        .get(startOfMonth.toISOString())?.count || 0;

    const prevMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const prevMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    const newUsersPrevMonth =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM users 
      WHERE created_at >= ? AND created_at <= ?
    `
        )
        .get(prevMonthStart.toISOString(), prevMonthEnd.toISOString())?.count ||
      0;

    const userGrowthRate =
      newUsersPrevMonth > 0
        ? ((newUsersThisMonth - newUsersPrevMonth) / newUsersPrevMonth) * 100
        : 0;

    return {
      totalUsers,
      activeUsers24h,
      activeUsers7d,
      activeUsers30d,
      newUsersToday,
      newUsersThisWeek,
      newUsersThisMonth,
      userGrowthRate,
    };
  }

  /**
   * Get wallet analytics
   */
  async getWalletAnalytics(): Promise<WalletAnalytics> {
    const totalWallets =
      this.db.prepare("SELECT COUNT(*) as count FROM wallets").get()?.count ||
      0;

    const now = new Date();
    const startOfToday = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate()
    );
    const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const walletsCreatedToday =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM wallets 
      WHERE created_at >= ?
    `
        )
        .get(startOfToday.toISOString())?.count || 0;

    const walletsCreatedThisWeek =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM wallets 
      WHERE created_at >= ?
    `
        )
        .get(startOfWeek.toISOString())?.count || 0;

    const walletsCreatedThisMonth =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM wallets 
      WHERE created_at >= ?
    `
        )
        .get(startOfMonth.toISOString())?.count || 0;

    const walletsByNetworkQuery = this.db.prepare(`
      SELECT network, COUNT(*) as count 
      FROM wallets 
      GROUP BY network
    `);
    const networkResults = walletsByNetworkQuery.all();
    const walletsByNetwork: Array<{ network: string; count: number }> =
      networkResults.map((row: any) => ({
        network: row.network,
        count: row.count,
      }));

    const userCount = await cacheService.wrap<number>(
      "analytics:userCount",
      15_000,
      () => {
        return (
          this.db.prepare("SELECT COUNT(*) as count FROM users").get()?.count ||
          1
        );
      }
    );
    const averageWalletsPerUser = totalWallets / userCount;

    // Assuming we have a wallet_type field or similar logic
    const embeddedWalletUsage =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM wallets 
      WHERE wallet_type = 'embedded' OR wallet_type IS NULL
    `
        )
        .get()?.count || 0;

    const externalWalletUsage = totalWallets - embeddedWalletUsage;

    return {
      totalWallets,
      walletsCreatedToday,
      walletsCreatedThisWeek,
      walletsCreatedThisMonth,
      walletsByNetwork,
      averageWalletsPerUser,
      embeddedWalletUsage,
      externalWalletUsage,
    };
  }

  /**
   * Get transaction analytics
   */
  async getTransactionAnalytics(): Promise<TransactionAnalytics> {
    // Mock transaction data for now
    return {
      totalTransactions: 0,
      transactionsToday: 0,
      transactionsThisWeek: 0,
      transactionsThisMonth: 0,
      successfulTransactions: 0,
      failedTransactions: 0,
      averageTransactionValue: "0",
      transactionsByNetwork: {},
      transactionVolumeByDay: [],
    };
  }

  /**
   * Get security analytics
   */
  async getSecurityAnalytics(): Promise<SecurityAnalytics> {
    const mfaEnabledUsers =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM user_mfa 
      WHERE totp_enabled = true OR sms_enabled = true
    `
        )
        .get()?.count || 0;

    const socialLoginQuery = this.db.prepare(`
      SELECT provider, COUNT(*) as count 
      FROM social_accounts 
      GROUP BY provider
    `);
    const socialLoginUsage: Record<string, number> = {};
    const socialResults = socialLoginQuery.all();
    for (const row of socialResults) {
      socialLoginUsage[row.provider] = row.count;
    }

    const failedLoginAttempts24h = 0;

    const suspiciousActivity = 0;

    const accountsWithBackupCodes =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM user_mfa 
      WHERE backup_codes IS NOT NULL AND backup_codes != '[]'
    `
        )
        .get()?.count || 0;

    return {
      mfaEnabledUsers,
      socialLoginUsage,
      failedLoginAttempts24h,
      suspiciousActivity,
      accountsWithBackupCodes,
    };
  }

  /**
   * Get app analytics
   */
  async getAppAnalytics(): Promise<AppAnalytics> {
    const totalApps =
      this.db.prepare("SELECT COUNT(*) as count FROM apps").get()?.count || 0;

    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const appsCreatedThisMonth =
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM apps 
      WHERE created_at >= ?
    `
        )
        .get(startOfMonth.toISOString())?.count || 0;

    // For active apps, we could check recent API usage or wallet activity
    const activeApps = totalApps; // Simplified for now

    // Mock API call data for now
    const apiCallsToday = 0;
    const apiCallsThisWeek = 0;
    const apiCallsThisMonth = 0;

    // Top apps by user count
    const topAppsQuery = this.db.prepare(`
      SELECT 
        a.id as appId,
        a.name,
        COUNT(DISTINCT w.end_user_id) as userCount
      FROM apps a
      LEFT JOIN wallets w ON a.id = w.app_id
      GROUP BY a.id, a.name
      ORDER BY userCount DESC
      LIMIT 10
    `);

    const topApps = topAppsQuery.all().map((row: any) => ({
      appId: row.appId,
      name: row.name,
      userCount: row.userCount || 0,
      apiCalls: 0, // Would track actual API calls
    }));

    return {
      totalApps,
      activeApps,
      appsCreatedThisMonth,
      apiCallsToday,
      apiCallsThisWeek,
      apiCallsThisMonth,
      topApps,
    };
  }

  /**
   * Get system health metrics
   */
  async getSystemHealth() {
    const uptime = process.uptime();

    // Mock response time - would integrate with actual monitoring
    const responseTime = 150; // ms

    // Mock error rate - would track actual errors
    const errorRate = 0.01; // 1%

    // Get database size (approximate)
    const dbStats = this.db
      .prepare(
        "SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()"
      )
      .get();
    const databaseSize = dbStats?.size || 0;

    return {
      uptime,
      responseTime,
      errorRate,
      databaseSize,
    };
  }

  /**
   * Track user activity
   */
  async trackUserActivity(userId: string, activity: string, metadata?: any) {
    try {
      const insertStmt = this.db.prepare(`
        INSERT INTO user_activity (user_id, activity, metadata, created_at)
        VALUES (?, ?, ?, ?)
      `);

      insertStmt.run(
        userId,
        activity,
        metadata ? JSON.stringify(metadata) : null,
        new Date().toISOString()
      );
    } catch (error) {
      logger.error("Failed to track user activity:", error);
    }
  }

  /**
   * Get user activity timeline
   */
  async getUserActivity(userId: string, limit = 50) {
    const stmt = this.db.prepare(`
      SELECT * FROM user_activity 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT ?
    `);

    return stmt.all(userId, limit);
  }

  /**
   * Get real-time metrics for dashboard
   */
  async getRealtimeMetrics() {
    const [activeUsers, recentWallets, recentActivity] = await Promise.all([
      this.getActiveUsersCount(),
      this.getRecentWalletCreations(),
      this.getRecentUserActivity(),
    ]);

    return {
      activeUsers,
      recentWallets,
      recentActivity,
      timestamp: new Date().toISOString(),
    };
  }

  private async getActiveUsersCount() {
    const last15Min = new Date(Date.now() - 15 * 60 * 1000);
    return (
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM users 
      WHERE updated_at > ?
    `
        )
        .get(last15Min.toISOString())?.count || 0
    );
  }

  private async getRecentWalletCreations() {
    const lastHour = new Date(Date.now() - 60 * 60 * 1000);
    return (
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM wallets 
      WHERE created_at > ?
    `
        )
        .get(lastHour.toISOString())?.count || 0
    );
  }

  private async getRecentUserActivity() {
    const last15Min = new Date(Date.now() - 15 * 60 * 1000);
    return (
      this.db
        .prepare(
          `
      SELECT COUNT(*) as count FROM user_activity 
      WHERE created_at > ?
    `
        )
        .get(last15Min.toISOString())?.count || 0
    );
  }
}

export const analyticsService = new AnalyticsService();
