import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import { databaseService } from '../database/database';
import { logger } from '../utils/logger';

export interface SessionKey {
  id: string;
  sessionId: string;
  userId: string;
  appId?: string;
  keyName: string;
  keyHash: string;
  permissions: string[];
  scope?: string;
  expiresAt: Date;
  lastUsedAt?: Date;
  createdAt: Date;
  isActive: boolean;
  revokedAt?: Date;
  revokedBy?: string;
  metadata?: any;
}

export interface CreateSessionKeyRequest {
  sessionId: string;
  userId: string;
  appId?: string;
  keyName: string;
  permissions: string[];
  scope?: string;
  expiryDuration?: number; // in milliseconds, default 24 hours
  metadata?: any;
}

export interface SessionKeyValidationResult {
  isValid: boolean;
  sessionKey?: SessionKey;
  error?: string;
}

export interface PermissionCheckResult {
  hasPermission: boolean;
  reason?: string;
}

export class SessionKeysService {
  private readonly keyLength = 32; // 32 bytes = 256 bits
  private readonly defaultExpiryDuration = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Create a new session key with delegated permissions
   */
  async createSessionKey(request: CreateSessionKeyRequest): Promise<{ sessionKey: SessionKey; rawKey: string }> {
    const db = databaseService.getDatabase();
    
    await this.validatePermissions(request.permissions);
    
    // Generate secure random key
    const rawKey = crypto.randomBytes(this.keyLength).toString('base64url');
    const keyHash = crypto.createHash('sha256').update(rawKey).digest('hex');
    
    const sessionKeyId = uuidv4();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (request.expiryDuration || this.defaultExpiryDuration));
    
    const insertStmt = db.prepare(`
      INSERT INTO session_keys (
        id, session_id, user_id, app_id, key_name, key_hash, 
        permissions, scope, expires_at, created_at, is_active, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    insertStmt.run(
      sessionKeyId,
      request.sessionId,
      request.userId,
      request.appId || null,
      request.keyName,
      keyHash,
      JSON.stringify(request.permissions),
      request.scope || null,
      expiresAt.toISOString(),
      now.toISOString(),
      1,
      request.metadata ? JSON.stringify(request.metadata) : null
    );
    
    const sessionKey: SessionKey = {
      id: sessionKeyId,
      sessionId: request.sessionId,
      userId: request.userId,
      appId: request.appId,
      keyName: request.keyName,
      keyHash,
      permissions: request.permissions,
      scope: request.scope,
      expiresAt,
      createdAt: now,
      isActive: true,
      metadata: request.metadata
    };
    
    logger.info(`Session key created: ${sessionKeyId} for user ${request.userId}`);
    
    return { sessionKey, rawKey };
  }

  /**
   * Validate a session key and return its information
   */
  async validateSessionKey(rawKey: string): Promise<SessionKeyValidationResult> {
    const db = databaseService.getDatabase();
    const keyHash = crypto.createHash('sha256').update(rawKey).digest('hex');
    
    const selectStmt = db.prepare(`
      SELECT * FROM session_keys 
      WHERE key_hash = ? AND is_active = 1 AND revoked_at IS NULL
    `);
    
    const row = selectStmt.get(keyHash);
    
    if (!row) {
      return { isValid: false, error: 'Session key not found or inactive' };
    }
    
    const expiresAt = new Date(row.expires_at);
    if (expiresAt <= new Date()) {
      return { isValid: false, error: 'Session key has expired' };
    }
    
    await this.updateLastUsed(row.id);
    
    const sessionKey: SessionKey = {
      id: row.id,
      sessionId: row.session_id,
      userId: row.user_id,
      appId: row.app_id,
      keyName: row.key_name,
      keyHash: row.key_hash,
      permissions: JSON.parse(row.permissions),
      scope: row.scope,
      expiresAt: new Date(row.expires_at),
      lastUsedAt: row.last_used_at ? new Date(row.last_used_at) : undefined,
      createdAt: new Date(row.created_at),
      isActive: Boolean(row.is_active),
      revokedAt: row.revoked_at ? new Date(row.revoked_at) : undefined,
      revokedBy: row.revoked_by,
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined
    };
    
    return { isValid: true, sessionKey };
  }

  /**
   * Check if a session key has a specific permission
   */
  checkPermission(sessionKey: SessionKey, requiredPermission: string, resource?: string): PermissionCheckResult {
    if (!sessionKey.permissions.includes(requiredPermission)) {
      return { 
        hasPermission: false, 
        reason: `Permission '${requiredPermission}' not granted to this session key` 
      };
    }
    
    if (sessionKey.scope && resource) {
      // Simple scope matching - can be extended for more complex patterns
      if (resource.startsWith(sessionKey.scope)) {
        return { hasPermission: true };
      } else {
        return { 
          hasPermission: false, 
          reason: `Resource '${resource}' is outside the permitted scope '${sessionKey.scope}'` 
        };
      }
    }
    
    return { hasPermission: true };
  }

  /**
   * Revoke a session key
   */
  async revokeSessionKey(sessionKeyId: string, revokedBy: string): Promise<boolean> {
    const db = databaseService.getDatabase();
    const now = new Date();
    
    const updateStmt = db.prepare(`
      UPDATE session_keys 
      SET is_active = 0, revoked_at = ?, revoked_by = ?
      WHERE id = ? AND is_active = 1
    `);
    
    const result = updateStmt.run(now.toISOString(), revokedBy, sessionKeyId);
    
    if (result.changes > 0) {
      logger.info(`Session key revoked: ${sessionKeyId} by ${revokedBy}`);
      return true;
    }
    
    return false;
  }

  /**
   * Revoke all session keys for a user
   */
  async revokeAllUserSessionKeys(userId: string, revokedBy: string): Promise<number> {
    const db = databaseService.getDatabase();
    const now = new Date();
    
    const updateStmt = db.prepare(`
      UPDATE session_keys 
      SET is_active = 0, revoked_at = ?, revoked_by = ?
      WHERE user_id = ? AND is_active = 1
    `);
    
    const result = updateStmt.run(now.toISOString(), revokedBy, userId);
    
    logger.info(`Revoked ${result.changes} session keys for user ${userId}`);
    return result.changes;
  }

  /**
   * Get all session keys for a user
   */
  async getUserSessionKeys(userId: string, includeRevoked: boolean = false): Promise<SessionKey[]> {
    const db = databaseService.getDatabase();
    
    let query = `
      SELECT * FROM session_keys 
      WHERE user_id = ?
    `;
    
    if (!includeRevoked) {
      query += ` AND is_active = 1 AND revoked_at IS NULL`;
    }
    
    query += ` ORDER BY created_at DESC`;
    
    const selectStmt = db.prepare(query);
    const rows = selectStmt.all(userId);
    
    return rows.map((row: any) => ({
      id: row.id,
      sessionId: row.session_id,
      userId: row.user_id,
      appId: row.app_id,
      keyName: row.key_name,
      keyHash: row.key_hash,
      permissions: JSON.parse(row.permissions),
      scope: row.scope,
      expiresAt: new Date(row.expires_at),
      lastUsedAt: row.last_used_at ? new Date(row.last_used_at) : undefined,
      createdAt: new Date(row.created_at),
      isActive: Boolean(row.is_active),
      revokedAt: row.revoked_at ? new Date(row.revoked_at) : undefined,
      revokedBy: row.revoked_by,
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined
    }));
  }

  /**
   * Log session key usage for audit trail
   */
  async logUsage(
    sessionKeyId: string,
    userId: string,
    action: string,
    resource?: string,
    ipAddress?: string,
    userAgent?: string,
    success: boolean = true,
    errorMessage?: string
  ): Promise<void> {
    const db = databaseService.getDatabase();
    
    const insertStmt = db.prepare(`
      INSERT INTO session_key_usage (
        session_key_id, user_id, action, resource, ip_address, 
        user_agent, success, error_message
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    insertStmt.run(
      sessionKeyId,
      userId,
      action,
      resource || null,
      ipAddress || null,
      userAgent || null,
      success ? 1 : 0,
      errorMessage || null
    );
  }

  /**
   * Clean up expired session keys
   */
  async cleanupExpiredKeys(): Promise<number> {
    const db = databaseService.getDatabase();
    const now = new Date();
    
    const updateStmt = db.prepare(`
      UPDATE session_keys 
      SET is_active = 0, revoked_at = ?, revoked_by = 'system_cleanup'
      WHERE expires_at <= ? AND is_active = 1
    `);
    
    const result = updateStmt.run(now.toISOString(), now.toISOString());
    
    if (result.changes > 0) {
      logger.info(`Cleaned up ${result.changes} expired session keys`);
    }
    
    return result.changes;
  }

  /**
   * Get available permissions
   */
  async getAvailablePermissions(category?: string): Promise<any[]> {
    const db = databaseService.getDatabase();
    
    let query = `SELECT * FROM permission_definitions`;
    const params: any[] = [];
    
    if (category) {
      query += ` WHERE category = ?`;
      params.push(category);
    }
    
    query += ` ORDER BY category, name`;
    
    const selectStmt = db.prepare(query);
    const rows = selectStmt.all(...params);
    
    return rows.map((row: any) => ({
      id: row.id,
      name: row.name,
      description: row.description,
      category: row.category,
      isSystem: Boolean(row.is_system),
      createdAt: new Date(row.created_at)
    }));
  }

  /**
   * Validate that all requested permissions exist
   */
  private async validatePermissions(permissions: string[]): Promise<void> {
    const db = databaseService.getDatabase();
    
    const selectStmt = db.prepare(`
      SELECT name FROM permission_definitions WHERE name IN (${permissions.map(() => '?').join(',')})
    `);
    
    const validPermissions = selectStmt.all(...permissions).map((row: any) => row.name);
    const invalidPermissions = permissions.filter(p => !validPermissions.includes(p));
    
    if (invalidPermissions.length > 0) {
      throw new Error(`Invalid permissions: ${invalidPermissions.join(', ')}`);
    }
  }

  /**
   * Update last used timestamp for a session key
   */
  private async updateLastUsed(sessionKeyId: string): Promise<void> {
    const db = databaseService.getDatabase();
    const now = new Date();
    
    const updateStmt = db.prepare(`
      UPDATE session_keys 
      SET last_used_at = ?
      WHERE id = ?
    `);
    
    updateStmt.run(now.toISOString(), sessionKeyId);
  }
}

// Export singleton instance
export const sessionKeysService = new SessionKeysService();