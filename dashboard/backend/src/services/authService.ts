import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';

import { Response } from 'express';
import { databaseService } from '../database/database';

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  accessTokenExpiresAt: Date;
  refreshTokenExpiresAt: Date;
}

export interface SessionInfo {
  id: string;
  userId: string;
  deviceInfo?: string;
  ipAddress?: string;
  userAgent?: string;
  lastUsedAt: Date;
  createdAt: Date;
}

export interface RefreshTokenPayload {
  sessionId: string;
  userId: string;
  tokenVersion: number;
  jti: string; // JWT ID for token uniqueness
}

export interface AccessTokenPayload {
  userId: string;
  sessionId: string;
  iat: number;
  exp: number;
}

export class AuthService {
  private readonly jwtSecret: string;
  private readonly accessTokenExpiry = '15m'; // 15 minutes
  private readonly refreshTokenExpiry = '7d'; // 7 days
  private readonly maxSessionsPerUser = 10; // Maximum sessions per user

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'dev-secret';
    if (!this.jwtSecret) {
      throw new Error('JWT_SECRET environment variable is required');
    }
  }

  /**
   * Set secure HTTP-only cookies for tokens
   */
  setTokenCookies(res: Response, tokens: TokenPair): void {
    // Access token cookie (short-lived)
    res.cookie('accessToken', tokens.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 15 * 60 * 1000, // 15 minutes
      path: '/'
    });

    // Refresh token cookie (long-lived)
    res.cookie('refreshToken', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      path: '/'
    });
  }

  /**
   * Clear token cookies
   */
  clearTokenCookies(res: Response): void {
    res.clearCookie('accessToken', { path: '/' });
    res.clearCookie('refreshToken', { path: '/' });
  }

  /**
   * Extract tokens from cookies
   */
  getTokensFromCookies(req: any): { accessToken?: string; refreshToken?: string } {
    return {
      accessToken: req.cookies?.accessToken,
      refreshToken: req.cookies?.refreshToken
    };
  }

  /**
   * Create a new session with access and refresh tokens
   */
  async createSession(
    userId: string,
    deviceInfo?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<TokenPair> {
    const db = databaseService.getDatabase();
    
    const activeSessionsCount = db.prepare(`
      SELECT COUNT(*) as count FROM sessions 
      WHERE user_id = ? AND is_active = 1
    `).get(userId).count;

    if (activeSessionsCount >= this.maxSessionsPerUser) {
      // Remove oldest session to make room
      const oldestSession = db.prepare(`
        SELECT id FROM sessions 
        WHERE user_id = ? AND is_active = 1 
        ORDER BY last_used_at ASC 
        LIMIT 1
      `).get(userId);
      
      if (oldestSession) {
        await this.invalidateSession(oldestSession.id);
      }
    }

    const sessionId = uuidv4();
    const now = new Date();
    
    // Calculate expiry times
    const accessTokenExpiresAt = new Date(now.getTime() + 15 * 60 * 1000); // 15 minutes
    const refreshTokenExpiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days

    // Generate tokens with unique JWT ID
    const jti = uuidv4();
    const initialTokenVersion = 1;
    const accessToken = this.generateAccessToken(userId, sessionId);
    const refreshToken = this.generateRefreshToken(sessionId, userId, jti, initialTokenVersion);

    // Store session in database
    const insertSessionStmt = db.prepare(`
      INSERT INTO sessions (
        id, user_id, access_token, refresh_token, 
        access_token_expires_at, refresh_token_expires_at,
        device_info, ip_address, user_agent, is_active,
        token_version, jti
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    insertSessionStmt.run(
      sessionId,
      userId,
      accessToken,
      refreshToken,
      accessTokenExpiresAt.toISOString(),
      refreshTokenExpiresAt.toISOString(),
      deviceInfo || null,
      ipAddress || null,
      userAgent || null,
      1,
      initialTokenVersion,
      jti
    );

    return {
      accessToken,
      refreshToken,
      accessTokenExpiresAt,
      refreshTokenExpiresAt,
    };
  }

  /**
   * Refresh access token using refresh token with enhanced security
   */
  async refreshAccessToken(refreshToken: string): Promise<TokenPair> {
    const db = databaseService.getDatabase();
    
    let payload: RefreshTokenPayload;
    try {
      payload = this.verifyRefreshToken(refreshToken);
    } catch (error) {
      throw new Error('Invalid refresh token');
    }

    const getSessionStmt = db.prepare(`
      SELECT id, user_id, refresh_token, refresh_token_expires_at, is_active, token_version, jti
      FROM sessions 
      WHERE id = ? AND is_active = 1
    `);
    const session = getSessionStmt.get(payload.sessionId);

    if (!session) {
      throw new Error('Session not found');
    }

    if (session.refresh_token !== refreshToken) {
      throw new Error('Refresh token mismatch');
    }

    if (session.jti !== payload.jti) {
      throw new Error('Token ID mismatch');
    }

    const refreshExpiresAt = new Date(session.refresh_token_expires_at);
    if (refreshExpiresAt < new Date()) {
      await this.invalidateSession(session.id);
      throw new Error('Refresh token expired');
    }

    if (session.token_version !== payload.tokenVersion) {
      await this.invalidateSession(session.id);
      throw new Error('Token version mismatch');
    }

    const now = new Date();
    const accessTokenExpiresAt = new Date(now.getTime() + 15 * 60 * 1000);
    const refreshTokenExpiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    const newJti = uuidv4();
    const newTokenVersion = session.token_version + 1;
    const newAccessToken = this.generateAccessToken(session.user_id, session.id);
    const newRefreshToken = this.generateRefreshToken(session.id, session.user_id, newJti, newTokenVersion);
    const updateSessionStmt = db.prepare(`
      UPDATE sessions 
      SET access_token = ?, refresh_token = ?,
          access_token_expires_at = ?, refresh_token_expires_at = ?,
          last_used_at = CURRENT_TIMESTAMP, token_version = token_version + 1, jti = ?
      WHERE id = ?
    `);

    updateSessionStmt.run(
      newAccessToken,
      newRefreshToken,
      accessTokenExpiresAt.toISOString(),
      refreshTokenExpiresAt.toISOString(),
      newJti,
      session.id
    );

    return {
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
      accessTokenExpiresAt,
      refreshTokenExpiresAt,
    };
  }

  /**
   * Verify access token and return payload
   */
  verifyAccessToken(accessToken: string): AccessTokenPayload {
    try {
      const payload = jwt.verify(accessToken, this.jwtSecret) as AccessTokenPayload;
      
      // Additional verification: check if session is still active
      const db = databaseService.getDatabase();
      const sessionStmt = db.prepare(`
        SELECT is_active FROM sessions WHERE id = ? AND access_token = ?
      `);
      const session = sessionStmt.get(payload.sessionId, accessToken);
      
      if (!session || !session.is_active) {
        throw new Error('Session is invalid or inactive');
      }
      
      return payload;
    } catch (error) {
      throw new Error('Invalid access token');
    }
  }

  /**
   * Verify refresh token and return payload
   */
  verifyRefreshToken(refreshToken: string): RefreshTokenPayload {
    try {
      const payload = jwt.verify(refreshToken, this.jwtSecret) as RefreshTokenPayload;
      return payload;
    } catch (error) {
      throw new Error('Invalid refresh token');
    }
  }

  /**
   * Get active sessions for a user with enhanced information
   */
  async getUserSessions(userId: string): Promise<SessionInfo[]> {
    const db = databaseService.getDatabase();
    const getSessionsStmt = db.prepare(`
      SELECT id, user_id, device_info, ip_address, user_agent, 
             last_used_at, created_at, token_version
      FROM sessions 
      WHERE user_id = ? AND is_active = 1
      ORDER BY last_used_at DESC
    `);

    const sessions = getSessionsStmt.all(userId);
    return sessions.map((session: any) => ({
      id: session.id,
      userId: session.user_id,
      deviceInfo: session.device_info,
      ipAddress: session.ip_address,
      userAgent: session.user_agent,
      lastUsedAt: new Date(session.last_used_at),
      createdAt: new Date(session.created_at),
    }));
  }

  /**
   * Invalidate a specific session
   */
  async invalidateSession(sessionId: string): Promise<void> {
    const db = databaseService.getDatabase();
    const invalidateStmt = db.prepare(`
      UPDATE sessions SET is_active = 0 WHERE id = ?
    `);
    invalidateStmt.run(sessionId);
  }

  /**
   * Invalidate all sessions for a user (logout from all devices)
   */
  async invalidateAllUserSessions(userId: string): Promise<void> {
    const db = databaseService.getDatabase();
    const invalidateAllStmt = db.prepare(`
      UPDATE sessions SET is_active = 0 WHERE user_id = ?
    `);
    invalidateAllStmt.run(userId);
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    const db = databaseService.getDatabase();
    const cleanupStmt = db.prepare(`
      UPDATE sessions 
      SET is_active = 0 
      WHERE refresh_token_expires_at < datetime('now') AND is_active = 1
    `);
    
    const result = cleanupStmt.run();
    return result.changes || 0;
  }

  /**
   * Update session last used time
   */
  async updateSessionLastUsed(sessionId: string): Promise<void> {
    const db = databaseService.getDatabase();
    const updateStmt = db.prepare(`
      UPDATE sessions SET last_used_at = CURRENT_TIMESTAMP WHERE id = ?
    `);
    updateStmt.run(sessionId);
  }

  /**
   * Get session statistics for a user
   */
  async getSessionStats(userId: string): Promise<{
    totalSessions: number;
    activeSessions: number;
    oldestSession: Date | null;
    newestSession: Date | null;
  }> {
    const db = databaseService.getDatabase();
    
    const statsStmt = db.prepare(`
      SELECT 
        COUNT(*) as total_sessions,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_sessions,
        MIN(created_at) as oldest_session,
        MAX(created_at) as newest_session
      FROM sessions 
      WHERE user_id = ?
    `);
    
    const stats = statsStmt.get(userId);
    
    return {
      totalSessions: stats.total_sessions || 0,
      activeSessions: stats.active_sessions || 0,
      oldestSession: stats.oldest_session ? new Date(stats.oldest_session) : null,
      newestSession: stats.newest_session ? new Date(stats.newest_session) : null,
    };
  }

  /**
   * Generate access token (short-lived)
   */
  private generateAccessToken(userId: string, sessionId: string): string {
    return jwt.sign(
      { userId, sessionId, iat: Math.floor(Date.now() / 1000), nonce: Math.random() },
      this.jwtSecret,
      { expiresIn: this.accessTokenExpiry }
    );
  }

  /**
   * Generate refresh token (long-lived) with enhanced security
   */
  private generateRefreshToken(sessionId: string, userId: string, jti: string, tokenVersion?: number): string {
    const version = tokenVersion || Math.floor(Date.now() / 1000); // Use provided version or timestamp
    return jwt.sign(
      { sessionId, userId, tokenVersion: version, jti },
      this.jwtSecret,
      { expiresIn: this.refreshTokenExpiry }
    );
  }

  /**
   * Extract device info from user agent with enhanced detection
   */
  static extractDeviceInfo(userAgent?: string): string {
    if (!userAgent) return 'Unknown Device';

    const ua = userAgent.toLowerCase();
    
    // Mobile devices
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone') || ua.includes('ipod')) {
      if (ua.includes('android')) {
        return 'Android Mobile';
      } else if (ua.includes('iphone')) {
        return 'iPhone';
      } else if (ua.includes('ipod')) {
        return 'iPod';
      }
      return 'Mobile Device';
    }
    
    // Tablets
    if (ua.includes('ipad') || ua.includes('tablet')) {
      return 'iPad';
    }
    
    // Desktop operating systems
    if (ua.includes('macintosh') || ua.includes('mac os')) {
      return 'Mac';
    } else if (ua.includes('windows')) {
      return 'Windows PC';
    } else if (ua.includes('linux')) {
      return 'Linux PC';
    } else if (ua.includes('x11') || ua.includes('unix')) {
      return 'Unix/Linux';
    }
    
    // Browsers (fallback)
    if (ua.includes('chrome')) return 'Chrome Browser';
    if (ua.includes('firefox')) return 'Firefox Browser';
    if (ua.includes('safari')) return 'Safari Browser';
    if (ua.includes('edge')) return 'Edge Browser';
    
    return 'Desktop';
  }
}

export const authService = new AuthService();