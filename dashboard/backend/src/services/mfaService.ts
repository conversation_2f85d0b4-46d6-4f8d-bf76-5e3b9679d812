import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { v4 as uuidv4 } from 'uuid';
import { databaseService } from '../database/database';
import { logger } from '../utils/logger';
import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';

export interface SMSProvider {
  sendSMS(to: string, message: string): Promise<boolean>;
}

// AWS SNS SMS Provider
class AWSSNSSMSProvider implements SMSProvider {
  private snsClient: any;

  constructor() {
    const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
    const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
    const region = process.env.AWS_REGION || 'us-east-1';

    if (!accessKeyId || !secretAccessKey) {
      console.warn('AWS credentials not configured. SMS functionality will use mock provider.');
      return;
    }

    try {
      this.snsClient = new SNSClient({ 
        region,
        credentials: {
          accessKeyId,
          secretAccess<PERSON>ey
        }
      });
    } catch (error) {
      console.warn('AWS SDK not installed. Run: npm install @aws-sdk/client-sns');
      console.warn('SMS functionality will use mock provider.');
    }
  }

  async sendSMS(to: string, message: string): Promise<boolean> {
    if (!this.snsClient) {
      console.log(`[MOCK SMS] Would send to ${to}: ${message}`);
      return true;
    }

    try {
      const command = new PublishCommand({
        PhoneNumber: to,
        Message: message,
        MessageAttributes: {
          'AWS.SNS.SMS.SMSType': {
            DataType: 'String',
            StringValue: 'Transactional'
          }
        }
      });

      const result = await this.snsClient.send(command);
      console.log(`SMS sent successfully to ${to}, MessageId: ${result.MessageId}`);
      return true;
    } catch (error) {
      console.error('Failed to send SMS via AWS SNS:', error);
      return false;
    }
  }
}

// Mock SMS Provider (fallback)
class MockSMSProvider implements SMSProvider {
  async sendSMS(to: string, message: string): Promise<boolean> {
    console.log(`[MOCK SMS] To: ${to}`);
    console.log(`[MOCK SMS] Message: ${message}`);
    console.log('[MOCK SMS] ✅ SMS would be sent successfully (mock mode)');
    return true;
  }
}

// SMS Provider Factory
function createSMSProvider(): SMSProvider {
  const smsProvider = process.env.SMS_PROVIDER?.toLowerCase();
  
  switch (smsProvider) {
    case 'aws':
    case 'sns':
      return new AWSSNSSMSProvider();
    case 'mock':
    default:
      return new MockSMSProvider();
  }
}

export interface MFAConfig {
  totpEnabled: boolean;
  smsEnabled: boolean;
  backupCodesRemaining: number;
}

export interface TOTPSetup {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

class MFAService {
  private smsProvider: SMSProvider;

  constructor(smsProvider?: SMSProvider) {
    this.smsProvider = smsProvider || createSMSProvider();
  }

  /**
   * Generate TOTP setup for user
   */
  async setupTOTP(userId: string, userEmail: string): Promise<TOTPSetup> {
    const secret = speakeasy.generateSecret({
      name: userEmail,
      issuer: 'Tokai',
      length: 32,
    });

    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);

    // Generate backup codes
    const backupCodes = this.generateBackupCodes();

    // Store temporary setup in database
    const setupId = uuidv4();
    const db = databaseService.getDatabase();
    
    const insertSetupStmt = db.prepare(`
      INSERT INTO user_mfa_setup (id, user_id, totp_secret, backup_codes, created_at)
      VALUES (?, ?, ?, ?, datetime('now'))
    `);
    
    insertSetupStmt.run(
      setupId,
      userId,
      secret.base32,
      JSON.stringify(backupCodes)
    );

    return {
      secret: secret.base32,
      qrCodeUrl,
      backupCodes,
    };
  }

  /**
   * Verify TOTP and enable it for user
   */
  async verifyAndEnableTOTP(userId: string, token: string): Promise<boolean> {
    const db = databaseService.getDatabase();
    
    // Get temporary setup
    const getSetupStmt = db.prepare(`
      SELECT totp_secret, backup_codes FROM user_mfa_setup 
      WHERE user_id = ? ORDER BY created_at DESC LIMIT 1
    `);
    const setup = getSetupStmt.get(userId);
    
    if (!setup) {
      throw new Error('No TOTP setup found. Please start setup process again.');
    }

    // Verify token
    const verified = speakeasy.totp.verify({
      secret: setup.totp_secret,
      encoding: 'base32',
      token,
      window: 2, // Allow 2 steps tolerance
    });

    if (!verified) {
      return false;
    }

    // Enable TOTP for user
    const upsertMFAStmt = db.prepare(`
      INSERT INTO user_mfa (user_id, totp_secret, totp_enabled, backup_codes)
      VALUES (?, ?, 1, ?)
      ON CONFLICT(user_id) DO UPDATE SET
        totp_secret = excluded.totp_secret,
        totp_enabled = 1,
        backup_codes = excluded.backup_codes
    `);
    
    upsertMFAStmt.run(userId, setup.totp_secret, setup.backup_codes);

    // Clean up temporary setup
    const deleteSetupStmt = db.prepare(`
      DELETE FROM user_mfa_setup WHERE user_id = ?
    `);
    deleteSetupStmt.run(userId);

    logger.info(`TOTP enabled for user ${userId}`);
    return true;
  }

  /**
   * Setup SMS MFA for user
   */
  async setupSMS(userId: string, phoneNumber: string): Promise<boolean> {
    // Generate and send verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Store verification code
    const db = databaseService.getDatabase();
    const insertCodeStmt = db.prepare(`
      INSERT INTO sms_verification_codes (user_id, phone_number, code, expires_at)
      VALUES (?, ?, ?, ?)
    `);
    
    insertCodeStmt.run(userId, phoneNumber, verificationCode, expiresAt.toISOString());

    // Send SMS
    const message = `Your Tokai verification code is: ${verificationCode}. Valid for 10 minutes.`;
    const sent = await this.smsProvider.sendSMS(phoneNumber, message);

    if (!sent) {
      throw new Error('Failed to send SMS verification code');
    }

    logger.info(`SMS verification code sent to user ${userId}`);
    return true;
  }

  /**
   * Verify SMS code and enable SMS MFA
   */
  async verifySMSAndEnable(userId: string, code: string): Promise<boolean> {
    const db = databaseService.getDatabase();
    
    // Get verification code
    const getCodeStmt = db.prepare(`
      SELECT phone_number, expires_at FROM sms_verification_codes
      WHERE user_id = ? AND code = ?
      ORDER BY created_at DESC LIMIT 1
    `);
    const verification = getCodeStmt.get(userId, code);
    
    if (!verification) {
      return false;
    }

    // Check expiration
    if (new Date(verification.expires_at) < new Date()) {
      return false;
    }

    // Enable SMS MFA
    const upsertMFAStmt = db.prepare(`
      INSERT INTO user_mfa (user_id, sms_phone, sms_enabled)
      VALUES (?, ?, 1)
      ON CONFLICT(user_id) DO UPDATE SET
        sms_phone = excluded.sms_phone,
        sms_enabled = 1
    `);
    
    upsertMFAStmt.run(userId, verification.phone_number);

    // Clean up verification codes
    const deleteCodesStmt = db.prepare(`
      DELETE FROM sms_verification_codes WHERE user_id = ?
    `);
    deleteCodesStmt.run(userId);

    logger.info(`SMS MFA enabled for user ${userId}`);
    return true;
  }

  /**
   * Send SMS verification code for login
   */
  async sendSMSCode(userId: string): Promise<boolean> {
    const db = databaseService.getDatabase();
    
    // Get user's phone number
    const getUserStmt = db.prepare(`
      SELECT sms_phone FROM user_mfa WHERE user_id = ? AND sms_enabled = 1
    `);
    const userMFA = getUserStmt.get(userId);
    
    if (!userMFA || !userMFA.sms_phone) {
      throw new Error('SMS MFA not enabled for this user');
    }

    // Generate verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes for login

    // Store verification code
    const insertCodeStmt = db.prepare(`
      INSERT INTO sms_verification_codes (user_id, phone_number, code, expires_at)
      VALUES (?, ?, ?, ?)
    `);
    
    insertCodeStmt.run(userId, userMFA.sms_phone, verificationCode, expiresAt.toISOString());

    // Send SMS
    const message = `Your Tokai login code is: ${verificationCode}. Valid for 5 minutes.`;
    const sent = await this.smsProvider.sendSMS(userMFA.sms_phone, message);

    if (!sent) {
      throw new Error('Failed to send SMS verification code');
    }

    return true;
  }

  /**
   * Verify MFA code (TOTP, SMS, or backup code)
   */
  async verifyMFA(userId: string, code: string, method?: 'totp' | 'sms' | 'backup'): Promise<boolean> {
    const db = databaseService.getDatabase();
    
    // Get user MFA config
    const getMFAStmt = db.prepare(`
      SELECT totp_secret, totp_enabled, sms_enabled, backup_codes
      FROM user_mfa WHERE user_id = ?
    `);
    const mfa = getMFAStmt.get(userId);
    
    if (!mfa) {
      return false;
    }

    // Try different verification methods
    if (!method || method === 'totp') {
      if (mfa.totp_enabled && mfa.totp_secret) {
        const verified = speakeasy.totp.verify({
          secret: mfa.totp_secret,
          encoding: 'base32',
          token: code,
          window: 2,
        });
        
        if (verified) {
          return true;
        }
      }
    }

    if (!method || method === 'sms') {
      if (mfa.sms_enabled) {
        const getCodeStmt = db.prepare(`
          SELECT id FROM sms_verification_codes
          WHERE user_id = ? AND code = ? AND expires_at > datetime('now')
          ORDER BY created_at DESC LIMIT 1
        `);
        const verification = getCodeStmt.get(userId, code);
        
        if (verification) {
          // Clean up used code
          const deleteCodeStmt = db.prepare(`
            DELETE FROM sms_verification_codes WHERE id = ?
          `);
          deleteCodeStmt.run(verification.id);
          
          return true;
        }
      }
    }

    if (!method || method === 'backup') {
      if (mfa.backup_codes) {
        const backupCodes = JSON.parse(mfa.backup_codes);
        const codeIndex = backupCodes.indexOf(code);
        
        if (codeIndex !== -1) {
          // Remove used backup code
          backupCodes.splice(codeIndex, 1);
          
          const updateCodesStmt = db.prepare(`
            UPDATE user_mfa SET backup_codes = ? WHERE user_id = ?
          `);
          updateCodesStmt.run(JSON.stringify(backupCodes), userId);
          
          logger.info(`Backup code used for user ${userId}. ${backupCodes.length} codes remaining.`);
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Get MFA configuration for user
   */
  async getMFAConfig(userId: string): Promise<MFAConfig> {
    const db = databaseService.getDatabase();
    
    const getMFAStmt = db.prepare(`
      SELECT totp_enabled, sms_enabled, backup_codes
      FROM user_mfa WHERE user_id = ?
    `);
    const mfa = getMFAStmt.get(userId);

    if (!mfa) {
      return {
        totpEnabled: false,
        smsEnabled: false,
        backupCodesRemaining: 0,
      };
    }

    return {
      totpEnabled: mfa.totp_enabled || false,
      smsEnabled: mfa.sms_enabled || false,
      backupCodesRemaining: mfa.backup_codes ? JSON.parse(mfa.backup_codes).length : 0,
    };
  }

  /**
   * Check if MFA is required for user
   */
  async isMFARequired(userId: string): Promise<boolean> {
    const config = await this.getMFAConfig(userId);
    return config.totpEnabled || config.smsEnabled;
  }

  /**
   * Disable MFA for user
   */
  async disableMFA(userId: string, method: 'totp' | 'sms' | 'all'): Promise<boolean> {
    const db = databaseService.getDatabase();
    
    if (method === 'all') {
      const deleteStmt = db.prepare(`
        DELETE FROM user_mfa WHERE user_id = ?
      `);
      deleteStmt.run(userId);
    } else if (method === 'totp') {
      const updateStmt = db.prepare(`
        UPDATE user_mfa SET totp_enabled = 0, totp_secret = NULL WHERE user_id = ?
      `);
      updateStmt.run(userId);
    } else if (method === 'sms') {
      const updateStmt = db.prepare(`
        UPDATE user_mfa SET sms_enabled = 0, sms_phone = NULL WHERE user_id = ?
      `);
      updateStmt.run(userId);
    }

    logger.info(`${method} MFA disabled for user ${userId}`);
    return true;
  }

  /**
   * Generate backup codes
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  /**
   * Regenerate backup codes
   */
  async regenerateBackupCodes(userId: string): Promise<string[]> {
    const db = databaseService.getDatabase();
    
    const newCodes = this.generateBackupCodes();
    
    const updateStmt = db.prepare(`
      UPDATE user_mfa SET backup_codes = ? WHERE user_id = ?
    `);
    updateStmt.run(JSON.stringify(newCodes), userId);

    logger.info(`Backup codes regenerated for user ${userId}`);
    return newCodes;
  }
}

export default MFAService;