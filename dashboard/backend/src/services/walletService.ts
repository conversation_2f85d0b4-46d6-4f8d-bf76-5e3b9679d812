import { ethers } from "ethers";
import { Keypair } from "@solana/web3.js";
import * as bip39 from "bip39";
// Avoid bip32 dependency; derive Solana keypair directly from seed using ed25519
import { v4 as uuidv4 } from "uuid";
import { databaseService } from "../database/database";
import { logger } from "../utils/logger";
import { EncryptionService } from "./encryptionService";

export interface GeneratedWallet {
  id: string;
  network: string;
  seedPhrase: string;
  privateKey: string;
  publicKey: string;
  address: string;
  derivationPath?: string;
}

export interface WalletGenerationOptions {
  network:
    | "ethereum"
    | "polygon"
    | "bsc"
    | "avalanche"
    | "arbitrum"
    | "optimism"
    | "solana";
  derivationPath?: string;
  existingSeedPhrase?: string;
}

export interface StoredWallet {
  id: string;
  user_id: string;
  network: string;
  wallet_type: string;
  wallet_address: string;
  encrypted_private_key: string;
  encrypted_seed_phrase: string;
  public_key: string;
  derivation_path: string;
  is_active: boolean;
  metadata: string;
  created_at: string;
  updated_at: string;
}

export class WalletService {
  private static readonly DERIVATION_PATHS = {
    ethereum: "m/44'/60'/0'/0/0",
    polygon: "m/44'/60'/0'/0/0",
    bsc: "m/44'/60'/0'/0/0",
    avalanche: "m/44'/60'/0'/0/0",
    arbitrum: "m/44'/60'/0'/0/0",
    optimism: "m/44'/60'/0'/0/0",
    solana: "m/44'/501'/0'/0'",
  };

  private static readonly NETWORK_CONFIGS = {
    ethereum: { chainId: 1, name: "Ethereum Mainnet" },
    polygon: { chainId: 137, name: "Polygon Mainnet" },
    bsc: { chainId: 56, name: "Binance Smart Chain" },
    avalanche: { chainId: 43114, name: "Avalanche C-Chain" },
    arbitrum: { chainId: 42161, name: "Arbitrum One" },
    optimism: { chainId: 10, name: "Optimism" },
    solana: { chainId: 0, name: "Solana Mainnet" },
  };

  /**
   * Generate a new wallet for the specified network
   */
  static async generateWallet(
    options: WalletGenerationOptions,
  ): Promise<GeneratedWallet> {
    const { network, derivationPath, existingSeedPhrase } = options;

    // Generate or use existing seed phrase
    const seedPhrase = existingSeedPhrase || bip39.generateMnemonic(256); // 24 words for better security

    if (network === "solana") {
      return this.generateSolanaWallet(seedPhrase, derivationPath);
    } else {
      return this.generateEthereumWallet(seedPhrase, network, derivationPath);
    }
  }

  /**
   * Generate Ethereum-compatible wallet (ETH, Polygon, BSC, etc.)
   */
  private static async generateEthereumWallet(
    seedPhrase: string,
    network: string,
    derivationPath?: string,
  ): Promise<GeneratedWallet> {
    const path =
      derivationPath ||
      this.DERIVATION_PATHS[network as keyof typeof this.DERIVATION_PATHS];

    // Generate HD wallet from seed phrase
    const hdNode = ethers.HDNodeWallet.fromPhrase(seedPhrase, undefined, path);

    return {
      id: uuidv4(),
      network,
      seedPhrase,
      privateKey: hdNode.privateKey,
      publicKey: hdNode.publicKey,
      address: hdNode.address,
      derivationPath: path,
    };
  }

  /**
   * Generate Solana wallet
   */
  private static async generateSolanaWallet(
    seedPhrase: string,
    derivationPath?: string,
  ): Promise<GeneratedWallet> {
    const path = derivationPath || this.DERIVATION_PATHS.solana;

    // Generate seed from mnemonic
    const seed = await bip39.mnemonicToSeed(seedPhrase);

    // Derive a keypair from seed using first 32 bytes (simplified)
    // For production, use a proper ed25519 HD derivation library
    const seedBytes = new Uint8Array(seed).slice(0, 32);
    const keypair = Keypair.fromSeed(seedBytes);

    return {
      id: uuidv4(),
      network: "solana",
      seedPhrase,
      privateKey: Buffer.from(keypair.secretKey).toString("hex"),
      publicKey: keypair.publicKey.toString(),
      address: keypair.publicKey.toString(),
      derivationPath: path,
    };
  }

  /**
   * Create and store wallet in database
   */
  static async createAndStoreWallet(
    userId: string,
    network?: string,
    isAutoCreated: boolean = false,
  ): Promise<{ wallet: GeneratedWallet; storedWallet: StoredWallet }> {
    try {
      const db = databaseService.getDatabase();

      // If no network specified, fetch from system configuration
      let targetNetwork = network;
      if (!targetNetwork) {
        const configStmt = db.prepare(
          "SELECT config_value FROM system_configs WHERE config_key = ?",
        );
        const result = configStmt.get("default_wallet_type") as {
          config_value: string;
        };
        targetNetwork = result?.config_value || "ethereum";
        logger.info(
          `No network specified, using default from config: ${targetNetwork}`,
        );
      }

      // Validate that the network is allowed for embedded wallet creation
      // Only Ethereum and Solana are supported for backend-created embedded wallets
      const allowedEmbeddedNetworks = ["ethereum", "solana"];
      
      if (!allowedEmbeddedNetworks.includes(targetNetwork)) {
        throw new Error(
          `Network ${targetNetwork} is not supported for embedded wallet creation. Only Ethereum and Solana are supported for backend-created wallets.`,
        );
      }

      // Check wallet limits
      const maxWalletsStmt = db.prepare(
        "SELECT config_value FROM system_configs WHERE config_key = ?",
      );
      const maxResult = maxWalletsStmt.get("max_wallets_per_user") as {
        config_value: string;
      };
      const maxWallets = parseInt(maxResult?.config_value || "5");

      const existingWalletsStmt = db.prepare(
        "SELECT COUNT(*) as count FROM wallets WHERE user_id = ? AND is_active = 1",
      );
      const existingResult = existingWalletsStmt.get(userId) as {
        count: number;
      };

      if (existingResult.count >= maxWallets) {
        throw new Error(
          `User has reached the maximum limit of ${maxWallets} wallets`,
        );
      }

      // Generate wallet
      const wallet = await this.generateWallet({
        network: targetNetwork as any,
      });

      // Encrypt sensitive data
      const encryptionKey = process.env.WALLET_ENCRYPTION_KEY;
      if (!encryptionKey) {
        throw new Error(
          "WALLET_ENCRYPTION_KEY environment variable is required",
        );
      }

      const encryptedPrivateKey = EncryptionService.encrypt(
        wallet.privateKey,
        encryptionKey,
      );
      const encryptedSeedPhrase = EncryptionService.encrypt(
        wallet.seedPhrase,
        encryptionKey,
      );

      // Store wallet data
      const metadata = JSON.stringify({
        publicKey: wallet.publicKey,
        derivationPath: wallet.derivationPath,
        createdAt: new Date().toISOString(),
        createdBy: isAutoCreated ? "system_auto_create" : "user_manual",
        configSource: network ? "manual" : "system_default",
        networkConfig:
          this.NETWORK_CONFIGS[
            targetNetwork as keyof typeof this.NETWORK_CONFIGS
          ],
      });

      const insertStmt = db.prepare(`
        INSERT INTO wallets (
          id, user_id, network, wallet_type, wallet_address, 
          encrypted_private_key, encrypted_seed_phrase, public_key, 
          derivation_path, is_active, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertStmt.run(
        wallet.id,
        userId,
        wallet.network,
        "embedded",
        wallet.address,
        encryptedPrivateKey,
        encryptedSeedPhrase,
        wallet.publicKey,
        wallet.derivationPath,
        true,
        metadata,
      );

      // If this is an auto-created wallet, mark it as such
      if (isAutoCreated) {
        await this.markWalletAsAutoCreated(userId, wallet.id, targetNetwork);
        logger.info(
          `Marked ${targetNetwork} wallet as auto-created for user ${userId}`,
        );
      }

      logger.info(
        `Successfully created and stored ${targetNetwork} wallet for user ${userId} (${isAutoCreated ? "auto-created" : "manual"})`,
      );

      // Return the stored wallet data
      const storedWalletData: StoredWallet = {
        id: wallet.id,
        user_id: userId,
        network: wallet.network,
        wallet_type: "embedded",
        wallet_address: wallet.address,
        encrypted_private_key: encryptedPrivateKey,
        encrypted_seed_phrase: encryptedSeedPhrase,
        public_key: wallet.publicKey,
        derivation_path: wallet.derivationPath || "",
        is_active: true,
        metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      return { wallet, storedWallet: storedWalletData };
    } catch (error) {
      logger.error("Error creating and storing wallet:", error);
      throw error;
    }
  }

  /**
   * Mark wallet as auto-created
   */
  private static async markWalletAsAutoCreated(
    userId: string,
    walletId: string,
    network: string,
  ): Promise<void> {
    const db = databaseService.getDatabase();

    const insertStmt = db.prepare(`
      INSERT INTO auto_created_wallets (id, user_id, wallet_id, network, wallet_type)
      VALUES (?, ?, ?, ?, ?)
    `);

    insertStmt.run(uuidv4(), userId, walletId, network, "embedded");
  }

  /**
   * Get user's wallets from database
   */
  static async getUserWallets(userId: string): Promise<StoredWallet[]> {
    const db = databaseService.getDatabase();

    const stmt = db.prepare(`
      SELECT * FROM wallets 
      WHERE user_id = ? AND is_active = 1 
      ORDER BY created_at DESC
    `);

    const wallets = stmt.all(userId) as StoredWallet[];
    return wallets;
  }

  /**
   * Check if user should get auto-created wallet
   */
  static async shouldAutoCreateWallet(userId: string): Promise<boolean> {
    const db = databaseService.getDatabase();

    // Check if auto-create is enabled
    const configStmt = db.prepare(
      "SELECT config_value FROM system_configs WHERE config_key = ?",
    );
    const result = configStmt.get("auto_create_wallet") as {
      config_value: string;
    };

    if (result?.config_value !== "true") {
      return false;
    }

    // Check if user already has an auto-created wallet
    const autoCreatedStmt = db.prepare(
      "SELECT id FROM auto_created_wallets WHERE user_id = ? LIMIT 1",
    );
    const autoCreated = autoCreatedStmt.get(userId);

    return !autoCreated;
  }

  /**
   * Get wallet by ID
   */
  static async getWalletById(
    walletId: string,
    userId: string,
  ): Promise<StoredWallet | null> {
    const db = databaseService.getDatabase();

    const stmt = db.prepare(`
      SELECT * FROM wallets 
      WHERE id = ? AND user_id = ? AND is_active = 1
    `);

    const wallet = stmt.get(walletId, userId) as StoredWallet | undefined;
    return wallet || null;
  }

  /**
   * Decrypt wallet private key
   */
  static async decryptWalletPrivateKey(
    walletId: string,
    userId: string,
  ): Promise<string> {
    const wallet = await this.getWalletById(walletId, userId);
    if (!wallet) {
      throw new Error("Wallet not found");
    }

    const encryptionKey = process.env.WALLET_ENCRYPTION_KEY;
    if (!encryptionKey) {
      throw new Error("WALLET_ENCRYPTION_KEY environment variable is required");
    }

    return EncryptionService.decrypt(
      wallet.encrypted_private_key,
      encryptionKey,
    );
  }

  /**
   * Decrypt wallet seed phrase
   */
  static async decryptWalletSeedPhrase(
    walletId: string,
    userId: string,
  ): Promise<string> {
    const wallet = await this.getWalletById(walletId, userId);
    if (!wallet) {
      throw new Error("Wallet not found");
    }

    const encryptionKey = process.env.WALLET_ENCRYPTION_KEY;
    if (!encryptionKey) {
      throw new Error("WALLET_ENCRYPTION_KEY environment variable is required");
    }

    return EncryptionService.decrypt(
      wallet.encrypted_seed_phrase,
      encryptionKey,
    );
  }

  /**
   * Validate seed phrase
   */
  static validateSeedPhrase(seedPhrase: string): boolean {
    return bip39.validateMnemonic(seedPhrase);
  }

  /**
   * Get supported networks
   */
  static getSupportedNetworks(): string[] {
    return Object.keys(this.DERIVATION_PATHS);
  }

  /**
   * Get network configuration
   */
  static getNetworkConfig(network: string) {
    return this.NETWORK_CONFIGS[network as keyof typeof this.NETWORK_CONFIGS];
  }
}
