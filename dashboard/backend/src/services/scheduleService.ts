import { authService } from './authService';
import { sessionKeysService } from './sessionKeysService';
import { emailService } from './emailService';
import { cleanupProgressiveStore } from '../middleware/rateLimiting';

export class ScheduleService {
  private static instance: ScheduleService;
  private intervals: NodeJS.Timeout[] = [];

  private constructor() {}

  static getInstance(): ScheduleService {
    if (!ScheduleService.instance) {
      ScheduleService.instance = new ScheduleService();
    }
    return ScheduleService.instance;
  }

  /**
   * Start all scheduled tasks
   */
  start(): void {
    console.log('Starting scheduled services...');
    
    // Clean up expired sessions every hour
    const sessionCleanupInterval = setInterval(async () => {
      try {
        const cleanedCount = await authService.cleanupExpiredSessions();
        if (cleanedCount > 0) {
          console.log(`Cleaned up ${cleanedCount} expired sessions`);
        }
      } catch (error) {
        console.error('Error cleaning up expired sessions:', error);
      }
    }, 60 * 60 * 1000); // 1 hour

    // Clean up expired session keys every 2 hours
    const sessionKeysCleanupInterval = setInterval(async () => {
      try {
        const cleanedCount = await sessionKeysService.cleanupExpiredKeys();
        if (cleanedCount > 0) {
          console.log(`Cleaned up ${cleanedCount} expired session keys`);
        }
      } catch (error) {
        console.error('Error cleaning up expired session keys:', error);
      }
    }, 2 * 60 * 60 * 1000); // 2 hours

    // Clean up progressive rate limiting store every 6 hours
    const rateLimitCleanupInterval = setInterval(() => {
      try {
        cleanupProgressiveStore();
        console.log('Progressive rate limiting store cleaned up');
      } catch (error) {
        console.error('Error cleaning up rate limit store:', error);
      }
    }, 6 * 60 * 60 * 1000); // 6 hours

    // Clean up expired email tokens and OTPs every 12 hours
    const emailCleanupInterval = setInterval(async () => {
      try {
        const cleanedCount = await emailService.cleanupExpiredTokens();
        if (cleanedCount > 0) {
          console.log(`Cleaned up ${cleanedCount} expired email tokens/OTPs`);
        }
      } catch (error) {
        console.error('Error cleaning up expired email tokens:', error);
      }
    }, 12 * 60 * 60 * 1000); // 12 hours

    this.intervals.push(sessionCleanupInterval, sessionKeysCleanupInterval, rateLimitCleanupInterval, emailCleanupInterval);

    // Add more scheduled tasks here as needed
    // For example: analytics aggregation, wallet health checks, etc.

    console.log('Scheduled services started successfully');
  }

  /**
   * Stop all scheduled tasks
   */
  stop(): void {
    console.log('Stopping scheduled services...');
    
    this.intervals.forEach(interval => {
      clearInterval(interval);
    });
    
    this.intervals = [];
    console.log('Scheduled services stopped');
  }

  /**
   * Clean up expired sessions immediately
   */
  async cleanupExpiredSessions(): Promise<number> {
    return await authService.cleanupExpiredSessions();
  }

  /**
   * Clean up expired session keys immediately
   */
  async cleanupExpiredSessionKeys(): Promise<number> {
    return await sessionKeysService.cleanupExpiredKeys();
  }

  /**
   * Clean up rate limiting store immediately
   */
  cleanupRateLimitStore(): void {
    cleanupProgressiveStore();
  }

  /**
   * Clean up expired email tokens immediately
   */
  async cleanupExpiredEmailTokens(): Promise<number> {
    return await emailService.cleanupExpiredTokens();
  }
}

export const scheduleService = ScheduleService.getInstance();