// Simple test to verify email service functionality
import nodemailer from 'nodemailer';
import { v4 as uuidv4 } from 'uuid';

// Test email configuration
const testConfig = {
  host: 'smtp.gmail.com',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: 'test-password'
  }
};

// Test OTP generation
function generateOTP() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Test verification token generation
function generateVerificationToken() {
  return uuidv4().replace(/-/g, '');
}

// Test email templates
function getVerificationEmailTemplate(token, appName = 'Tokai') {
  const verificationUrl = `http://localhost:3000/verify-email?token=${token}`;
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Verify your email</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #6366f1; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9fafb; }
        .button { display: inline-block; padding: 12px 24px; background: #6366f1; color: white; text-decoration: none; border-radius: 6px; }
        .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>${appName}</h1>
        </div>
        <div class="content">
          <h2>Verify your email address</h2>
          <p>Thanks for signing up! Please verify your email address by clicking the button below:</p>
          <p style="text-align: center;">
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
          </p>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #6366f1;">${verificationUrl}</p>
          <p>This link will expire in 24 hours.</p>
        </div>
        <div class="footer">
          <p>If you didn't create an account, you can safely ignore this email.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

function getOTPEmailTemplate(otp, appName = 'Tokai') {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Your login code</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #6366f1; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9fafb; }
        .otp { font-size: 32px; font-weight: bold; text-align: center; color: #6366f1; padding: 20px; background: white; border-radius: 6px; margin: 20px 0; }
        .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>${appName}</h1>
        </div>
        <div class="content">
          <h2>Your login code</h2>
          <p>Here's your login code:</p>
          <div class="otp">${otp}</div>
          <p>This code will expire in 10 minutes.</p>
          <p>If you didn't request this code, you can safely ignore this email.</p>
        </div>
        <div class="footer">
          <p>For security reasons, this code will expire soon.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Test functions
console.log('Testing email service functionality...');

// Test OTP generation
const otp = generateOTP();
console.log('Generated OTP:', otp);
console.log('OTP length:', otp.length);
console.log('OTP is numeric:', /^\d{6}$/.test(otp));

// Test verification token generation
const token = generateVerificationToken();
console.log('Generated verification token:', token);
console.log('Token length:', token.length);
console.log('Token is hexadecimal:', /^[a-f0-9]+$/.test(token));

// Test email templates
const verificationTemplate = getVerificationEmailTemplate(token, 'Test App');
const otpTemplate = getOTPEmailTemplate(otp, 'Test App');

console.log('Verification email template length:', verificationTemplate.length);
console.log('OTP email template length:', otpTemplate.length);
console.log('Templates contain required elements:', 
  verificationTemplate.includes('Verify Email Address') && 
  otpTemplate.includes(otp)
);

// Test nodemailer configuration
try {
  const transporter = nodemailer.createTransport(testConfig);
  console.log('Nodemailer transporter created successfully');
} catch (error) {
  console.log('Nodemailer configuration test passed (expected to fail with test credentials)');
}

console.log('All email service tests passed!');