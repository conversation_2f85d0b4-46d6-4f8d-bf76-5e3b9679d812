{"env": {"node": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2021, "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"no-case-declarations": "off", "prefer-const": "off", "@typescript-eslint/no-namespace": "off", "@typescript-eslint/no-unused-vars": ["warn"], "@typescript-eslint/no-explicit-any": "off"}}