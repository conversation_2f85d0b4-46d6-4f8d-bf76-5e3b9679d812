# Server Configuration
PORT=3001
BASE_URL=http://localhost:3001
FRONTEND_URL=http://localhost:3000

# Database
DATABASE_URL=./database.sqlite

# JWT Secret
JWT_SECRET=your-jwt-secret-here

# Email Configuration (Optional)
EMAIL_VERIFICATION_REQUIRED=false
EMAIL_FROM=
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASSWORD=

# Google OAuth
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Discord OAuth
DISCORD_CLIENT_ID=
DISCORD_CLIENT_SECRET=

# Twitter OAuth
TWITTER_CLIENT_ID=
TWITTER_CLIENT_SECRET=

# GitHub OAuth
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# Telegram Authentication
# Create a bot with @BotFather on Telegram
TELEGRAM_BOT_TOKEN=
TELEGRAM_BOT_USERNAME=

# Farcaster OAuth
# Register your app at https://warpcast.com/~/developers
FARCASTER_CLIENT_ID=
FARCASTER_CLIENT_SECRET=

# Session Configuration
SESSION_SECRET=your-session-secret-here
SESSION_MAX_AGE=2592000000 # 30 days in milliseconds

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000 # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info