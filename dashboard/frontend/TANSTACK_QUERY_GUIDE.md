# TanStack Query Implementation Guide

## Overview

This dashboard has been updated to use TanStack Query (React Query) for efficient data fetching, caching, and state management. This replaces the manual `useState` and `useEffect` patterns with a more robust and performant solution.

## Benefits

### Before (Manual State Management)
- ❌ Repetitive loading states in every component
- ❌ No caching - data refetched on every mount
- ❌ Complex error handling per component
- ❌ No background updates
- ❌ Manual cache invalidation
- ❌ No optimistic updates

### After (TanStack Query)
- ✅ Automatic caching with smart invalidation
- ✅ Background refetching keeps data fresh
- ✅ Centralized error handling
- ✅ Built-in loading states
- ✅ Optimistic updates for better UX
- ✅ DevTools for debugging
- ✅ Automatic retry logic
- ✅ Request deduplication

## Setup

### 1. Dependencies Added
```json
{
  "@tanstack/react-query": "^5.59.16",
  "@tanstack/react-query-devtools": "^5.59.16"
}
```

### 2. Query Client Configuration
Located in `src/lib/query-provider.tsx`:
- **Stale Time**: 5 minutes (data considered fresh)
- **GC Time**: 10 minutes (cache garbage collection)
- **Retry Logic**: 3 attempts, no retry on 4xx errors
- **Background Refetching**: Disabled on window focus, enabled on reconnect

### 3. Provider Setup
The `QueryProvider` wraps the entire app in `src/app/layout.tsx`:
```tsx
<QueryProvider>
  <AuthProvider>
    {children}
    <ToastProvider />
  </AuthProvider>
</QueryProvider>
```

## Available Hooks

### Apps Management
```tsx
import { useApps, useCreateApp, useUpdateApp, useDeleteApp } from '@/hooks/useApps';

// Get all apps
const { data: apps, isLoading, error, refetch } = useApps();

// Create app
const createAppMutation = useCreateApp();
await createAppMutation.mutateAsync({ name: 'My App' });

// Update app
const updateAppMutation = useUpdateApp();
await updateAppMutation.mutateAsync({ appId: '123', appData: { name: 'New Name' } });

// Delete app
const deleteAppMutation = useDeleteApp();
await deleteAppMutation.mutateAsync('123');
```

### Wallets Management
```tsx
import { useWallets, useWallet, useCreateWallet, useDeleteWallet, useWalletBalance } from '@/hooks/useWallets';

// Get wallets for an app
const { data: wallets } = useWallets(appId);

// Get single wallet
const { data: wallet } = useWallet(walletId);

// Get wallet balance (auto-refreshes every minute)
const { data: balance } = useWalletBalance(walletId);
```

### API Keys Management
```tsx
import { useApiKeys, useCreateApiKey, useDeleteApiKey, useRegenerateApiKey } from '@/hooks/useApiKeys';

// Get API keys for an app
const { data: apiKeys } = useApiKeys(appId);

// Create API key
const createApiKeyMutation = useCreateApiKey();
await createApiKeyMutation.mutateAsync({ appId, name: 'Production Key', permissions: 'read,write' });
```

### Analytics
```tsx
import { 
  useDashboardMetrics, 
  useUserAnalytics, 
  useWalletAnalytics,
  useTransactionAnalytics,
  useSystemHealth 
} from '@/hooks/useAnalytics';

// Dashboard metrics (refreshes every 5 minutes)
const { data: metrics } = useDashboardMetrics();

// System health (refreshes every minute)
const { data: health } = useSystemHealth();
```

## Query Keys Structure

Query keys are organized hierarchically for efficient cache management:

```tsx
// Apps
appsKeys.lists() // ['apps', 'list']
appsKeys.detail('123') // ['apps', 'detail', '123']

// Wallets
walletsKeys.list('app-123') // ['wallets', 'list', 'app-123']
walletsKeys.detail('wallet-456') // ['wallets', 'detail', 'wallet-456']

// Analytics
analyticsKeys.dashboard() // ['analytics', 'dashboard']
analyticsKeys.user() // ['analytics', 'user']
```

## Cache Management

### Automatic Invalidation
When mutations succeed, related queries are automatically invalidated:

```tsx
// Creating an app invalidates the apps list
const createAppMutation = useCreateApp();
// onSuccess: invalidates appsKeys.lists()

// Deleting a wallet removes it from cache and invalidates the list
const deleteWalletMutation = useDeleteWallet();
// onSuccess: removes walletsKeys.detail(id) and invalidates walletsKeys.list(appId)
```

### Manual Cache Updates
For optimistic updates, you can manually update the cache:

```tsx
const updateAppMutation = useUpdateApp();
// onSuccess: updates appsKeys.detail(appId) with new data
```

## Error Handling

### Global Error Configuration
- 4xx errors don't retry (user errors)
- 5xx errors retry up to 3 times
- Network errors retry automatically

### Component-Level Error Handling
```tsx
const { data, error, isLoading } = useApps();

if (error) {
  return (
    <div>
      <p>Error: {error.message}</p>
      <button onClick={() => refetch()}>Try Again</button>
    </div>
  );
}
```

## Loading States

### Built-in Loading States
```tsx
const { isLoading, isFetching, isPending } = useApps();

// isLoading: true when no data and fetching
// isFetching: true when refetching in background
// isPending: true for mutations
```

### Mutation Loading States
```tsx
const createAppMutation = useCreateApp();

<Button disabled={createAppMutation.isPending}>
  {createAppMutation.isPending ? 'Creating...' : 'Create App'}
</Button>
```

## DevTools

React Query DevTools are included in development mode:
- Shows all queries and their states
- Cache inspection and manipulation
- Query timeline
- Network tab integration

Access via the floating button in the bottom-right corner.

## Migration Guide

### From Manual State Management

**Before:**
```tsx
const [apps, setApps] = useState([]);
const [isLoading, setIsLoading] = useState(true);
const [error, setError] = useState(null);

useEffect(() => {
  const fetchApps = async () => {
    try {
      setIsLoading(true);
      const data = await apiClient.getApps();
      setApps(data);
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };
  fetchApps();
}, []);
```

**After:**
```tsx
const { data: apps = [], isLoading, error } = useApps();
```

### From Manual Mutations

**Before:**
```tsx
const [isCreating, setIsCreating] = useState(false);

const handleCreate = async (data) => {
  setIsCreating(true);
  try {
    await apiClient.createApp(data);
    // Manual refetch or state update
  } catch (error) {
    // Error handling
  } finally {
    setIsCreating(false);
  }
};
```

**After:**
```tsx
const createAppMutation = useCreateApp();

const handleCreate = async (data) => {
  try {
    await createAppMutation.mutateAsync(data);
    // Cache automatically updated
  } catch (error) {
    // Error handling
  }
};
```

## Best Practices

### 1. Use Query Keys Consistently
```tsx
// ✅ Good
const { data } = useWallet(walletId);

// ❌ Bad - inconsistent keys
const { data } = useQuery({
  queryKey: ['wallet', walletId],
  queryFn: () => getWallet(walletId)
});
```

### 2. Handle Loading States Gracefully
```tsx
// ✅ Good
if (isLoading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;
if (!data) return <EmptyState />;

// ❌ Bad - no loading states
return <div>{data.map(item => <Item key={item.id} />)}</div>;
```

### 3. Use Optimistic Updates
```tsx
const updateAppMutation = useUpdateApp();

// ✅ Good - optimistic update
const handleUpdate = async (data) => {
  await updateAppMutation.mutateAsync(data, {
    onMutate: async (newData) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: appsKeys.detail(appId) });
      
      // Snapshot previous value
      const previousApp = queryClient.getQueryData(appsKeys.detail(appId));
      
      // Optimistically update
      queryClient.setQueryData(appsKeys.detail(appId), newData);
      
      return { previousApp };
    },
    onError: (err, newData, context) => {
      // Rollback on error
      queryClient.setQueryData(appsKeys.detail(appId), context.previousApp);
    }
  });
};
```

### 4. Configure Stale Times Appropriately
```tsx
// ✅ Good - different stale times for different data
const { data: apps } = useApps(); // 2 minutes
const { data: balance } = useWalletBalance(walletId); // 30 seconds
const { data: analytics } = useDashboardMetrics(); // 5 minutes
```

## Performance Optimizations

### 1. Selective Invalidation
```tsx
// ✅ Good - only invalidate related queries
queryClient.invalidateQueries({ queryKey: appsKeys.lists() });

// ❌ Bad - invalidate everything
queryClient.invalidateQueries();
```

### 2. Background Refetching
```tsx
// ✅ Good - keep data fresh
const { data } = useQuery({
  queryKey: ['balance'],
  queryFn: getBalance,
  refetchInterval: 60000, // Every minute
  staleTime: 30000 // Consider fresh for 30 seconds
});
```

### 3. Request Deduplication
TanStack Query automatically deduplicates identical requests, so multiple components can use the same query without additional network calls.

## Troubleshooting

### Common Issues

1. **Queries not updating after mutations**
   - Check if query keys match
   - Ensure proper invalidation in `onSuccess`

2. **Infinite refetching**
   - Check `refetchInterval` and `staleTime` settings
   - Verify query key stability

3. **Cache not persisting**
   - Check `gcTime` (garbage collection time)
   - Consider using persistence plugins for critical data

### Debug Tools
- Use React Query DevTools
- Check browser network tab
- Monitor query cache in DevTools

## Next Steps

1. **Migrate remaining components** to use TanStack Query hooks
2. **Add optimistic updates** for better UX
3. **Implement offline support** with persistence
4. **Add infinite queries** for paginated data
5. **Set up prefetching** for better performance

## Resources

- [TanStack Query Documentation](https://tanstack.com/query/latest)
- [React Query DevTools](https://tanstack.com/query/latest/docs/react/devtools)
- [Query Key Factory](https://tanstack.com/query/latest/docs/react/guides/query-keys)
- [Optimistic Updates](https://tanstack.com/query/latest/docs/react/guides/optimistic-updates)
