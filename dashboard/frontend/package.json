{"name": "@tokai/dashboard-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.59.16", "@tanstack/react-query-devtools": "^5.59.16", "autoprefixer": "^10.4.0", "axios": "^1.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "iconoir-react": "^7.11.0", "lucide-react": "^0.536.0", "next": "15.2.4", "next-themes": "^0.4.6", "postcss": "^8.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "eslint": "^8.57.1", "eslint-config-next": "15.2.4", "typescript": "^5.5.4"}}