'use client';

import { useState } from 'react';
import OTPLogin from '@/components/OTPLogin';
import EmailVerification from '@/components/EmailVerification';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestAuthPage() {
  const [currentTest, setCurrentTest] = useState<'otp' | 'verification' | null>(null);

  return (
    <div className="min-h-screen gradient-bg flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="h-12 w-12 logo-gradient rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-lg">🧪</span>
            </div>
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-2">
            Authentication Test
          </h1>
          <p className="text-muted-foreground">
            Test the new authentication components
          </p>
        </div>

        {!currentTest ? (
          <Card>
            <CardHeader>
              <CardTitle>Choose a Test</CardTitle>
              <CardDescription>
                Select which authentication component to test
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={() => setCurrentTest('otp')}
                className="w-full"
              >
                Test OTP Login
              </Button>
              <Button
                onClick={() => setCurrentTest('verification')}
                className="w-full"
                variant="outline"
              >
                Test Email Verification
              </Button>
            </CardContent>
          </Card>
        ) : currentTest === 'otp' ? (
          <OTPLogin
            onBack={() => setCurrentTest(null)}
            onSuccess={() => {
              alert('OTP login successful!');
              setCurrentTest(null);
            }}
          />
        ) : (
          <EmailVerification
            onSuccess={() => {
              alert('Email verification successful!');
              setCurrentTest(null);
            }}
            onBack={() => setCurrentTest(null)}
          />
        )}
      </div>
    </div>
  );
}