@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
  :root {
    --base-50: 200 27.3% 97.8%;
    --base-100: 202 36.4% 95.7%;
    --base-200: 200 28.3% 89.6%;
    --base-300: 202 25% 82.7%;
    --base-400: 202 13.1% 62.5%;
    --base-500: 203 11.4% 44.7%;
    --base-600: 203 14.3% 32.9%;
    --base-700: 205 17.2% 25.1%;
    --base-800: 205 21% 15.9%;
    --base-900: 204 31.9% 9.2%;
    --base-950: 210 62.5% 3.1%;
    --base-1000: 210 100% 0.8%;

    --primary-50: 202 73.3% 97.1%;
    --primary-100: 201 69.7% 93.5%;
    --primary-200: 201 73.1% 86.9%;
    --primary-300: 200 69.7% 74.1%;
    --primary-400: 200 73.3% 64.7%;
    --primary-500: 200 62.2% 56.5%;
    --primary-600: 202 53% 45.9%;
    --primary-700: 202 53.2% 36.9%;
    --primary-800: 203 47.8% 31.6%;
    --primary-900: 204 44.1% 26.7%;
    --primary-950: 205 47.1% 17.1%;
    --primary-1000: 205 50.9% 11.2%;

    --background: var(--base-50);
    --foreground: var(--base-800);
    --card: 0 0% 100%;
    --card-foreground: var(--base-800);
    --popover: 0 0% 100%;
    --popover-foreground: var(--base-800);
    --primary: var(--primary-300);
    --primary-foreground: 0 0% 0%;
    --secondary: var(--base-200);
    --secondary-foreground: var(--base-950);
    --muted: var(--base-100);
    --muted-foreground: var(--base-600);
    --accent: var(--base-100);
    --accent-foreground: var(--base-800);
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: var(--base-200);
    --input: var(--base-300);
    --ring: var(--primary-300);
    --chart-1: var(--primary-300);
    --chart-2: var(--primary-200);
    --chart-3: var(--primary-400);
    --chart-4: var(--primary-300);
    --chart-5: var(--primary-100);
    --radius: 1rem;
    --sidebar: 0 0% 100%;
    --sidebar-foreground: var(--base-800);
    --sidebar-primary: var(--primary-300);
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: var(--base-50);
    --sidebar-accent-foreground: var(--base-800);
    --sidebar-border: var(--base-200);
    --sidebar-ring: var(--primary-300);
  }
  .dark {
    --background: var(--base-950);
    --foreground: var(--base-200);
    --card: var(--base-900);
    --card-foreground: var(--base-200);
    --popover: var(--base-900);
    --popover-foreground: var(--base-200);
    --primary: var(--primary-300);
    --primary-foreground: 0 0% 0%;
    --secondary: var(--base-700);
    --secondary-foreground: var(--base-50);
    --muted: var(--base-800);
    --muted-foreground: var(--base-300);
    --accent: var(--base-800);
    --accent-foreground: var(--base-200);
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: var(--base-800);
    --input: var(--base-700);
    --ring: var(--primary-300);
    --chart-1: var(--primary-300);
    --chart-2: var(--primary-200);
    --chart-3: var(--primary-400);
    --chart-4: var(--primary-300);
    --chart-5: var(--primary-100);
    --sidebar: var(--base-900);
    --sidebar-foreground: var(--base-200);
    --sidebar-primary: var(--primary-300);
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: var(--base-800);
    --sidebar-accent-foreground: var(--base-200);
    --sidebar-border: var(--base-800);
    --sidebar-ring: var(--primary-300);
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-primary-300 to-primary-600 hover:from-primary-400 hover:to-primary-700 text-primary-foreground font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
  }
  
  .btn-secondary {
    @apply bg-secondary hover:bg-secondary/80 text-secondary-foreground font-medium py-2.5 px-4 rounded-lg transition-all duration-200 border border-border;
  }
  
  .card {
    @apply bg-card rounded-xl shadow-sm border border-border p-6 hover:shadow-md transition-shadow duration-200;
  }
  
  .input-field {
    @apply w-full px-4 py-3 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 bg-background text-foreground placeholder:text-muted-foreground;
  }

  .sidebar {
    @apply bg-gradient-to-b from-background via-primary-50/30 to-primary-100/30 border-r border-border;
  }

  .header {
    @apply bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border;
  }

  .app-card {
    @apply bg-card rounded-xl border border-border p-6 hover:shadow-lg transition-all duration-200 hover:border-primary-200 hover:bg-gradient-to-br hover:from-card hover:to-primary-50/20;
  }

  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-active {
    @apply bg-emerald-100 text-emerald-800 border border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-400 dark:border-emerald-800;
  }

  .status-inactive {
    @apply bg-amber-100 text-amber-800 border border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800;
  }

  .tab-active {
    @apply text-primary-600 border-b-2 border-primary-600 bg-gradient-to-r from-primary-50 to-primary-200 dark:text-primary-300 dark:border-primary-300 dark:from-primary-950/30 dark:to-primary-900/20;
  }

  .tab-inactive {
    @apply text-muted-foreground hover:text-foreground hover:bg-muted/50 border-b-2 border-transparent;
  }

  /* Dark mode specific styles */
  .dark .sidebar {
    @apply bg-gradient-to-b from-background via-primary-950/20 to-primary-900/20;
  }

  .dark .header {
    @apply bg-background/80 backdrop-blur;
  }

  .dark .app-card {
    @apply hover:border-primary-400 hover:bg-gradient-to-br hover:from-card hover:to-primary-950/20;
  }

  /* Gradient backgrounds */
  .gradient-bg {
    @apply bg-gradient-to-br from-background via-primary-50/30 to-primary-100/30;
  }

  .dark .gradient-bg {
    @apply bg-gradient-to-br from-background via-primary-950/20 to-primary-900/20;
  }

  /* Logo gradient */
  .logo-gradient {
    @apply bg-gradient-to-br from-primary-300 via-primary-600 to-primary-800;
  }

  /* Button gradients */
  .btn-gradient {
    @apply bg-gradient-to-r from-primary-300 to-primary-600 hover:from-primary-400 hover:to-primary-700;
  }

  .btn-gradient-secondary {
    @apply bg-gradient-to-r from-primary-100 to-primary-200 hover:from-primary-200 hover:to-primary-300 text-primary-800;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground transition-colors duration-200;
  }
}

/* Enhanced UI Components */
@layer components {
  .modern-card {
    @apply bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .dark .modern-card {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.1) 100%);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-effect {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Enhanced animations */
  .hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  .dark .hover-lift:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }

  /* Loading animations */
  .pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .bounce-slow {
    animation: bounce 2s infinite;
  }

  /* Enhanced focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }

  /* Status indicators */
  .status-dot {
    @apply w-2 h-2 rounded-full;
  }

  .status-dot.online {
    @apply bg-green-500;
  }

  .status-dot.offline {
    @apply bg-gray-400;
  }

  .status-dot.warning {
    @apply bg-yellow-500;
  }
}
