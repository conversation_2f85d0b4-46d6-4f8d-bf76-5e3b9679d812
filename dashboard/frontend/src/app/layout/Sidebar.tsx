"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";
import {
  ArrowLeft,
  Home,
  ViewGrid,
} from "iconoir-react";
import { App } from "@/lib/api";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface SidebarProps {
  selectedApp: App | null;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  onBackToApps: () => void;
}

const navigation = [
  { 
    name: "Dashboard", 
    icon: Home,
    href: "/",
  },
  { 
    name: "Apps", 
    icon: ViewGrid,
    href: "/apps",
  },
];

export function Sidebar({ 
  selectedApp, 
  sidebarOpen, 
  setSidebarOpen, 
  onBackToApps 
}: SidebarProps) {
  const pathname = usePathname();

  const handleLinkClick = () => {
    setSidebarOpen(false);
  };

  const SidebarContent = () => (
    <div className="flex grow flex-col gap-y-5 overflow-y-auto sidebar px-6 pb-4">
      <div className="flex h-20 shrink-0 items-center border-b border-border/50 pb-4">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center shadow-lg">
            <span className="text-white font-bold text-lg">T</span>
          </div>
          <div className="flex flex-col">
            <h1 className="text-xl font-bold text-foreground">
              Tokai
            </h1>
            <span className="text-xs text-muted-foreground">Wallet Platform</span>
          </div>
        </div>
      </div>
      
      <nav className="flex flex-1 flex-col">
        <ul role="list" className="flex flex-1 flex-col gap-y-7">
          <li>
            <ul role="list" className="-mx-2 space-y-1">
              {navigation.map((item) => {
                const isActive = pathname === item.href || (item.href === "/apps" && pathname.startsWith("/apps"));
                
                return (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      onClick={handleLinkClick}
                      className={`
                        group flex gap-x-3 rounded-xl p-3 text-sm leading-6 font-medium transition-all duration-200 w-full text-left
                        ${
                          isActive
                            ? "bg-primary/10 text-primary shadow-sm border border-primary/20"
                            : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
                        }
                      `}
                    >
                      <item.icon
                        className="h-5 w-5 shrink-0"
                        aria-hidden="true"
                      />
                      {item.name}
                    </Link>
                  </li>
                );
              })}
            </ul>
          </li>
          {selectedApp && (
            <li>
              <div className="text-xs font-semibold leading-6 text-muted-foreground mb-2">
                Current App
              </div>
              <button
                onClick={onBackToApps}
                className="group flex gap-x-3 rounded-xl p-3 text-sm leading-6 font-medium text-muted-foreground hover:text-foreground hover:bg-muted/50 transition-all duration-200 w-full text-left"
              >
                <ArrowLeft className="h-5 w-5 shrink-0" />
                Back to Apps
              </button>
            </li>
          )}
        </ul>
      </nav>
    </div>
  );

  return (
    <>
      {/* Mobile sidebar */}
      <Dialog open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <DialogContent className="relative mr-16 flex w-full max-w-xs flex-1 p-0">
          <SidebarContent />
        </DialogContent>
      </Dialog>

      {/* Static sidebar for desktop */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <SidebarContent />
      </div>
    </>
  );
} 