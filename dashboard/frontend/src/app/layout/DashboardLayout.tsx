"use client";

import React, { useEffect } from "react";
import { Header } from "./Header";
import { Sidebar } from "./Sidebar";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useDashboard } from "@/hooks/useDashboard";
import { App } from "@/lib/api";

interface DashboardLayoutProps {
  children: React.ReactNode | ((props: {
    apps: App[];
    selectedApp: App | null;
    user: any;
    onCreateApp: (appData: {
      name: string;
      description?: string;
      domain?: string;
    }) => Promise<App>;
    onSelectApp: (app: App) => void;
    onRefresh: () => void;
  }) => React.ReactNode);
}

export const DashboardLayout = React.memo(function DashboardLayout({ children }: DashboardLayoutProps) {
  const {
    sidebarOpen,
    apps,
    selectedApp,
    user,
    isLoading,
    error,
    setSidebarOpen,
    loadApps,
    handleCreateApp,
    handleSelectApp,
    handleBackToApps,
    handleLogout,
    clearError,
  } = useDashboard();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen gradient-bg">
      <Sidebar 
        selectedApp={selectedApp}
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
        onBackToApps={handleBackToApps}
      />

      {/* Main content */}
      <div className="lg:pl-72">
        <Header 
          selectedApp={selectedApp}
          user={user}
          onBackToApps={handleBackToApps}
          onLogout={handleLogout}
          onOpenSidebar={() => setSidebarOpen(true)}
        />

        {/* Main content area */}
        <main className="py-8">
          <div className="px-4 sm:px-6 lg:px-8">
            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertDescription>
                  {error}
                  <button 
                    onClick={clearError}
                    className="ml-2 text-xs underline hover:no-underline"
                  >
                    Dismiss
                  </button>
                </AlertDescription>
              </Alert>
            )}

            {/* Pass context to children through clone */}
            {typeof children === 'function' 
              ? children({
                  apps,
                  selectedApp,
                  user,
                  onCreateApp: handleCreateApp,
                  onSelectApp: handleSelectApp,
                  onRefresh: loadApps,
                })
              : children
            }
          </div>
        </main>
      </div>
    </div>
  );
});