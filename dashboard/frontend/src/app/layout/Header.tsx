"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "@/components/ThemeToggle";
import {
  <PERSON>u,
  Bell,
  ViewGrid,
  User,
} from "iconoir-react";
import { App } from "@/lib/api";
import Link from "next/link";

interface HeaderProps {
  selectedApp: App | null;
  user: any;
  onBackToApps: () => void;
  onLogout: () => void;
  onOpenSidebar: () => void;
}

export function Header({ 
  selectedApp, 
  user, 
  onBackToApps, 
  onLogout, 
  onOpenSidebar 
}: HeaderProps) {
  return (
    <div className="sticky top-0 z-40 flex h-20 shrink-0 items-center gap-x-6 header px-6 shadow-sm backdrop-blur-lg bg-background/80 border-b border-border/50">
      <Button
        variant="ghost"
        type="button"
        className="p-2 text-muted-foreground hover:text-foreground hover:bg-muted/80 lg:hidden rounded-lg"
        onClick={onOpenSidebar}
      >
        <span className="sr-only">Open sidebar</span>
        <Menu className="h-5 w-5" aria-hidden="true" />
      </Button>

      <div className="flex flex-1 gap-x-6 self-stretch items-center">
        {/* App title section with breadcrumb-style navigation */}
        <div className="flex flex-1 items-center space-x-3">
          {selectedApp ? (
            <div className="flex items-center space-x-3">
              <Link 
                href="/apps"
                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
              >
                Apps
              </Link>
              <span className="text-muted-foreground">/</span>
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg flex items-center justify-center">
                  <span className="text-xs font-bold text-white">
                    {selectedApp.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <span className="text-lg font-semibold text-foreground">
                  {selectedApp.name}
                </span>
              </div>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg flex items-center justify-center">
                <ViewGrid className="h-4 w-4 text-white" />
              </div>
              <span className="text-lg font-semibold text-foreground">
                Applications
              </span>
            </div>
          )}
        </div>

        {/* Right side actions */}
        <div className="flex items-center gap-x-3">
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="relative text-muted-foreground hover:text-foreground hover:bg-muted/80 rounded-lg"
          >
            <span className="sr-only">View notifications</span>
            <Bell className="h-5 w-5" aria-hidden="true" />
            <span className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full"></span>
          </Button>

          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Separator */}
          <div className="hidden lg:block h-6 w-px bg-border/60" aria-hidden="true" />

          {/* Profile dropdown */}
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center space-x-3 p-2 hover:bg-muted/80 rounded-lg"
              >
                <div className="h-8 w-8 bg-gradient-to-br from-primary-500 to-primary-700 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" aria-hidden="true" />
                </div>
                <div className="hidden lg:flex lg:flex-col lg:items-start">
                  <span className="text-sm font-semibold text-foreground">
                    {user?.email?.split('@')[0] || 'User'}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {user?.email}
                  </span>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={onLogout} className="text-destructive">
                Sign out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
} 