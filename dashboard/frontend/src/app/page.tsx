"use client";

import { AnalyticsDashboard } from "@/components/AnalyticsDashboard";
import { DeveloperTools } from "@/components/DeveloperTools";
import { MobileNav } from "@/components/navigation/mobile-nav";
import { SessionKeysManager } from "@/components/SessionKeysManager";
import { SessionManager } from "@/components/SessionManager";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { EnhancedButton } from "@/components/ui/enhanced-button";
import {
  EnhancedTabs,
  TabsContent,
  TabsTrigger,
} from "@/components/ui/enhanced-tabs";
import { ModernCard } from "@/components/ui/modern-card";
import { ActionsGrid, StatsGrid } from "@/components/ui/responsive-grid";
import { DashboardSkeleton } from "@/components/ui/skeleton";
import {
  apiClient,
  App,
  DashboardMetrics,
  UserAnalytics,
  WalletAnalytics,
} from "@/lib/api";
import { useAuth } from "@/lib/auth-context";
import {
  ArrowRight,
  Bell,
  Key,
  Plus,
  Settings,
  Shield,
  Terminal,
  ViewGrid,
  Wallet as WalletIcon,
} from "iconoir-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { DashboardLayout } from "./layout/DashboardLayout";

interface DashboardContentProps {
  apps: App[];
  selectedApp: App | null;
  user: any;
  onCreateApp: (appData: {
    name: string;
    description?: string;
    domain?: string;
  }) => Promise<App>;
  onSelectApp: (app: App) => void;
  onRefresh: () => void;
}

function DashboardContent({
  apps,
  selectedApp,
  user,
  onCreateApp,
  onSelectApp,
  onRefresh,
}: DashboardContentProps) {
  const [dashboardMetrics, setDashboardMetrics] =
    useState<DashboardMetrics | null>(null);
  const [userAnalytics, setUserAnalytics] = useState<UserAnalytics | null>(
    null
  );
  const [walletAnalytics, setWalletAnalytics] =
    useState<WalletAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const totalApps = apps?.length || 0;
  const activeApps = apps?.filter((app) => app.status === "active").length;

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [metrics, users, wallets] = await Promise.all([
        apiClient.getDashboardMetrics().catch(() => null),
        apiClient.getUserAnalytics().catch(() => null),
        apiClient.getWalletAnalytics().catch(() => null),
      ]);

      setDashboardMetrics(metrics);
      setUserAnalytics(users);
      setWalletAnalytics(wallets);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to load dashboard data"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  if (isLoading) {
    return <DashboardSkeleton />;
  }

  return (
    <>
      <MobileNav />
      <EnhancedTabs defaultValue="overview" className="space-y-8">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="analytics">Analytics</TabsTrigger>
        <TabsTrigger value="session-keys">Session Keys</TabsTrigger>
        <TabsTrigger value="sessions">Sessions</TabsTrigger>
        <TabsTrigger value="developer-tools">Developer Tools</TabsTrigger>
        <TabsTrigger value="apps">Apps</TabsTrigger>

        <TabsContent value="overview" className="space-y-8">
          {/* Welcome Section */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-foreground">
              Welcome back, {user?.email?.split("@")[0] || "User"}!
            </h1>
            <p className="text-muted-foreground">
              Here&apos;s an overview of your Tokai Wallet applications and
              activity.
            </p>
          </div>

          {/* Enhanced Stats Cards */}
          <StatsGrid>
            <ModernCard
              title="Total Apps"
              value={totalApps || 0}
              subtitle={`${activeApps || 0} active`}
              icon={<ViewGrid className="h-4 w-4" />}
              trend={{ value: 12, isPositive: true }}
            />

            <ModernCard
              title="Total Wallets"
              value={
                walletAnalytics
                  ? formatNumber(walletAnalytics.totalWallets || 0)
                  : "-"
              }
              subtitle={
                walletAnalytics
                  ? `${formatNumber(walletAnalytics.activeWallets || 0)} active`
                  : "Loading..."
              }
              icon={<WalletIcon className="h-4 w-4" />}
              loading={!walletAnalytics}
            />

            <ModernCard
              title="Total Users"
              value={
                userAnalytics
                  ? formatNumber(userAnalytics.totalUsers || 0)
                  : "-"
              }
              subtitle={
                userAnalytics
                  ? `${formatNumber(userAnalytics.activeUsers || 0)} active`
                  : "Loading..."
              }
              icon={<Key className="h-4 w-4" />}
              loading={!userAnalytics}
            />

            <ModernCard
              title="This Month"
              value={
                apps.filter((app) => {
                  const now = new Date();
                  const appDate = new Date(app.created_at);
                  return (
                    appDate.getMonth() === now.getMonth() &&
                    appDate.getFullYear() === now.getFullYear()
                  );
                }).length
              }
              subtitle="New apps created"
              icon={<Plus className="h-4 w-4" />}
              trend={{ value: 8, isPositive: true }}
            />
          </StatsGrid>

          {/* Enhanced Quick Actions */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-foreground">
                Quick Actions
              </h2>
              <div className="flex items-center space-x-2">
                <EnhancedButton
                  variant="ghost"
                  size="sm"
                  icon={<Settings className="h-4 w-4" />}
                  ariaLabel="Settings"
                >
                  Settings
                </EnhancedButton>
                <EnhancedButton
                  variant="ghost"
                  size="sm"
                  icon={<Bell className="h-4 w-4" />}
                  ariaLabel="Notifications"
                >
                  Notifications
                </EnhancedButton>
              </div>
            </div>

            <ActionsGrid>
              <Link href="/apps">
                <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <ViewGrid className="h-5 w-5" />
                        <span>Manage Apps</span>
                      </div>
                      <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      View, create, and manage your applications
                    </p>
                  </CardContent>
                </Card>
              </Link>

              <Card
                className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900"
                onClick={() =>
                  (
                    document.querySelector(
                      '[data-value="analytics"]'
                    ) as HTMLElement
                  )?.click()
                }
              >
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Key className="h-5 w-5" />
                      <span>Analytics</span>
                    </div>
                    <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    View detailed analytics and metrics
                  </p>
                </CardContent>
              </Card>

              <Card
                className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900"
                onClick={() =>
                  (
                    document.querySelector(
                      '[data-value="session-keys"]'
                    ) as HTMLElement
                  )?.click()
                }
              >
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Shield className="h-5 w-5" />
                      <span>Session Keys</span>
                    </div>
                    <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Manage delegated access tokens
                  </p>
                </CardContent>
              </Card>

              <Card
                className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900"
                onClick={() =>
                  (
                    document.querySelector(
                      '[data-value="developer-tools"]'
                    ) as HTMLElement
                  )?.click()
                }
              >
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Terminal className="h-5 w-5" />
                      <span>Developer Tools</span>
                    </div>
                    <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Test APIs and access integration guides
                  </p>
                </CardContent>
              </Card>
            </ActionsGrid>
          </div>

          {/* Recent Apps */}
          {apps && apps?.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-foreground">
                  Recent Apps
                </h2>
                <Link href="/apps">
                  <EnhancedButton
                    variant="ghost"
                    size="sm"
                    icon={<ArrowRight className="h-4 w-4" />}
                    iconPosition="right"
                    ariaLabel="View all apps"
                  >
                    View All
                  </EnhancedButton>
                </Link>
              </div>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {apps?.slice(0, 3).map((app) => (
                  <Link key={app.id} href={`/apps/${app.id}`}>
                    <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
                      <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="h-8 w-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg flex items-center justify-center">
                              <span className="text-xs font-bold text-white">
                                {app.name.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <span className="truncate">{app.name}</span>
                          </div>
                          <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${
                              app.status === "active"
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                            }`}
                          >
                            {app.status}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {new Date(app.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>

              {apps && apps?.length > 3 && (
                <div className="text-center">
                  <Link
                    href="/apps"
                    className="inline-block px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700 dark:hover:bg-gray-700"
                  >
                    View All Apps ({apps?.length || 0})
                  </Link>
                </div>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="analytics">
          <AnalyticsDashboard />
        </TabsContent>

        <TabsContent value="session-keys">
          <SessionKeysManager />
        </TabsContent>

        <TabsContent value="sessions">
          <SessionManager />
        </TabsContent>

        <TabsContent value="developer-tools">
          <DeveloperTools />
        </TabsContent>

        <TabsContent value="apps" className="space-y-8">
          <div>
            {/* Apps Management Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold">Applications</h2>
                  <p className="text-muted-foreground">
                    Manage your Tokai Wallet applications
                  </p>
                </div>
                <Button asChild>
                  <Link href="/apps">
                    <ViewGrid className="h-4 w-4 mr-2" />
                    View All Apps
                  </Link>
                </Button>
              </div>

              {/* Apps Grid */}
              {apps && apps?.length > 0 ? (
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                  {apps?.map((app) => (
                    <Link
                      key={`${app.id}-${app.name}`}
                      href={`/apps/${app.id}`}
                    >
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardHeader>
                          <CardTitle className="flex items-center space-x-2">
                            <div className="h-8 w-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg flex items-center justify-center">
                              <span className="text-xs font-bold text-white">
                                {app.name.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <span className="truncate">{app.name}</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span
                                className={`status-badge ${app.status === "active" ? "status-active" : "status-inactive"}`}
                              >
                                {app.status}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {new Date(app.created_at).toLocaleDateString()}
                              </span>
                            </div>
                            {app.description && (
                              <p className="text-sm text-muted-foreground line-clamp-2">
                                {app.description}
                              </p>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <ViewGrid className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">
                        No applications yet
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        Create your first application to get started with Tokai
                        Wallet integration.
                      </p>
                      <Button asChild>
                        <Link href="/apps">
                          <Plus className="h-4 w-4 mr-2" />
                          Create Application
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>
      </EnhancedTabs>
    </>
  );
}

export default function Dashboard() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to login via AuthProvider
  }

  return (
    <DashboardLayout>
      {(props: DashboardContentProps) => <DashboardContent {...props} />}
    </DashboardLayout>
  );
}
