'use client';

import { Suspense, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/ThemeToggle';
import { WarningTriangle, ArrowLeft } from 'iconoir-react';

declare global {
  interface Window {
    onTelegramAuth: (user: any) => void;
  }
}

function TelegramAuthContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const state = searchParams.get('state');

  useEffect(() => {
    // Define the callback function for Telegram
    window.onTelegramAuth = async (user: any) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/auth/telegram/callback', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            auth_data: user,
            state: state,
          }),
        });

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || 'Authentication failed');
        }

        // Store the token in a cookie or handle authentication
        document.cookie = `auth-token=${data.data.token}; path=/; max-age=2592000; samesite=strict`;
        
        // Redirect to dashboard
        router.push('/');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
        setError(errorMessage);
        setIsLoading(false);
      }
    };

    // Load Telegram widget script
    const script = document.createElement('script');
    script.src = 'https://telegram.org/js/telegram-widget.js?22';
    script.async = true;
    script.setAttribute('data-telegram-login', process.env.NEXT_PUBLIC_TELEGRAM_BOT_USERNAME || '');
    script.setAttribute('data-size', 'large');
    script.setAttribute('data-onauth', 'onTelegramAuth(user)');
    script.setAttribute('data-request-access', 'write');
    
    const widgetContainer = document.getElementById('telegram-widget');
    if (widgetContainer) {
      widgetContainer.appendChild(script);
    }

    return () => {
      delete (window as any).onTelegramAuth;
    };
  }, [state, router]);

  return (
    <div className="min-h-screen gradient-bg flex items-center justify-center px-4 sm:px-6 lg:px-8">
      {/* Theme Toggle - Top Right */}
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>

      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="h-12 w-12 logo-gradient rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-lg">🚀</span>
            </div>
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-2">
            Sign in with Telegram
          </h1>
          <p className="text-muted-foreground">
            Use your Telegram account to continue
          </p>
        </div>

        {/* Card */}
        <div className="card">
          {error && (
            <Alert variant="destructive" className="mb-6">
              <WarningTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Authenticating...</p>
            </div>
          ) : (
            <>
              <div className="flex justify-center py-8">
                <div id="telegram-widget"></div>
              </div>

              <div className="text-center text-sm text-muted-foreground mb-4">
                Click the button above to authenticate with your Telegram account
              </div>
            </>
          )}

          <div className="mt-6 text-center">
            <Button
              variant="ghost"
              onClick={() => router.push('/login')}
              className="text-sm"
              disabled={isLoading}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to login
            </Button>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-muted-foreground">
          <p>Secure authentication powered by Telegram</p>
        </div>
      </div>
    </div>
  );
}

export default function TelegramAuthPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    }>
      <TelegramAuthContent />
    </Suspense>
  );
}