'use client';

import { Suspense, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ThemeToggle } from '@/components/ThemeToggle';
import { WarningTriangle, Check } from 'iconoir-react';

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const token = searchParams.get('token');
  const errorParam = searchParams.get('error');
  const successParam = searchParams.get('success');

  useEffect(() => {
    if (errorParam) {
      setError(decodeURIComponent(errorParam));
      return;
    }

    if (token && successParam === 'true') {
      // Store the token in a cookie
      document.cookie = `auth-token=${token}; path=/; max-age=2592000; samesite=strict`;
      setSuccess(true);
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/');
      }, 1500);
    } else {
      setError('Invalid callback parameters');
    }
  }, [token, errorParam, successParam, router]);

  return (
    <div className="min-h-screen gradient-bg flex items-center justify-center px-4 sm:px-6 lg:px-8">
      {/* Theme Toggle - Top Right */}
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>

      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="h-12 w-12 logo-gradient rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-lg">🚀</span>
            </div>
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-2">
            {error ? 'Authentication Failed' : success ? 'Success!' : 'Authenticating...'}
          </h1>
        </div>

        {/* Card */}
        <div className="card">
          {error ? (
            <Alert variant="destructive">
              <WarningTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : success ? (
            <Alert className="border-green-500 bg-green-50 dark:bg-green-950/20">
              <Check className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-600">
                Authentication successful! Redirecting to dashboard...
              </AlertDescription>
            </Alert>
          ) : (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Processing authentication...</p>
            </div>
          )}

          {error && (
            <div className="mt-6 text-center">
              <a
                href="/login"
                className="text-sm text-primary hover:underline"
              >
                Back to login
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  );
}