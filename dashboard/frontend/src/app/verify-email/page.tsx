'use client';

import { Suspense } from 'react';
import { useRouter } from 'next/navigation';
import EmailVerification from '@/components/EmailVerification';

function VerifyEmailContent() {
  const router = useRouter();

  const handleSuccess = () => {
    router.push('/');
  };

  const handleBack = () => {
    router.push('/login');
  };

  return (
    <EmailVerification
      onSuccess={handleSuccess}
      onBack={handleBack}
    />
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    }>
      <VerifyEmailContent />
    </Suspense>
  );
}