"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth-context";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ThemeToggle } from "@/components/ThemeToggle";
import OTPLogin from "@/components/OTPLogin";
import {
  Eye,
  EyeClosed,
  Check,
  WarningTriangle,
  Refresh,
  Mail,
  Lock,
  Google,
  MessageText,
} from "iconoir-react";

export default function LoginPage() {
  const router = useRouter();
  const { login, register, isAuthenticated } = useAuth();
  const [isSignUp, setIsSignUp] = useState(false);
  const [authType, setAuthType] = useState<"password" | "otp">("password");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [fullName, setFullName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [socialLoading, setSocialLoading] = useState<string | null>(null);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await login(email, password);
      router.push("/");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Login failed";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await register(email, password, fullName || undefined);
      router.push("/");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Registration failed";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = isSignUp ? handleSignUp : handleLogin;

  const handleSocialLogin = async (provider: string) => {
    setSocialLoading(provider);
    setError(null);

    try {
      const response = await fetch("/api/auth/social/initiate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          provider,
          redirectUrl: window.location.origin + "/auth/callback",
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || "Failed to initiate social login");
      }

      // Handle Telegram differently - it uses a widget
      if (
        provider === "telegram" &&
        data.data.authUrl.startsWith("telegram-widget://")
      ) {
        // We'll handle this with a Telegram widget component
        window.location.href = `/auth/telegram?state=${data.data.state}`;
      } else {
        // Redirect to OAuth provider
        window.location.href = data.data.authUrl;
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Social login failed";
      setError(errorMessage);
      setSocialLoading(null);
    }
  };

  return (
    <div className="min-h-screen gradient-bg flex items-center justify-center px-4 sm:px-6 lg:px-8">
      {/* Theme Toggle - Top Right */}
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>

      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="h-12 w-12 logo-gradient rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-lg">🚀</span>
            </div>
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-2">
            Welcome to Tokai Wallet
          </h1>
          <p className="text-muted-foreground">
            {isSignUp
              ? "Create your account to get started"
              : "Sign in to your account"}
          </p>
        </div>

        {/* Authentication Type Selector */}
        <div className="card">
          {/* Social Login Buttons */}
          <div className="space-y-3 mb-6">
            <p className="text-sm text-muted-foreground text-center mb-4">
              Sign in with your favorite platform
            </p>
            <div className="grid grid-cols-2 gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleSocialLogin("google")}
                disabled={socialLoading !== null}
                className="w-full relative"
              >
                {socialLoading === "google" ? (
                  <Refresh className="h-4 w-4 animate-spin" />
                ) : (
                  <>
                    <Google className="h-4 w-4 mr-2" />
                    Google
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleSocialLogin("github")}
                disabled={socialLoading !== null}
                className="w-full"
              >
                {socialLoading === "github" ? (
                  <Refresh className="h-4 w-4 animate-spin" />
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    GitHub
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleSocialLogin("discord")}
                disabled={socialLoading !== null}
                className="w-full"
              >
                {socialLoading === "discord" ? (
                  <Refresh className="h-4 w-4 animate-spin" />
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Discord
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleSocialLogin("twitter")}
                disabled={socialLoading !== null}
                className="w-full"
              >
                {socialLoading === "twitter" ? (
                  <Refresh className="h-4 w-4 animate-spin" />
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Twitter
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleSocialLogin("telegram")}
                disabled={socialLoading !== null}
                className="w-full"
              >
                {socialLoading === "telegram" ? (
                  <Refresh className="h-4 w-4 animate-spin" />
                ) : (
                  <>
                    <MessageText className="h-4 w-4 mr-2" />
                    Telegram
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleSocialLogin("farcaster")}
                disabled={socialLoading !== null}
                className="w-full"
              >
                {socialLoading === "farcaster" ? (
                  <Refresh className="h-4 w-4 animate-spin" />
                ) : (
                  <>
                    <svg
                      className="h-4 w-4 mr-2"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 2L2 7L12 12L22 7L12 2Z"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M2 17L12 22L22 17"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M2 12L12 17L22 12"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    Farcaster
                  </>
                )}
              </Button>
            </div>

            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-border"></div>
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
            </div>
          </div>

          <div className="flex space-x-2 mb-6">
            <button
              type="button"
              onClick={() => setAuthType("password")}
              className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-lg border transition-colors ${
                authType === "password"
                  ? "border-primary bg-primary/10 text-primary"
                  : "border-border text-muted-foreground hover:text-foreground"
              }`}
            >
              <Lock className="h-4 w-4" />
              <span className="text-sm font-medium">Password</span>
            </button>
            <button
              type="button"
              onClick={() => setAuthType("otp")}
              className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-lg border transition-colors ${
                authType === "otp"
                  ? "border-primary bg-primary/10 text-primary"
                  : "border-border text-muted-foreground hover:text-foreground"
              }`}
            >
              <Mail className="h-4 w-4" />
              <span className="text-sm font-medium">OTP</span>
            </button>
          </div>

          {authType === "otp" ? (
            <OTPLogin
              onBack={() => setAuthType("password")}
              onSuccess={() => router.push("/")}
            />
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <WarningTriangle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Email Address
                </label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  required
                  className="w-full"
                />
              </div>
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Password
                </label>
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                    className="w-full pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    {showPassword ? (
                      <Eye className="h-4 w-4" />
                    ) : (
                      <EyeClosed className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>
              {isSignUp && (
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Full Name (Optional)
                  </label>
                  <Input
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Enter your full name"
                    className="w-full"
                  />
                </div>
              )}
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <div className="flex items-center">
                    <Refresh className="h-4 w-4 mr-2 animate-spin" />
                    {isSignUp ? "Creating Account..." : "Signing In..."}
                  </div>
                ) : (
                  <div className="flex items-center">
                    <Check className="h-4 w-4 mr-2" />
                    {isSignUp ? "Create Account" : "Sign In"}
                  </div>
                )}
              </Button>
            </form>
          )}
          {/* Toggle between login and signup */}
          <div className="mt-6 text-center">
            <button
              type="button"
              onClick={() => {
                setIsSignUp(!isSignUp);
                setError(null);
              }}
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              {isSignUp
                ? "Already have an account? Sign in"
                : "Don't have an account? Create one"}
            </button>
          </div>
          {/* Footer */}
          <div className="text-center text-sm text-muted-foreground">
            <p>Secure authentication powered by HTTP-only cookies</p>
          </div>
        </div>
      </div>
    </div>
  );
}
