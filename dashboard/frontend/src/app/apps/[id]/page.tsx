"use client";

import { AppDetailsView } from "@/components/AppDetailsView";
import { DashboardLayout } from "../../layout/DashboardLayout";
import { App } from "@/lib/api";
import { useParams, useRouter } from "next/navigation";
import { useEffect } from "react";

interface AppDetailContentProps {
  apps: App[];
  selectedApp: App | null;
  user: any;
  onCreateApp: (appData: {
    name: string;
    description?: string;
    domain?: string;
  }) => Promise<App>;
  onSelectApp: (app: App) => void;
  onRefresh: () => void;
}

function AppDetailContent({ 
  apps, 
  selectedApp, 
  user, 
  onCreateApp, 
  onSelectApp, 
  onRefresh 
}: AppDetailContentProps) {
  const params = useParams();
  const router = useRouter();
  const appId = params.id as string;

  useEffect(() => {
    if (apps.length > 0 && appId) {
      const app = apps.find(a => a.id === appId);
      if (app && selectedApp?.id !== app.id) {
        onSelectApp(app);
      } else if (!app) {
        // App not found, redirect to apps list
        router.push('/apps');
      }
    }
  }, [apps, appId, selectedApp, onSelectApp, router]);

  if (!selectedApp) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading app details...</p>
        </div>
      </div>
    );
  }

  return (
    <AppDetailsView
      app={selectedApp}
      onAppUpdated={(updatedApp: App) => {
        // Update the app in the list if needed
        onRefresh();
      }}
    />
  );
}

export default function AppDetailPage() {
  return (
    <DashboardLayout>
      {(props: AppDetailContentProps) => <AppDetailContent {...props} />}
    </DashboardLayout>
  );
} 