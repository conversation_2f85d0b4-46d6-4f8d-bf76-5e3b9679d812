"use client";

import { AppsListView } from "@/components/AppsListView";
import { DashboardLayout } from "../layout/DashboardLayout";
import { useToast } from "@/hooks/use-toast";
import { App } from "@/lib/api";
import { useRouter } from "next/navigation";

interface AppsContentProps {
  apps: App[];
  selectedApp: App | null;
  user: any;
  onCreateApp: (appData: {
    name: string;
    description?: string;
    domain?: string;
  }) => Promise<App>;
  onSelectApp: (app: App) => void;
  onRefresh: () => void;
}

function AppsContent({ 
  apps, 
  selectedApp, 
  user, 
  onCreateApp, 
  onSelectApp, 
  onRefresh 
}: AppsContentProps) {
  const toast = useToast();
  const router = useRouter();

  const handleCreateApp = async (appData: {
    name: string;
    description?: string;
    domain?: string;
  }) => {
    try {
      await onCreateApp(appData);
      toast.success("App created successfully!");
    } catch (error) {
      console.error("Failed to create app:", error);
      toast.error("There was an error creating the application.");
      throw error;
    }
  };

  const handleSelectApp = (app: App) => {
    onSelectApp(app);
    router.push(`/apps/${app.id}`);
  };

  return (
    <AppsListView
      apps={apps}
      onCreateApp={handleCreateApp}
      onSelectApp={handleSelectApp}
      onRefresh={onRefresh}
    />
  );
}

export default function AppsPage() {
  return (
    <DashboardLayout>
      {(props: AppsContentProps) => <AppsContent {...props} />}
    </DashboardLayout>
  );
} 