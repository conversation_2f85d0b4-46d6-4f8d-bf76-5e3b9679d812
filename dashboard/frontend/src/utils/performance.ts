import React from 'react';

/**
 * Performance optimization utilities for React components
 */

/**
 * Creates a memoized component with proper display name
 */
export function createMemoizedComponent<T extends React.ComponentType<any>>(
  Component: T,
  displayName?: string
): React.MemoExoticComponent<T> {
  const MemoizedComponent = React.memo(Component);
  if (displayName) {
    MemoizedComponent.displayName = displayName;
  }
  return MemoizedComponent;
}



/**
 * Debounced callback for performance optimization
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T {
  const timeoutRef = React.useRef<NodeJS.Timeout>();
  
  return React.useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]) as T;
}

/**
 * Throttled callback for performance optimization
 */
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T {
  const lastCallRef = React.useRef(0);
  
  return React.useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCallRef.current >= delay) {
      lastCallRef.current = now;
      callback(...args);
    }
  }, [callback, delay]) as T;
}

/**
 * Memoized list rendering with key optimization
 */
export function useMemoizedList<T>(
  items: T[],
  keyExtractor: (item: T, index: number) => string | number,
  renderItem: (item: T, index: number) => React.ReactNode,
  deps: React.DependencyList = []
): React.ReactNode[] {
  return React.useMemo(() => {
    return items.map((item, index) => 
      React.createElement(React.Fragment, { key: keyExtractor(item, index) }, renderItem(item, index))
    );
  }, [items, keyExtractor, renderItem]);
}

/**
 * Memoized object comparison for deep equality
 */
export function useDeepMemo<T>(
  value: T,
  deps: React.DependencyList = []
): T {
  return React.useMemo(() => value, [value]);
}

/**
 * Performance monitoring hook
 */
export function usePerformanceMonitor(componentName: string) {
  const renderCountRef = React.useRef(0);
  const lastRenderTimeRef = React.useRef(performance.now());
  
  React.useEffect(() => {
    renderCountRef.current += 1;
    const currentTime = performance.now();
    const timeSinceLastRender = currentTime - lastRenderTimeRef.current;
    lastRenderTimeRef.current = currentTime;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${componentName}] Render #${renderCountRef.current} (${timeSinceLastRender.toFixed(2)}ms)`);
    }
  });
  
  return {
    renderCount: renderCountRef.current,
    timeSinceLastRender: performance.now() - lastRenderTimeRef.current,
  };
}

/**
 * Virtual scrolling utilities
 */
export interface VirtualScrollConfig {
  itemHeight: number;
  containerHeight: number;
  overscan?: number; 
}

export function useVirtualScroll<T>(
  items: T[],
  config: VirtualScrollConfig
) {
  const { itemHeight, containerHeight, overscan = 5 } = config;
  const [scrollTop, setScrollTop] = React.useState(0);
  
  const totalHeight = items.length * itemHeight;
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );
  
  const visibleItems = items.slice(startIndex, endIndex);
  const offsetY = startIndex * itemHeight;
  
  return {
    visibleItems,
    offsetY,
    totalHeight,
    startIndex,
    endIndex,
    setScrollTop,
  };
}
