import { toast } from 'sonner';

export const useToast = () => {
  const showToast = {
    success: (message: string, options?: { duration?: number }) => {
      toast.success(message, {
        duration: options?.duration || 4000,
        className: 'modern-toast success',
      });
    },
    error: (message: string, options?: { duration?: number }) => {
      toast.error(message, {
        duration: options?.duration || 6000,
        className: 'modern-toast error',
      });
    },
    info: (message: string, options?: { duration?: number }) => {
      toast.info(message, {
        duration: options?.duration || 4000,
        className: 'modern-toast info',
      });
    },
    warning: (message: string, options?: { duration?: number }) => {
      toast.warning(message, {
        duration: options?.duration || 5000,
        className: 'modern-toast warning',
      });
    },
    loading: (message: string) => {
      return toast.loading(message, {
        className: 'modern-toast loading',
      });
    },
    dismiss: (toastId: string | number) => {
      toast.dismiss(toastId);
    },
  };

  return showToast;
};
