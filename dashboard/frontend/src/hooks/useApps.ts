import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import type { App } from '@/lib/api';

export const appsKeys = {
  all: ['apps'] as const,
  lists: () => [...appsKeys.all, 'list'] as const,
  list: (filters: string) => [...appsKeys.lists(), { filters }] as const,
  details: () => [...appsKeys.all, 'detail'] as const,
  detail: (id: string) => [...appsKeys.details(), id] as const,
};

export function useApps() {
  return useQuery({
    queryKey: appsKeys.lists(),
    queryFn: () => apiClient.getApps(),
    staleTime: 2 * 60 * 1000,
  });
}

export function useApp(appId: string) {
  return useQuery({
    queryKey: appsKeys.detail(appId),
    queryFn: () => apiClient.getApp(appId),
    enabled: !!appId,
    staleTime: 2 * 60 * 1000,
  });
}

export function useCreateApp() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (appData: { name: string; description?: string; domain?: string }) =>
      apiClient.createApp(appData),
    onSuccess: (newApp) => {
      queryClient.invalidateQueries({ queryKey: appsKeys.lists() });
      queryClient.setQueryData(appsKeys.detail(newApp.id), newApp);
    },
  });
}

export function useUpdateApp() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ appId, appData }: { appId: string; appData: Partial<App> }) =>
      apiClient.updateApp(appId, appData),
    onSuccess: (updatedApp, { appId }) => {
      queryClient.setQueryData(appsKeys.detail(appId), updatedApp);
      queryClient.invalidateQueries({ queryKey: appsKeys.lists() });
    },
  });
}

export function useDeleteApp() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (appId: string) => apiClient.deleteApp(appId),
    onSuccess: (_, appId) => {
      queryClient.removeQueries({ queryKey: appsKeys.detail(appId) });
      queryClient.invalidateQueries({ queryKey: appsKeys.lists() });
    },
  });
}
