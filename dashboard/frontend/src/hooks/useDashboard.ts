import { useReducer, useCallback, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { apiClient } from '@/lib/api';
import { App } from '@/lib/api';
import { useAuth, useRequireAuth } from '@/lib/auth-context';

interface DashboardState {
  sidebarOpen: boolean;
  apps: App[];
  selectedApp: App | null;
  isLoading: boolean;
  error: string | null;
}

type DashboardAction =
  | { type: 'SET_SIDEBAR_OPEN'; payload: boolean }
  | { type: 'SET_APPS'; payload: App[] }
  | { type: 'SET_SELECTED_APP'; payload: App | null }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'ADD_APP'; payload: App }
  | { type: 'CLEAR_ERROR' }
  | { type: 'RESET_STATE' };

const initialState: DashboardState = {
  sidebarOpen: false,
  apps: [],
  selectedApp: null,
  isLoading: true,
  error: null,
};

function dashboardReducer(state: DashboardState, action: DashboardAction): DashboardState {
  switch (action.type) {
    case 'SET_SIDEBAR_OPEN':
      return { ...state, sidebarOpen: action.payload };
    
    case 'SET_APPS':
      return { ...state, apps: action.payload };
    
    case 'SET_SELECTED_APP':
      return { ...state, selectedApp: action.payload };
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'ADD_APP':
      return { ...state, apps: [action.payload, ...state.apps] };
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    case 'RESET_STATE':
      return initialState;
    
    default:
      return state;
  }
}

interface UseDashboardReturn {
  sidebarOpen: boolean;
  apps: App[];
  selectedApp: App | null;
  user: any;
  isLoading: boolean;
  error: string | null;
  
  setSidebarOpen: (open: boolean) => void;
  checkAuth: () => Promise<void>;
  loadApps: () => Promise<void>;
  handleCreateApp: (appData: {
    name: string;
    description?: string;
    domain?: string;
  }) => Promise<App>;
  handleSelectApp: (app: App) => void;
  handleBackToApps: () => void;
  handleLogout: () => void;
  clearError: () => void;
}

export function useDashboard(): UseDashboardReturn {
  const router = useRouter();
  const pathname = usePathname();
  const { user, isAuthenticated, isLoading: authLoading, logout: authLogout } = useAuth();
  // Ensure unauthenticated users are redirected; this hook handles it internally
  const { isLoading: requireAuthLoading } = useRequireAuth();
  const [state, dispatch] = useReducer(dashboardReducer, initialState);

  // Clear selectedApp when navigating away from app details
  useEffect(() => {
    if (!pathname.startsWith('/apps/')) {
      dispatch({ type: 'SET_SELECTED_APP', payload: null });
    }
  }, [pathname]);

  const checkAuth = useCallback(async () => {
    // No-op. Authentication is handled globally by AuthProvider and useRequireAuth.
    return;
  }, []);

  const loadApps = useCallback(async () => {
    try {
      const appsData = await apiClient.getApps();
      dispatch({ type: 'SET_APPS', payload: appsData });
      dispatch({ type: 'CLEAR_ERROR' });
    } catch (error) {
      console.error("Failed to load apps:", error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: `Failed to load apps: ${error instanceof Error ? error.message : "Unknown error"}` 
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  const handleCreateApp = useCallback(async (appData: {
    name: string;
    description?: string;
    domain?: string;
  }) => {
    const newApp = await apiClient.createApp(appData);
    dispatch({ type: 'ADD_APP', payload: newApp });
    return newApp;
  }, []);

  const handleSelectApp = useCallback((app: App) => {
    dispatch({ type: 'SET_SELECTED_APP', payload: app });
  }, []);

  const handleBackToApps = useCallback(() => {
    dispatch({ type: 'SET_SELECTED_APP', payload: null });
    router.push('/apps');
  }, [router]);

  const handleLogout = useCallback(() => {
    localStorage.removeItem("tokai_token");
    router.push("/login");
  }, [router]);

  const setSidebarOpen = useCallback((open: boolean) => {
    dispatch({ type: 'SET_SIDEBAR_OPEN', payload: open });
  }, []);

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  // Load apps when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      loadApps();
    }
  }, [isAuthenticated, loadApps]);

  return {
    sidebarOpen: state.sidebarOpen,
    apps: state.apps,
    selectedApp: state.selectedApp,
    user: user,
    isLoading: authLoading || requireAuthLoading || state.isLoading,
    error: state.error,
    
    setSidebarOpen,
    checkAuth,
    loadApps,
    handleCreateApp,
    handleSelectApp,
    handleBackToApps,
    handleLogout,
    clearError,
  };
}

