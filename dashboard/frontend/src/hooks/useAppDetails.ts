import { useReducer, useCallback, useEffect } from 'react';
import { apiClient } from '@/lib/api';
import { Wallet, ApiKey, App } from '@/lib/api';

// Types
interface AppDetailsState {
  wallets: Wallet[];
  apiKeys: Api<PERSON>ey[];
  isLoading: boolean;
  error: string | null;
}

// Action types
type AppDetailsAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_WALLETS'; payload: Wallet[] }
  | { type: 'SET_API_KEYS'; payload: ApiKey[] }
  | { type: 'SET_DATA'; payload: { wallets: Wallet[]; apiKeys: ApiKey[] } }
  | { type: 'CLEAR_ERROR' }
  | { type: 'RESET_STATE' };

// Initial state
const initialState: AppDetailsState = {
  wallets: [],
  apiKeys: [],
  isLoading: true,
  error: null,
};

// Reducer function
function appDetailsReducer(state: AppDetailsState, action: AppDetailsAction): AppDetailsState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_WALLETS':
      return { ...state, wallets: action.payload };
    
    case 'SET_API_KEYS':
      return { ...state, apiKeys: action.payload };
    
    case 'SET_DATA':
      return { 
        ...state, 
        wallets: action.payload.wallets, 
        apiKeys: action.payload.apiKeys 
      };
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    case 'RESET_STATE':
      return initialState;
    
    default:
      return state;
  }
}

interface UseAppDetailsReturn {
  // State
  wallets: Wallet[];
  apiKeys: ApiKey[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  loadData: () => Promise<void>;
  refreshWallets: () => Promise<void>;
  refreshApiKeys: () => Promise<void>;
  clearError: () => void;
}

export function useAppDetails(app: App): UseAppDetailsReturn {
  const [state, dispatch] = useReducer(appDetailsReducer, initialState);

  const loadData = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const [walletsData, apiKeysData] = await Promise.all([
        apiClient.getAppWallets(app.id),
        apiClient.getAppApiKeys(app.id),
      ]);
      dispatch({ 
        type: 'SET_DATA', 
        payload: { wallets: walletsData, apiKeys: apiKeysData } 
      });
      dispatch({ type: 'CLEAR_ERROR' });
    } catch (error) {
      console.error("Failed to load app data:", error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: `Failed to load data: ${error instanceof Error ? error.message : "Unknown error"}` 
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [app.id]);

  const refreshWallets = useCallback(async () => {
    try {
      const walletsData = await apiClient.getAppWallets(app.id);
      dispatch({ type: 'SET_WALLETS', payload: walletsData });
    } catch (error) {
      console.error("Failed to refresh wallets:", error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: `Failed to refresh wallets: ${error instanceof Error ? error.message : "Unknown error"}` 
      });
    }
  }, [app.id]);

  const refreshApiKeys = useCallback(async () => {
    try {
      const apiKeysData = await apiClient.getAppApiKeys(app.id);
      dispatch({ type: 'SET_API_KEYS', payload: apiKeysData });
    } catch (error) {
      console.error("Failed to refresh API keys:", error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: `Failed to refresh API keys: ${error instanceof Error ? error.message : "Unknown error"}` 
      });
    }
  }, [app.id]);

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    // State
    wallets: state.wallets,
    apiKeys: state.apiKeys,
    isLoading: state.isLoading,
    error: state.error,
    
    // Actions
    loadData,
    refreshWallets,
    refreshApiKeys,
    clearError,
  };
}

