import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';

export const analyticsKeys = {
  all: ['analytics'] as const,
  dashboard: () => [...analyticsKeys.all, 'dashboard'] as const,
  user: () => [...analyticsKeys.all, 'user'] as const,
  wallet: () => [...analyticsKeys.all, 'wallet'] as const,
  transaction: () => [...analyticsKeys.all, 'transaction'] as const,
  security: () => [...analyticsKeys.all, 'security'] as const,
  app: () => [...analyticsKeys.all, 'app'] as const,
  system: () => [...analyticsKeys.all, 'system'] as const,
};

export function useDashboardMetrics() {
  return useQuery({
    queryKey: analyticsKeys.dashboard(),
    queryFn: () => apiClient.getDashboardMetrics(),
    staleTime: 2 * 60 * 1000,
    refetchInterval: 5 * 60 * 1000,
  });
}

export function useUserAnalytics() {
  return useQuery({
    queryKey: analyticsKeys.user(),
    queryFn: () => apiClient.getUserAnalytics(),
    staleTime: 5 * 60 * 1000,
  });
}

export function useWalletAnalytics() {
  return useQuery({
    queryKey: analyticsKeys.wallet(),
    queryFn: () => apiClient.getWalletAnalytics(),
    staleTime: 2 * 60 * 1000,
  });
}

export function useTransactionAnalytics() {
  return useQuery({
    queryKey: analyticsKeys.transaction(),
    queryFn: () => apiClient.getTransactionAnalytics(),
    staleTime: 1 * 60 * 1000,
  });
}

export function useSecurityAnalytics() {
  return useQuery({
    queryKey: analyticsKeys.security(),
    queryFn: () => apiClient.getSecurityAnalytics(),
    staleTime: 5 * 60 * 1000,
  });
}

export function useAppAnalytics() {
  return useQuery({
    queryKey: analyticsKeys.app(),
    queryFn: () => apiClient.getAppAnalytics(),
    staleTime: 2 * 60 * 1000,
  });
}

export function useSystemHealth() {
  return useQuery({
    queryKey: analyticsKeys.system(),
    queryFn: () => apiClient.getSystemHealth(),
    staleTime: 30 * 1000,
    refetchInterval: 60 * 1000,
  });
}
