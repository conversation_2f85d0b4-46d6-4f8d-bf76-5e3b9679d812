import { useReducer, useCallback } from 'react';

// Types
interface AsyncState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
}

// Action types
type AsyncAction<T> =
  | { type: 'SET_DATA'; payload: T | null }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'RESET' }
  | { type: 'CLEAR_ERROR' };

// Initial state
function createInitialState<T>(initialData: T | null = null): AsyncState<T> {
  return {
    data: initialData,
    isLoading: false,
    error: null,
  };
}

// Reducer function
function asyncReducer<T>(state: AsyncState<T>, action: AsyncAction<T>): AsyncState<T> {
  switch (action.type) {
    case 'SET_DATA':
      return { ...state, data: action.payload, error: null };
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'RESET':
      return createInitialState<T>(state.data);
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    default:
      return state;
  }
}

interface UseAsyncStateReturn<T> extends AsyncState<T> {
  setData: (data: T | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  reset: () => void;
  execute: (asyncFn: () => Promise<T>) => Promise<T | null>;
}

export function useAsyncState<T>(initialData: T | null = null): UseAsyncStateReturn<T> {
  const [state, dispatch] = useReducer(asyncReducer<T>, createInitialState(initialData));

  const setData = useCallback((data: T | null) => {
    dispatch({ type: 'SET_DATA', payload: data });
  }, []);

  const setLoading = useCallback((isLoading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: isLoading });
  }, []);

  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, []);

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  const reset = useCallback(() => {
    dispatch({ type: 'RESET' });
  }, []);

  const execute = useCallback(async (asyncFn: () => Promise<T>): Promise<T | null> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      const result = await asyncFn();
      dispatch({ type: 'SET_DATA', payload: result });
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      return null;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  return {
    ...state,
    setData,
    setLoading,
    setError,
    clearError,
    reset,
    execute,
  };
}

