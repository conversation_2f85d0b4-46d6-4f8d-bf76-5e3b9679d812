import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import type { Wallet } from '@/lib/api';

export const walletsKeys = {
  all: ['wallets'] as const,
  lists: () => [...walletsKeys.all, 'list'] as const,
  list: (appId: string) => [...walletsKeys.lists(), appId] as const,
  details: () => [...walletsKeys.all, 'detail'] as const,
  detail: (id: string) => [...walletsKeys.details(), id] as const,
};

export function useWallets(appId: string) {
  return useQuery({
    queryKey: walletsKeys.list(appId),
    queryFn: () => apiClient.getAppWallets(appId),
    enabled: !!appId,
    staleTime: 1 * 60 * 1000,
  });
}

export function useWallet(walletId: string) {
  return useQuery({
    queryKey: walletsKeys.detail(walletId),
    queryFn: () => apiClient.getWallets().then(wallets => wallets.find(wallet => wallet.id === walletId)),
    enabled: !!walletId,
    staleTime: 1 * 60 * 1000,
  });
}

export function useCreateWallet() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ appId, network }: { appId: string; network?: string }) =>
      apiClient.createAppWallet(appId, { network }),
    onSuccess: (newWallet, { appId }) => {
      queryClient.invalidateQueries({ queryKey: walletsKeys.list(appId) });
      queryClient.setQueryData(walletsKeys.detail(newWallet.id), newWallet);
    },
  });
}

export function useDeleteWallet() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ appId, walletId }: { appId: string; walletId: string }) =>
      apiClient.deleteAppWallet(appId, walletId),
    onSuccess: (_, { appId, walletId }) => {
      queryClient.removeQueries({ queryKey: walletsKeys.detail(walletId) });
      queryClient.invalidateQueries({ queryKey: walletsKeys.list(appId) });
    },
  });
}

export function useWalletBalance(walletId: string) {
  return useQuery({
    queryKey: [...walletsKeys.detail(walletId), 'balance'],
    queryFn: () => Promise.resolve({ balance: '0', currency: 'USD' }),
    enabled: !!walletId,
    staleTime: 30 * 1000,
    refetchInterval: 60 * 1000,
  });
}
