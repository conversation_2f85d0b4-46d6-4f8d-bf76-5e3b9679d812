import { useReducer, useCallback, useEffect } from 'react';
import { apiClient } from '@/lib/api';
import { SessionKey, SessionKeyUsage, CreateSessionKeyRequest } from '@/lib/api';

// Types
interface SessionKeysState {
  sessionKeys: SessionKey[];
  selectedKey: SessionKey | null;
  keyUsage: SessionKeyUsage[];
  isLoading: boolean;
  error: string | null;
  showCreateDialog: boolean;
  showRawKey: string | null;
  copiedKey: string | null;
  createForm: {
    keyName: string;
    permissions: string[];
    scope: string;
    expiryDuration: number;
    metadata: string;
  };
}

// Action types
type SessionKeysAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_SESSION_KEYS'; payload: SessionKey[] }
  | { type: 'SET_SELECTED_KEY'; payload: SessionKey | null }
  | { type: 'SET_KEY_USAGE'; payload: SessionKeyUsage[] }
  | { type: 'SET_SHOW_CREATE_DIALOG'; payload: boolean }
  | { type: 'SET_SHOW_RAW_KEY'; payload: string | null }
  | { type: 'SET_COPIED_KEY'; payload: string | null }
  | { type: 'UPDATE_CREATE_FORM'; payload: Partial<SessionKeysState['createForm']> }
  | { type: 'RESET_CREATE_FORM' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'RESET_STATE' };

// Initial state
const initialState: SessionKeysState = {
  sessionKeys: [],
  selectedKey: null,
  keyUsage: [],
  isLoading: true,
  error: null,
  showCreateDialog: false,
  showRawKey: null,
  copiedKey: null,
  createForm: {
    keyName: "",
    permissions: [],
    scope: "",
    expiryDuration: 86400000, // 24 hours default
    metadata: "",
  },
};

// Reducer function
function sessionKeysReducer(state: SessionKeysState, action: SessionKeysAction): SessionKeysState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_SESSION_KEYS':
      return { ...state, sessionKeys: action.payload };
    
    case 'SET_SELECTED_KEY':
      return { ...state, selectedKey: action.payload };
    
    case 'SET_KEY_USAGE':
      return { ...state, keyUsage: action.payload };
    
    case 'SET_SHOW_CREATE_DIALOG':
      return { ...state, showCreateDialog: action.payload };
    
    case 'SET_SHOW_RAW_KEY':
      return { ...state, showRawKey: action.payload };
    
    case 'SET_COPIED_KEY':
      return { ...state, copiedKey: action.payload };
    
    case 'UPDATE_CREATE_FORM':
      return {
        ...state,
        createForm: { ...state.createForm, ...action.payload }
      };
    
    case 'RESET_CREATE_FORM':
      return {
        ...state,
        createForm: initialState.createForm
      };
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    case 'RESET_STATE':
      return initialState;
    
    default:
      return state;
  }
}

interface UseSessionKeysReturn {
  // State
  sessionKeys: SessionKey[];
  selectedKey: SessionKey | null;
  keyUsage: SessionKeyUsage[];
  isLoading: boolean;
  error: string | null;
  showCreateDialog: boolean;
  showRawKey: string | null;
  copiedKey: string | null;
  createForm: SessionKeysState['createForm'];
  
  // Actions
  loadSessionKeys: () => Promise<void>;
  loadKeyUsage: (keyId: string) => Promise<void>;
  handleCreateKey: () => Promise<void>;
  handleRevokeKey: (keyId: string) => Promise<void>;
  handleCopyKey: (key: string) => Promise<void>;
  handlePermissionToggle: (permissionId: string) => void;
  setSelectedKey: (key: SessionKey | null) => void;
  setShowCreateDialog: (show: boolean) => void;
  setShowRawKey: (key: string | null) => void;
  setCopiedKey: (key: string | null) => void;
  updateCreateForm: (updates: Partial<SessionKeysState['createForm']>) => void;
  resetCreateForm: () => void;
  clearError: () => void;
}

export function useSessionKeys(): UseSessionKeysReturn {
  const [state, dispatch] = useReducer(sessionKeysReducer, initialState);

  const loadSessionKeys = useCallback(async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      const keys = await apiClient.getSessionKeys();
      dispatch({ type: 'SET_SESSION_KEYS', payload: keys });
    } catch (err) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: err instanceof Error ? err.message : "Failed to load session keys" 
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  const loadKeyUsage = useCallback(async (keyId: string) => {
    try {
      const usage = await apiClient.getSessionKeyUsage(keyId);
      dispatch({ type: 'SET_KEY_USAGE', payload: usage });
    } catch (err) {
      console.error("Failed to load key usage:", err);
    }
  }, []);

  const handleCreateKey = useCallback(async () => {
    try {
      const request: CreateSessionKeyRequest = {
        keyName: state.createForm.keyName,
        permissions: state.createForm.permissions,
        scope: state.createForm.scope || undefined,
        expiryDuration: state.createForm.expiryDuration,
        metadata: state.createForm.metadata ? JSON.parse(state.createForm.metadata) : undefined,
      };

      const response = await apiClient.createSessionKey(request);
      
      // Show the raw key to the user
      dispatch({ type: 'SET_SHOW_RAW_KEY', payload: response.rawKey });
      
      // Reset form
      dispatch({ type: 'RESET_CREATE_FORM' });
      
      // Reload keys
      await loadSessionKeys();
    } catch (err) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: err instanceof Error ? err.message : "Failed to create session key" 
      });
    }
  }, [state.createForm, loadSessionKeys]);

  const handleRevokeKey = useCallback(async (keyId: string) => {
    if (!confirm("Are you sure you want to revoke this session key? This action cannot be undone.")) {
      return;
    }

    try {
      await apiClient.revokeSessionKey(keyId);
      await loadSessionKeys();
      if (state.selectedKey?.id === keyId) {
        dispatch({ type: 'SET_SELECTED_KEY', payload: null });
        dispatch({ type: 'SET_KEY_USAGE', payload: [] });
      }
    } catch (err) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: err instanceof Error ? err.message : "Failed to revoke session key" 
      });
    }
  }, [state.selectedKey, loadSessionKeys]);

  const handleCopyKey = useCallback(async (key: string) => {
    try {
      await navigator.clipboard.writeText(key);
      dispatch({ type: 'SET_COPIED_KEY', payload: key });
      setTimeout(() => dispatch({ type: 'SET_COPIED_KEY', payload: null }), 2000);
    } catch (err) {
      console.error("Failed to copy key:", err);
    }
  }, []);

  const handlePermissionToggle = useCallback((permissionId: string) => {
    const currentPermissions = state.createForm.permissions;
    const newPermissions = currentPermissions.includes(permissionId)
      ? currentPermissions.filter(p => p !== permissionId)
      : [...currentPermissions, permissionId];
    
    dispatch({ 
      type: 'UPDATE_CREATE_FORM', 
      payload: { permissions: newPermissions } 
    });
  }, [state.createForm.permissions]);

  const setSelectedKey = useCallback((key: SessionKey | null) => {
    dispatch({ type: 'SET_SELECTED_KEY', payload: key });
  }, []);

  const setShowCreateDialog = useCallback((show: boolean) => {
    dispatch({ type: 'SET_SHOW_CREATE_DIALOG', payload: show });
  }, []);

  const setShowRawKey = useCallback((key: string | null) => {
    dispatch({ type: 'SET_SHOW_RAW_KEY', payload: key });
  }, []);

  const setCopiedKey = useCallback((key: string | null) => {
    dispatch({ type: 'SET_COPIED_KEY', payload: key });
  }, []);

  const updateCreateForm = useCallback((updates: Partial<SessionKeysState['createForm']>) => {
    dispatch({ type: 'UPDATE_CREATE_FORM', payload: updates });
  }, []);

  const resetCreateForm = useCallback(() => {
    dispatch({ type: 'RESET_CREATE_FORM' });
  }, []);

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  return {
    // State
    sessionKeys: state.sessionKeys,
    selectedKey: state.selectedKey,
    keyUsage: state.keyUsage,
    isLoading: state.isLoading,
    error: state.error,
    showCreateDialog: state.showCreateDialog,
    showRawKey: state.showRawKey,
    copiedKey: state.copiedKey,
    createForm: state.createForm,
    
    // Actions
    loadSessionKeys,
    loadKeyUsage,
    handleCreateKey,
    handleRevokeKey,
    handleCopyKey,
    handlePermissionToggle,
    setSelectedKey,
    setShowCreateDialog,
    setShowRawKey,
    setCopiedKey,
    updateCreateForm,
    resetCreateForm,
    clearError,
  };
}

