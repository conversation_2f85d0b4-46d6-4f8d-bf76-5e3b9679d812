import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import type { ApiKey } from '@/lib/api';

export const apiKeysKeys = {
  all: ['apiKeys'] as const,
  lists: () => [...apiKeysKeys.all, 'list'] as const,
  list: (appId: string) => [...apiKeysKeys.lists(), appId] as const,
  details: () => [...apiKeysKeys.all, 'detail'] as const,
  detail: (id: string) => [...apiKeysKeys.details(), id] as const,
};

export function useApiKeys(appId: string) {
  return useQuery({
    queryKey: apiKeysKeys.list(appId),
    queryFn: () => apiClient.getAppApiKeys(appId),
    enabled: !!appId,
    staleTime: 5 * 60 * 1000,
  });
}`1`

export function useApiKey(apiKeyId: string) {
  return useQuery({
    queryKey: apiKeysKeys.detail(apiKeyId),
    queryFn: () => apiClient.getApiKey(apiKeyId),
    enabled: !!apiKeyId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateApiKey() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ appId, name, permissions }: { appId: string; name: string; permissions: string }) =>
      apiClient.createAppApiKey(appId, { name, permissions }),
    onSuccess: (newApiKey, { appId }) => {
      queryClient.invalidateQueries({ queryKey: apiKeysKeys.list(appId) });
      queryClient.setQueryData(apiKeysKeys.detail(newApiKey.id), newApiKey);
    },
  });
}

export function useDeleteApiKey() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ appId, apiKeyId }: { appId: string; apiKeyId: string }) =>
      apiClient.deleteAppApiKey(appId, apiKeyId),
    onSuccess: (_, { appId, apiKeyId }) => {
      queryClient.removeQueries({ queryKey: apiKeysKeys.detail(apiKeyId) });
      queryClient.invalidateQueries({ queryKey: apiKeysKeys.list(appId) });
    },
  });
}

export function useRegenerateApiKey() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ appId, apiKeyId }: { appId: string; apiKeyId: string }) =>
      apiClient.regenerateApiKey(appId, apiKeyId),
    onSuccess: (updatedApiKey, { appId, apiKeyId }) => {
      queryClient.setQueryData(apiKeysKeys.detail(apiKeyId), updatedApiKey);
      queryClient.invalidateQueries({ queryKey: apiKeysKeys.list(appId) });
    },
  });
}
