import axios, { AxiosInstance } from 'axios';

export interface User {
  id: string;
  email: string;
  full_name?: string;
  email_verified?: boolean;
  email_verified_at?: string;
  created_at: string;
}

export interface SessionInfo {
  id: string;
  deviceInfo?: string;
  ipAddress?: string;
  lastUsedAt: string;
  createdAt: string;
  isCurrent: boolean;
}

export interface Wallet {
  id: string;
  user_id: string;
  network: string;
  wallet_type: string;
  wallet_address: string;
  public_key: string;
  derivation_path: string;
  is_active: boolean;
  metadata: string;
  created_at: string;
  updated_at: string;
}

export interface ApiKey {
  id: string;
  name: string;
  permissions: string;
  is_active: boolean;
  last_used: string | null;
  created_at: string;
  updated_at: string;
}

export interface CreateApiKeyRequest {
  name: string;
  permissions?: string;
}

export interface CreateApiKeyResponse {
  id: string;
  name: string;
  permissions: string;
  api_key: string;
  created_at: string;
}

export interface App {
  id: string;
  name: string;
  description?: string;
  api_key_preview?: string;
  domain?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface CreateAppRequest {
  name: string;
  description?: string;
  domain?: string;
}

// Analytics interfaces
export interface DashboardMetrics {
  totalUsers: number;
  totalWallets: number;
  totalApps: number;
  totalTransactions: number;
  activeUsers: number;
  newUsersThisMonth: number;
  walletCreationTrend: Array<{ date: string; count: number }>;
  userGrowthTrend: Array<{ date: string; count: number }>;
}

export interface UserAnalytics {
  totalUsers: number;
  activeUsers: number;
  newUsersThisMonth: number;
  userGrowthRate: number;
  topCountries: Array<{ country: string; count: number }>;
  userActivityByDay: Array<{ date: string; activeUsers: number }>;
}

export interface WalletAnalytics {
  totalWallets: number;
  walletsByNetwork: Array<{ network: string; count: number }>;
  walletCreationTrend: Array<{ date: string; count: number }>;
  activeWallets: number;
  walletTypes: Array<{ type: string; count: number }>;
}

export interface SecurityAnalytics {
  mfaEnabledUsers: number;
  failedLoginAttempts: number;
  suspiciousActivityCount: number;
  securityScore: number;
  mfaUsageByType: Array<{ type: string; count: number }>;
  loginAttemptsByDay: Array<{ date: string; attempts: number; failed: number }>;
}

export interface AppAnalytics {
  totalApps: number;
  activeApps: number;
  appsByStatus: Array<{ status: string; count: number }>;
  appCreationTrend: Array<{ date: string; count: number }>;
  topAppsByUsage: Array<{ appName: string; usage: number }>;
}

export interface TransactionAnalytics {
  totalTransactions: number;
  transactionTrend: Array<{ date: string; count: number }>;
  transactionsByNetwork: Array<{ network: string; count: number }>;
  averageTransactionValue: number;
  failedTransactions: number;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'down';
  uptime: number;
  responseTime: number;
  activeConnections: number;
  memoryUsage: number;
  cpuUsage: number;
  diskUsage: number;
}

// Session Keys interfaces
export interface SessionKey {
  id: string;
  keyName: string;
  permissions: string[];
  scope?: string;
  expiresAt: string;
  createdAt: string;
  lastUsedAt?: string;
  isActive: boolean;
  metadata?: Record<string, any>;
}

export interface CreateSessionKeyRequest {
  keyName: string;
  permissions: string[];
  scope?: string;
  expiryDuration?: number;
  metadata?: Record<string, any>;
}

export interface CreateSessionKeyResponse {
  sessionKey: SessionKey;
  rawKey: string;
}

export interface SessionKeyUsage {
  id: string;
  sessionKeyId: string;
  endpoint: string;
  method: string;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  success: boolean;
}

export class ApiClient {
  private client: AxiosInstance;
  private refreshClient: AxiosInstance;
  private isRefreshing = false;
  private refreshPromise: Promise<any> | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',
      timeout: 10000,
      withCredentials: true, // Important for cookies
    });

    // Separate bare client (no interceptors) for refresh to avoid loops
    this.refreshClient = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',
      timeout: 10000,
      withCredentials: true,
    });

    // Interceptor
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401) {
          const reqUrl: string = originalRequest?.url || '';
          const isRefreshCall = reqUrl.includes('/auth/refresh');

          // If refresh itself failed, don't loop – redirect to login
          if (isRefreshCall) {
            if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
              window.location.href = '/login';
            }
            return Promise.reject(error);
          }

          if (!originalRequest._retry) {
            originalRequest._retry = true;

            try {
              if (this.isRefreshing && this.refreshPromise) {
                await this.refreshPromise;
              } else {
                this.isRefreshing = true;
                this.refreshPromise = this.refreshClient.post('/auth/refresh');
                await this.refreshPromise;
              }
              return this.client(originalRequest);
            } catch (refreshError) {
              if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
                window.location.href = '/login';
              }
              return Promise.reject(refreshError);
            } finally {
              this.isRefreshing = false;
              this.refreshPromise = null;
            }
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Authentication methods
  async login(email: string, password: string): Promise<{ user: User }> {
    const response = await this.client.post('/auth/login', {
      email,
      password,
    });
    
    return {
      user: response.data.data.user,
    };
  }

  async register(email: string, password: string, fullName?: string): Promise<{ user: User; data?: any }> {
    const response = await this.client.post('/auth/register', {
      email,
      password,
      full_name: fullName,
    });
    
    return {
      user: response.data.data.user,
      data: response.data.data,
    };
  }

  async logout(): Promise<void> {
    await this.client.post('/auth/logout');
  }

  async logoutAll(): Promise<void> {
    await this.client.post('/auth/logout-all');
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.client.get('/auth/me');
    return response.data.data.user;
  }

  // Email verification methods
  async sendVerificationEmail(email: string): Promise<void> {
    await this.client.post('/auth/send-verification-email', { email });
  }

  async verifyEmail(token: string): Promise<{ user: User }> {
    const response = await this.client.post('/auth/verify-email', { token });
    return response.data;
  }

  // OTP authentication methods
  async sendOTP(email: string): Promise<{ isNewUser: boolean }> {
    const response = await this.client.post('/auth/send-otp', { email });
    return response.data;
  }

  async verifyOTP(email: string, otp: string): Promise<{ 
    user: User; 
    auto_created_wallet?: {
      id: string;
      network: string;
      wallet_address: string;
      is_embedded: boolean;
    };
  }> {
    const response = await this.client.post('/auth/verify-otp', { email, otp });
    return response.data;
  }

  // App authentication configuration
  async getAppAuthConfig(appId: string): Promise<{
    app: { id: string; name: string };
    auth_config: {
      auth_type: 'password' | 'otp' | 'both';
      email_otp_enabled: boolean;
      google_auth_enabled: boolean;
      email_verification_required: boolean;
    };
  }> {
    const response = await this.client.get(`/app-settings/${appId}/auth-config`);
    return response.data.data;
  }

  async getUserSessions(): Promise<SessionInfo[]> {
    const response = await this.client.get('/auth/sessions');
    return response.data.data.sessions;
  }

  async invalidateSession(sessionId: string): Promise<void> {
    await this.client.delete(`/auth/sessions/${sessionId}`);
  }

  async getSessionStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
    oldestSession: string | null;
    newestSession: string | null;
  }> {
    const response = await this.client.get('/auth/session-stats');
    return response.data.data.stats;
  }

  // Wallets
  async getWallets(): Promise<Wallet[]> {
    const response = await this.client.get('/wallets');
    return response.data.data;
  }

  async createWallet(network?: string): Promise<Wallet> {
    const response = await this.client.post('/wallets', { network });
    return response.data.data;
  }

  // API Keys
  async getApiKeys(): Promise<ApiKey[]> {
    const response = await this.client.get('/api-keys');
    return response.data.data;
  }

  async createApiKey(request: CreateApiKeyRequest): Promise<CreateApiKeyResponse> {
    const response = await this.client.post('/api-keys', request);
    return response.data.data;
  }

  async deleteApiKey(id: string): Promise<void> {
    await this.client.delete(`/api-keys/${id}`);
  }

  async updateApiKey(id: string, updates: Partial<ApiKey>): Promise<void> {
    await this.client.put(`/api-keys/${id}`, updates);
  }

  // Apps
  async getApps(): Promise<App[]> {
    const response = await this.client.get('/apps');
    return response.data.data;
  }

  async createApp(request: CreateAppRequest): Promise<App> {
    const response = await this.client.post('/apps', request);
    return response.data.data;
  }

  async getApp(appId: string): Promise<App> {
    const response = await this.client.get(`/apps/${appId}`);
    return response.data.data;
  }

  async updateApp(appId: string, updates: Partial<App>): Promise<void> {
    await this.client.put(`/apps/${appId}`, updates);
  }

  async deleteApp(appId: string): Promise<void> {
    await this.client.delete(`/apps/${appId}`);
  }

  async regenerateAppKey(appId: string): Promise<any> {
    const response = await this.client.post(`/apps/${appId}/regenerate-key`);
    return response.data.data;
  }

  async regenerateApiKey(appId: string, apiKeyId: string): Promise<CreateApiKeyResponse> {
    const response = await this.client.post(`/apps/${appId}/api-keys/${apiKeyId}/regenerate`);
    return response.data.data;
  }

  async getApiKey(apiKeyId: string): Promise<ApiKey> {
    const response = await this.client.get(`/api-keys/${apiKeyId}`);
    return response.data.data;
  }

  // App Domains (Allowed Origins)
  async getAppDomains(appId: string): Promise<Array<{ id: string; origin: string; created_at: string }>> {
    const response = await this.client.get(`/apps/${appId}/domains`);
    return response.data.data;
  }

  async addAppDomain(appId: string, origin: string): Promise<{ id: string; origin: string; created_at: string }>
  {
    const response = await this.client.post(`/apps/${appId}/domains`, { origin });
    return response.data.data;
  }

  async deleteAppDomain(appId: string, domainId: string): Promise<void> {
    await this.client.delete(`/apps/${appId}/domains/${domainId}`);
  }

  async getAppSettings(appId: string): Promise<any> {
    const response = await this.client.get(`/app-settings/${appId}`);
    return response.data.data.settings;
  }

  async updateAppSettings(appId: string, settings: any): Promise<void> {
    await this.client.put(`/app-settings/${appId}`, settings);
  }

  async getAppWallets(appId: string): Promise<Wallet[]> {
    const response = await this.client.get(`/apps/${appId}/wallets`);
    return response.data.data;
  }

  async createAppWallet(appId: string, wallet: { network?: string; endUserId?: string; walletType?: string }): Promise<Wallet> {
    const response = await this.client.post(`/apps/${appId}/wallets`, wallet);
    return response.data.data;
  }

  async getAppWallet(appId: string, walletId: string): Promise<Wallet> {
    const response = await this.client.get(`/apps/${appId}/wallets/${walletId}`);
    return response.data.data;
  }

  async deleteAppWallet(appId: string, walletId: string): Promise<void> {
    await this.client.delete(`/apps/${appId}/wallets/${walletId}`);
  }

  async getAppApiKeys(appId: string): Promise<ApiKey[]> {
    const response = await this.client.get(`/apps/${appId}/api-keys`);
    return response.data.data;
  }

  async createAppApiKey(appId: string, apiKey: { name: string; permissions?: string }): Promise<CreateApiKeyResponse> {
    const response = await this.client.post(`/apps/${appId}/api-keys`, apiKey);
    return response.data.data;
  }

  async updateAppApiKey(appId: string, keyId: string, updates: Partial<ApiKey>): Promise<void> {
    await this.client.put(`/apps/${appId}/api-keys/${keyId}`, updates);
  }

  async deleteAppApiKey(appId: string, keyId: string): Promise<void> {
    await this.client.delete(`/apps/${appId}/api-keys/${keyId}`);
  }

  // Settings
  async getSettings(): Promise<any> {
    const response = await this.client.get('/settings');
    return response.data.data;
  }

  async updateSettings(settings: any): Promise<void> {
    await this.client.put('/settings', settings);
  }

  // Analytics
  async getDashboardMetrics(): Promise<DashboardMetrics> {
    const response = await this.client.get('/analytics/dashboard');
    return response.data.data;
  }

  async getUserAnalytics(): Promise<UserAnalytics> {
    const response = await this.client.get('/analytics/users');
    return response.data.data;
  }

  async getWalletAnalytics(): Promise<WalletAnalytics> {
    const response = await this.client.get('/analytics/wallets');
    const data = response.data.data as any;
    // Normalize shape to array to be resilient to backend changes
    if (!Array.isArray(data.walletsByNetwork) && data.walletsByNetwork && typeof data.walletsByNetwork === 'object') {
      data.walletsByNetwork = Object.entries(data.walletsByNetwork).map(([network, count]) => ({ network, count }));
    }
    return data as WalletAnalytics;
  }

  async getSecurityAnalytics(): Promise<SecurityAnalytics> {
    const response = await this.client.get('/analytics/security');
    return response.data.data;
  }

  async getAppAnalytics(): Promise<AppAnalytics> {
    const response = await this.client.get('/analytics/apps');
    return response.data.data;
  }

  async getTransactionAnalytics(): Promise<TransactionAnalytics> {
    const response = await this.client.get('/analytics/transactions');
    return response.data.data;
  }

  async getSystemHealth(): Promise<SystemHealth> {
    const response = await this.client.get('/analytics/system-health');
    return response.data.data;
  }

  async getRealtimeMetrics(): Promise<any> {
    const response = await this.client.get('/analytics/realtime');
    return response.data.data;
  }

  // Session Keys
  async createSessionKey(request: CreateSessionKeyRequest): Promise<CreateSessionKeyResponse> {
    const response = await this.client.post('/session-keys', request);
    return response.data.data;
  }

  async getSessionKeys(includeRevoked?: boolean): Promise<SessionKey[]> {
    const response = await this.client.get('/session-keys', {
      params: { includeRevoked }
    });
    const data = response.data?.data;
    if (Array.isArray(data)) return data as SessionKey[];
    if (Array.isArray(data?.sessionKeys)) return data.sessionKeys as SessionKey[];
    return [];
  }

  async getSessionKey(keyId: string): Promise<SessionKey> {
    const response = await this.client.get(`/session-keys/${keyId}`);
    return response.data.data;
  }

  async updateSessionKey(keyId: string, updates: Partial<SessionKey>): Promise<void> {
    await this.client.put(`/session-keys/${keyId}`, updates);
  }

  async revokeSessionKey(keyId: string): Promise<void> {
    await this.client.delete(`/session-keys/${keyId}`);
  }

  async getSessionKeyUsage(keyId: string): Promise<SessionKeyUsage[]> {
    const response = await this.client.get(`/session-keys/${keyId}/usage`);
    const data = response.data?.data;
    if (Array.isArray(data)) return data as SessionKeyUsage[];
    if (Array.isArray(data?.usage)) return data.usage as SessionKeyUsage[];
    return [];
  }

  async getSessionKeyAuditLog(keyId: string): Promise<any[]> {
    const response = await this.client.get(`/session-keys/${keyId}/audit`);
    return response.data.data;
  }
}

export const apiClient = new ApiClient(); 