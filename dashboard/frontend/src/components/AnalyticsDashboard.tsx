"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  apiClient,
  AppAnalytics,
  DashboardMetrics,
  SecurityAnalytics,
  UserAnalytics,
  WalletAnalytics,
} from "@/lib/api";
import {
  Activity,
  Shield,
  StatsDownSquare,
  StatsReport,
  StatsUpSquare,
  Wallet,
  WarningTriangle,
} from "iconoir-react";
import { useEffect, useState } from "react";

interface AnalyticsDashboardProps {
  className?: string;
}

export function AnalyticsDashboard({ className }: AnalyticsDashboardProps) {
  const [dashboardMetrics, setDashboardMetrics] =
    useState<DashboardMetrics | null>(null);
  const [userAnalytics, setUserAnalytics] = useState<UserAnalytics | null>(
    null
  );
  const [walletAnalytics, setWalletAnalytics] = useState<WalletAnalytics>();
  const [securityAnalytics, setSecurityAnalytics] =
    useState<SecurityAnalytics | null>(null);
  const [appAnalytics, setAppAnalytics] = useState<AppAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAnalytics();
  }, []);

  const loadAnalytics = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [dashboard, users, wallets, security, apps] = await Promise.all([
        apiClient.getDashboardMetrics(),
        apiClient.getUserAnalytics(),
        apiClient.getWalletAnalytics(),
        apiClient.getSecurityAnalytics(),
        apiClient.getAppAnalytics(),
      ]);

      setDashboardMetrics(dashboard);
      setUserAnalytics(users);
      setWalletAnalytics(wallets);
      setSecurityAnalytics(security);
      setAppAnalytics(apps);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load analytics");
    } finally {
      setIsLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  const getGrowthIndicator = (current: number, previous: number) => {
    const growth = ((current - previous) / previous) * 100;
    return {
      value: growth,
      isPositive: growth >= 0,
      icon:
        growth >= 0 ? (
          <StatsUpSquare className="h-4 w-4" />
        ) : (
          <StatsDownSquare className="h-4 w-4" />
        ),
    };
  };

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-muted-foreground">
              Loading analytics...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <WarningTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Failed to load analytics
              </h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={loadAnalytics} variant="outline">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Comprehensive overview of your Tokai platform metrics
          </p>
        </div>
        <Button onClick={loadAnalytics} variant="outline" size="sm">
          <StatsReport className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      {dashboardMetrics && (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(dashboardMetrics.totalUsers || 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                {formatNumber(dashboardMetrics.newUsersThisMonth || 0)} this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Wallets
              </CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(dashboardMetrics.totalWallets || 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                {formatNumber(dashboardMetrics.activeUsers || 0)} active users
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Apps</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(dashboardMetrics.totalApps || 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                {formatNumber(dashboardMetrics.totalTransactions || 0)} transactions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Security Score
              </CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {securityAnalytics
                  ? `${securityAnalytics.securityScore || 0}/100`
                  : "N/A"}
              </div>
              <p className="text-xs text-muted-foreground">
                {securityAnalytics
                  ? `${securityAnalytics.mfaEnabledUsers} MFA enabled`
                  : "Loading..."}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Detailed Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="wallets">Wallets</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="apps">Apps</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
            {/* User Growth Trend */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Wallet className="h-5 w-5" />
                  <span>User Growth</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {userAnalytics && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Growth Rate
                      </span>
                      <Badge
                        variant={
                          userAnalytics?.userGrowthRate &&
                          userAnalytics?.userGrowthRate >= 0
                            ? "default"
                            : "destructive"
                        }
                      >
                        {formatPercentage(userAnalytics?.userGrowthRate || 0)}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      {userAnalytics?.userActivityByDay
                        ?.slice(-7)
                        .map((day, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between text-sm"
                          >
                            <span>
                              {new Date(day.date).toLocaleDateString()}
                            </span>
                            <span className="font-medium">
                              {day.activeUsers}
                            </span>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Wallet Creation Trend */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Wallet className="h-5 w-5" />
                  <span>Wallet Creation</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {walletAnalytics && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Active Wallets
                      </span>
                      <Badge variant="outline">
                        {formatNumber(walletAnalytics?.activeWallets || 0)}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      {walletAnalytics?.walletCreationTrend
                        ?.slice(-7)
                        .map((day, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between text-sm"
                          >
                            <span>
                              {new Date(day.date).toLocaleDateString()}
                            </span>
                            <span className="font-medium">{day.count}</span>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          {userAnalytics && (
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>User Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Total Users
                      </p>
                      <p className="text-2xl font-bold">
                        {formatNumber(userAnalytics?.totalUsers || 0)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Active Users
                      </p>
                      <p className="text-2xl font-bold">
                        {formatNumber(userAnalytics?.activeUsers || 0)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        New This Month
                      </p>
                      <p className="text-2xl font-bold">
                        {formatNumber(userAnalytics?.newUsersThisMonth || 0)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Growth Rate
                      </p>
                      <p className="text-2xl font-bold">
                        {formatPercentage(userAnalytics?.userGrowthRate || 0)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Countries</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {userAnalytics?.topCountries
                      ?.slice(0, 5)
                      .map((country, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between"
                        >
                          <span className="text-sm">{country.country}</span>
                          <Badge variant="outline">{country.count}</Badge>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="wallets" className="space-y-4">
          {walletAnalytics && (
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Wallet Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Total Wallets
                      </p>
                      <p className="text-2xl font-bold">
                        {formatNumber(walletAnalytics?.totalWallets || 0)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Active Wallets
                      </p>
                      <p className="text-2xl font-bold">
                        {formatNumber(walletAnalytics?.activeWallets || 0)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Wallets by Network</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {walletAnalytics &&
                      walletAnalytics?.walletsByNetwork &&
                      walletAnalytics?.walletsByNetwork?.map(
                        (network, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between"
                          >
                            <span className="text-sm capitalize">
                              {network.network}
                            </span>
                            <Badge variant="outline">{network.count}</Badge>
                          </div>
                        )
                      )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          {securityAnalytics && (
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Security Overview</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Security Score
                      </p>
                      <p className="text-2xl font-bold">
                        {securityAnalytics.securityScore}/100
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        MFA Enabled
                      </p>
                      <p className="text-2xl font-bold">
                        {formatNumber(securityAnalytics?.mfaEnabledUsers || 0)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Failed Attempts
                      </p>
                      <p className="text-2xl font-bold text-red-600">
                        {formatNumber(
                          securityAnalytics?.failedLoginAttempts || 0
                        )}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Suspicious Activity
                      </p>
                      <p className="text-2xl font-bold text-orange-600">
                        {formatNumber(
                          securityAnalytics?.suspiciousActivityCount || 0
                        )}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>MFA Usage by Type</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {securityAnalytics?.mfaUsageByType?.map((type, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between"
                      >
                        <span className="text-sm capitalize">{type.type}</span>
                        <Badge variant="outline">{type.count}</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="apps" className="space-y-4">
          {appAnalytics && (
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>App Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Total Apps
                      </p>
                      <p className="text-2xl font-bold">
                        {formatNumber(appAnalytics.totalApps)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Active Apps
                      </p>
                      <p className="text-2xl font-bold">
                        {formatNumber(appAnalytics?.activeApps || 0)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Apps by Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {appAnalytics?.appsByStatus?.map((status, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between"
                      >
                        <span className="text-sm capitalize">
                          {status.status}
                        </span>
                        <Badge variant="outline">{status.count}</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
