'use client';

import { useState } from 'react';
import { useAuth } from '@/lib/auth-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Check,
  WarningTriangle,
  Refresh,
  Mail,
  ArrowLeft,
} from 'iconoir-react';

interface OTPLoginProps {
  onBack: () => void;
  onSuccess: () => void;
}

export default function OTPLogin({ onBack, onSuccess }: OTPLoginProps) {
  const { sendOTP, verifyOTP } = useAuth();
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [step, setStep] = useState<'email' | 'otp'>('email');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSendOTP = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await sendOTP(email);
      setStep('otp');
      setSuccess('OTP sent to your email');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send OTP';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await verifyOTP(email, otp);
      onSuccess();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Invalid OTP';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await sendOTP(email);
      setSuccess('OTP resent to your email');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to resend OTP';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <button
          onClick={onBack}
          className="absolute left-4 top-4 text-muted-foreground hover:text-foreground"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <h2 className="text-2xl font-bold">Login with OTP</h2>
        <p className="text-muted-foreground">
          {step === 'email' 
            ? "Enter your email to receive a login code"
            : "Enter the 6-digit code sent to your email"
          }
        </p>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <WarningTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {success && (
        <Alert>
          <Check className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Email Step */}
      {step === 'email' && (
        <form onSubmit={handleSendOTP} className="space-y-4">
          <div>
            <label className="text-sm font-medium text-foreground mb-2 block">
              Email Address
            </label>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              required
              className="w-full"
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center">
                <Refresh className="h-4 w-4 mr-2 animate-spin" />
                Sending OTP...
              </div>
            ) : (
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2" />
                Send Login Code
              </div>
            )}
          </Button>
        </form>
      )}

      {/* OTP Step */}
      {step === 'otp' && (
        <form onSubmit={handleVerifyOTP} className="space-y-4">
          <div>
            <label className="text-sm font-medium text-foreground mb-2 block">
              6-Digit Code
            </label>
            <Input
              type="text"
              value={otp}
              onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
              placeholder="Enter 6-digit code"
              maxLength={6}
              required
              className="w-full text-center text-lg tracking-widest"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Code sent to {email}
            </p>
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isLoading || otp.length !== 6}
          >
            {isLoading ? (
              <div className="flex items-center">
                <Refresh className="h-4 w-4 mr-2 animate-spin" />
                Verifying...
              </div>
            ) : (
              <div className="flex items-center">
                <Check className="h-4 w-4 mr-2" />
                Verify & Login
              </div>
            )}
          </Button>

          <div className="text-center">
            <button
              type="button"
              onClick={handleResendOTP}
              disabled={isLoading}
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              Didn&apos;t receive the code? Resend
            </button>
          </div>
        </form>
      )}
    </div>
  );
}