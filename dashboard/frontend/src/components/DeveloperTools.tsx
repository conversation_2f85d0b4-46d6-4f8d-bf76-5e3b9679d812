"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Code, 
  Play, 
  Copy, 
  Download, 
  Globe, 
  Flash,
  WarningTriangle,
  CheckCircle,
  Clock,
  Settings,
  OpenBook,
  Terminal,
  StatsReport
} from "iconoir-react";

interface DeveloperToolsProps {
  className?: string;
}

interface ApiTestRequest {
  method: string;
  endpoint: string;
  headers: Record<string, string>;
  body: string;
}

interface ApiTestResponse {
  status: number;
  headers: Record<string, string>;
  body: string;
  duration: number;
}

const SAMPLE_REQUESTS = {
  "Get User Profile": {
    method: "GET",
    endpoint: "/api/auth/me",
    headers: { "Authorization": "Bearer YOUR_TOKEN" },
    body: ""
  },
  "Get Wallets": {
    method: "GET",
    endpoint: "/api/wallets",
    headers: { "Authorization": "Bearer YOUR_TOKEN" },
    body: ""
  },
  "Create Wallet": {
    method: "POST",
    endpoint: "/api/wallets",
    headers: { 
      "Authorization": "Bearer YOUR_TOKEN",
      "Content-Type": "application/json"
    },
    body: '{"network": "ethereum"}'
  },
  "Get Apps": {
    method: "GET",
    endpoint: "/api/apps",
    headers: { "Authorization": "Bearer YOUR_TOKEN" },
    body: ""
  }
};

const INTEGRATION_GUIDES = [
  {
    title: "Quick Start",
    description: "Get up and running with Tokai Wallet in minutes",
    steps: [
      "Create an application in the dashboard",
      "Generate an API key for your app",
      "Use the API key to authenticate requests",
      "Start creating wallets for your users"
    ],
    code: `// Example: Create a wallet for a user
const response = await fetch('https://api.tokai.com/api/wallets', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    network: 'ethereum',
    endUserId: 'user123'
  })
});

const wallet = await response.json();
        console.log('Created wallet:', wallet.wallet_address);`
  },
  {
    title: "Authentication",
    description: "Learn how to authenticate your requests",
    steps: [
      "Use API keys for server-to-server communication",
      "Use session keys for delegated access",
      "Include the Authorization header in all requests",
      "Handle token expiration gracefully"
    ],
    code: `// API Key Authentication
const headers = {
  'Authorization': 'Bearer sk_live_YOUR_API_KEY',
  'Content-Type': 'application/json'
};

// Session Key Authentication
const headers = {
  'Authorization': 'SessionKey sk_live_YOUR_SESSION_KEY',
  'Content-Type': 'application/json'
};`
  },
  {
    title: "Wallet Management",
    description: "Create and manage wallets for your users",
    steps: [
      "Create wallets for specific networks",
      "Associate wallets with end users",
      "Sign transactions on behalf of users",
      "Monitor wallet activity"
    ],
    code: `// Create a wallet
const wallet = await api.createWallet({
  network: 'ethereum',
  endUserId: 'user123'
});

// Sign a transaction
const signedTx = await api.signTransaction({
  walletId: wallet.id,
  transaction: {
    to: '0x...',
    value: '1000000000000000000',
    data: '0x'
  }
});`
  }
];

export function DeveloperTools({ className }: DeveloperToolsProps) {
  const [activeTab, setActiveTab] = useState("api-tester");
  const [apiRequest, setApiRequest] = useState<ApiTestRequest>({
    method: "GET",
    endpoint: "/api/auth/me",
    headers: { "Authorization": "Bearer YOUR_TOKEN" },
    body: ""
  });
  const [apiResponse, setApiResponse] = useState<ApiTestResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const handleApiTest = async () => {
    setIsLoading(true);
    setApiResponse(null);

    try {
      const startTime = Date.now();
      
      // Simulate API call (replace with actual implementation)
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const duration = Date.now() - startTime;
      
      // Mock response for demo
      setApiResponse({
        status: 200,
        headers: {
          "content-type": "application/json",
          "x-rate-limit-remaining": "999"
        },
        body: JSON.stringify({
          success: true,
          data: {
            id: "user123",
            email: "<EMAIL>",
            created_at: new Date().toISOString()
          }
        }, null, 2),
        duration
      });
    } catch (error) {
      setApiResponse({
        status: 500,
        headers: {},
        body: JSON.stringify({
          error: "Request failed",
          message: error instanceof Error ? error.message : "Unknown error"
        }, null, 2),
        duration: 0
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSampleRequest = (sample: keyof typeof SAMPLE_REQUESTS) => {
    const request = SAMPLE_REQUESTS[sample];
    setApiRequest({
      method: request.method,
      endpoint: request.endpoint,
      headers: request.headers,
      body: request.body
    });
  };

  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      setTimeout(() => setCopiedCode(null), 2000);
          } catch (err) {
        console.error("Failed to copy code:", err);
      }
  };

  const formatHeaders = (headers: Record<string, string>) => {
    return Object.entries(headers)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Developer Tools</h2>
          <p className="text-muted-foreground">Test APIs, monitor usage, and explore integration guides</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="api-tester">API Tester</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="guides">Guides</TabsTrigger>
        </TabsList>

        <TabsContent value="api-tester" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Request Panel */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Terminal className="h-5 w-5" />
                  <span>API Request</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="method">Method</Label>
                    <select
                      id="method"
                      value={apiRequest.method}
                      onChange={(e) => setApiRequest(prev => ({ ...prev, method: e.target.value }))}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="GET">GET</option>
                      <option value="POST">POST</option>
                      <option value="PUT">PUT</option>
                      <option value="DELETE">DELETE</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="endpoint">Endpoint</Label>
                    <Input
                      id="endpoint"
                      value={apiRequest.endpoint}
                      onChange={(e) => setApiRequest(prev => ({ ...prev, endpoint: e.target.value }))}
                      placeholder="/api/endpoint"
                    />
                  </div>
                </div>

                <div>
                  <Label>Headers</Label>
                  <Textarea
                    value={formatHeaders(apiRequest.headers)}
                    onChange={(e) => {
                      const headers: Record<string, string> = {};
                      e.target.value.split('\n').forEach(line => {
                        const [key, value] = line.split(': ');
                        if (key && value) headers[key] = value;
                      });
                      setApiRequest(prev => ({ ...prev, headers }));
                    }}
                    rows={4}
                    placeholder="Authorization: Bearer YOUR_TOKEN\nContent-Type: application/json"
                  />
                </div>

                {apiRequest.method !== "GET" && (
                  <div>
                    <Label>Request Body</Label>
                    <Textarea
                      value={apiRequest.body}
                      onChange={(e) => setApiRequest(prev => ({ ...prev, body: e.target.value }))}
                      rows={6}
                      placeholder='{"key": "value"}'
                    />
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Button onClick={handleApiTest} disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Testing...
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Test Request
                      </>
                    )}
                  </Button>
                  
                  <div className="flex-1">
                    <Label>Sample Requests</Label>
                    <select
                      onChange={(e) => handleSampleRequest(e.target.value as keyof typeof SAMPLE_REQUESTS)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="">Choose a sample...</option>
                      {Object.keys(SAMPLE_REQUESTS).map((key) => (
                        <option key={key} value={key}>{key}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Response Panel */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Code className="h-5 w-5" />
                  <span>Response</span>
                  {apiResponse && (
                    <Badge variant={apiResponse.status >= 200 && apiResponse.status < 300 ? "default" : "destructive"}>
                      {apiResponse.status}
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {apiResponse ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Duration: {apiResponse.duration}ms</span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleCopyCode(apiResponse.body)}
                      >
                        {copiedCode === apiResponse.body ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    
                    <div>
                      <Label>Response Headers</Label>
                      <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-20">
                        {formatHeaders(apiResponse.headers)}
                      </pre>
                    </div>
                    
                    <div>
                      <Label>Response Body</Label>
                      <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-64">
                        {apiResponse.body}
                      </pre>
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground">
                    <Code className="h-12 w-12 mx-auto mb-4" />
                    <p>No response yet. Test a request to see the response here.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="webhooks" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Globe className="h-5 w-5" />
                  <span>Webhook Testing</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="webhook-url">Webhook URL</Label>
                  <Input
                    id="webhook-url"
                    placeholder="https://your-app.com/webhook"
                  />
                </div>
                
                <div>
                  <Label>Event Type</Label>
                  <select className="w-full p-2 border rounded-md">
                    <option value="wallet.created">Wallet Created</option>
                    <option value="transaction.signed">Transaction Signed</option>
                    <option value="user.registered">User Registered</option>
                    <option value="app.created">App Created</option>
                  </select>
                </div>
                
                <Button className="w-full">
                  <Flash className="h-4 w-4 mr-2" />
                  Send Test Webhook
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Webhook History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>wallet.created</span>
                    <Badge variant="default">200</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>transaction.signed</span>
                    <Badge variant="destructive">500</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>user.registered</span>
                    <Badge variant="default">200</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <StatsReport className="h-5 w-5" />
                  <span>Rate Limits</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">API Requests</span>
                    <span className="text-sm font-medium">850/1000</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Authentication</span>
                    <span className="text-sm font-medium">2/10</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: '20%' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="h-5 w-5" />
                  <span>Response Times</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Average</span>
                    <span className="font-medium">245ms</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>P95</span>
                    <span className="font-medium">890ms</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>P99</span>
                    <span className="font-medium">1.2s</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <WarningTriangle className="h-5 w-5" />
                  <span>Errors</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>4xx Errors</span>
                    <Badge variant="outline">12</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>5xx Errors</span>
                    <Badge variant="destructive">3</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Success Rate</span>
                    <Badge variant="default">98.5%</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="guides" className="space-y-6">
          <div className="space-y-6">
            {INTEGRATION_GUIDES.map((guide, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <OpenBook className="h-5 w-5" />
                    <span>{guide.title}</span>
                  </CardTitle>
                  <p className="text-muted-foreground">{guide.description}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Steps</Label>
                    <ol className="list-decimal list-inside space-y-1 mt-2">
                      {guide.steps.map((step, stepIndex) => (
                        <li key={stepIndex} className="text-sm">{step}</li>
                      ))}
                    </ol>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Label>Code Example</Label>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleCopyCode(guide.code)}
                      >
                        {copiedCode === guide.code ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    <pre className="text-xs bg-muted p-4 rounded overflow-auto">
                      <code>{guide.code}</code>
                    </pre>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}