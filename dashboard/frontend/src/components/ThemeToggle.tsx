"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useTheme } from "next-themes";
import { SunLight, MoonSat } from "iconoir-react";

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="h-8 w-8"
      title={`Switch to ${theme === "light" ? "dark" : "light"} mode`}
    >
      {theme === "light" ? (
        <MoonSat className="h-4 w-4" />
      ) : (
        <SunLight className="h-4 w-4" />
      )}
      <span className="sr-only">
        Switch to {theme === "light" ? "dark" : "light"} mode
      </span>
    </Button>
  );
} 