"use client";

import React, { useEffect, useMemo, use<PERSON><PERSON>back } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Key, 
  Plus, 
  Eye, 
  EyeClosed, 
  Copy, 
  Trash, 
  Clock, 
  Shield,
  WarningTriangle,
  CheckCircle,
  Activity,
  Settings,
  Refresh
} from "iconoir-react";
import { useSessionKeys } from "@/hooks/useSessionKeys";

interface SessionKeysManagerProps {
  className?: string;
}

const AVAILABLE_PERMISSIONS = [
  { id: "read_profile", label: "Read Profile", description: "Access user profile information" },
  { id: "read_wallets", label: "Read Wallets", description: "View wallet addresses and balances" },
  { id: "create_wallets", label: "Create Wallets", description: "Create new wallets for users" },
  { id: "sign_transactions", label: "Sign Transactions", description: "Sign transactions on behalf of users" },
  { id: "read_apps", label: "Read Apps", description: "View application information" },
  { id: "manage_apps", label: "Manage Apps", description: "Create and manage applications" },
  { id: "read_analytics", label: "Read Analytics", description: "Access analytics and metrics" },
  { id: "admin_access", label: "Admin Access", description: "Full administrative access" },
];

const EXPIRY_OPTIONS = [
  { value: 3600000, label: "1 Hour" },
  { value: 86400000, label: "24 Hours" },
  { value: 604800000, label: "7 Days" },
  { value: 2592000000, label: "30 Days" },
  { value: 7776000000, label: "90 Days" },
];

export const SessionKeysManager = React.memo(function SessionKeysManager({ className }: SessionKeysManagerProps) {
  const {
    sessionKeys,
    selectedKey,
    keyUsage,
    isLoading,
    error,
    showCreateDialog,
    showRawKey,
    copiedKey,
    createForm,
    loadSessionKeys,
    loadKeyUsage,
    handleCreateKey,
    handleRevokeKey,
    handleCopyKey,
    handlePermissionToggle,
    setSelectedKey,
    setShowCreateDialog,
    setShowRawKey,
    setCopiedKey,
    updateCreateForm,
    resetCreateForm,
    clearError,
  } = useSessionKeys();

  useEffect(() => {
    loadSessionKeys();
  }, [loadSessionKeys]);

  // Memoized utility functions
  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleString();
  }, []);

  const isExpired = useCallback((expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  }, []);

  const getTimeUntilExpiry = useCallback((expiresAt: string) => {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diff = expiry.getTime() - now.getTime();
    
    if (diff <= 0) return "Expired";
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  }, []);

  // Memoized computed values
  const activeKeys = useMemo(() => 
    sessionKeys?.filter(key => key.isActive && !isExpired(key.expiresAt)), 
    [sessionKeys, isExpired]
  );

  const expiredKeys = useMemo(() => 
    sessionKeys?.filter(key => isExpired(key.expiresAt)), 
    [sessionKeys, isExpired]
  );

  const revokedKeys = useMemo(() => 
    sessionKeys?.filter(key => !key.isActive), 
    [sessionKeys]
  );

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-muted-foreground">Loading session keys...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Session Keys</h2>
          <p className="text-muted-foreground">Manage delegated access tokens with specific permissions</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={loadSessionKeys} variant="outline" size="sm">
            <Refresh className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Session Key
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Session Key</DialogTitle>
                <DialogDescription>
                  Create a time-limited access token with specific permissions for third-party applications.
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="keyName">Key Name</Label>
                  <Input
                    id="keyName"
                    value={createForm.keyName}
                    onChange={(e) => updateCreateForm({ keyName: e.target.value })}
                    placeholder="e.g., My App Integration"
                  />
                </div>

                <div>
                  <Label>Permissions</Label>
                  <div className="grid grid-cols-1 gap-3 mt-2">
                    {AVAILABLE_PERMISSIONS.map((permission) => (
                      <div key={permission.id} className="flex items-start space-x-3">
                        <Checkbox
                          id={permission.id}
                          checked={createForm.permissions.includes(permission.id)}
                          onCheckedChange={() => handlePermissionToggle(permission.id)}
                        />
                        <div className="flex-1">
                          <Label htmlFor={permission.id} className="text-sm font-medium">
                            {permission.label}
                          </Label>
                          <p className="text-xs text-muted-foreground">{permission.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label htmlFor="scope">Scope (Optional)</Label>
                  <Input
                    id="scope"
                    value={createForm.scope}
                    onChange={(e) => updateCreateForm({ scope: e.target.value })}
                    placeholder="e.g., /api/wallets"
                  />
                </div>

                <div>
                  <Label htmlFor="expiryDuration">Expiry Duration</Label>
                  <select
                    id="expiryDuration"
                    value={createForm.expiryDuration}
                    onChange={(e) => updateCreateForm({ expiryDuration: parseInt(e.target.value) })}
                    className="w-full p-2 border rounded-md"
                  >
                    {EXPIRY_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label htmlFor="metadata">Metadata (Optional)</Label>
                  <Textarea
                    id="metadata"
                    value={createForm.metadata}
                    onChange={(e) => updateCreateForm({ metadata: e.target.value })}
                    placeholder='{"app_id": "123", "environment": "production"}'
                    rows={3}
                  />
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleCreateKey}
                  disabled={!createForm.keyName || createForm.permissions.length === 0}
                >
                  Create Session Key
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
                      <WarningTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Raw Key Display Dialog */}
      <Dialog open={!!showRawKey} onOpenChange={() => setShowRawKey(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Session Key Created</DialogTitle>
            <DialogDescription>
              Store this key securely. It will not be shown again.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <Alert>
              <WarningTriangle className="h-4 w-4" />
              <AlertDescription>
                This is the only time you&apos;ll see this key. Copy it now and store it securely.
              </AlertDescription>
            </Alert>
            
            <div className="relative">
              <Textarea
                value={showRawKey || ""}
                readOnly
                rows={4}
                className="font-mono text-sm"
              />
              <Button
                size="sm"
                variant="outline"
                className="absolute top-2 right-2"
                onClick={() => handleCopyKey(showRawKey || "")}
              >
                {copiedKey === showRawKey ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>
          </div>
          
          <DialogFooter>
            <Button onClick={() => setShowRawKey(null)}>
              Done
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Session Keys List */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Your Session Keys</h3>
          
          {sessionKeys.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <Key className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No session keys</h3>
                  <p className="text-muted-foreground mb-4">
                    Create your first session key to get started with delegated access.
                  </p>
                  <Button onClick={() => setShowCreateDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Session Key
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {sessionKeys.map((key) => (
                <Card 
                  key={key.id} 
                  className={`cursor-pointer transition-colors ${
                    selectedKey?.id === key.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => {
                    setSelectedKey(key);
                    loadKeyUsage(key.id);
                  }}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-semibold">{key.keyName}</h4>
                          <Badge variant={key.isActive ? "default" : "secondary"}>
                            {key.isActive ? "Active" : "Inactive"}
                          </Badge>
                          {isExpired(key.expiresAt) && (
                            <Badge variant="destructive">Expired</Badge>
                          )}
                        </div>
                        
                        <div className="space-y-1 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-2">
                            <Clock className="h-3 w-3" />
                            <span>Expires: {formatDate(key.expiresAt)}</span>
                          </div>
                          
                          {key.lastUsedAt && (
                            <div className="flex items-center space-x-2">
                              <Activity className="h-3 w-3" />
                              <span>Last used: {formatDate(key.lastUsedAt)}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center space-x-2">
                            <Shield className="h-3 w-3" />
                            <span>{key.permissions.length} permissions</span>
                          </div>
                        </div>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRevokeKey(key.id);
                        }}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Key Details */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Key Details</h3>
          
          {selectedKey ? (
            <Tabs defaultValue="details" className="space-y-4">
              <TabsList>
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="usage">Usage</TabsTrigger>
                <TabsTrigger value="permissions">Permissions</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>{selectedKey.keyName}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Status</p>
                        <Badge variant={selectedKey.isActive ? "default" : "secondary"}>
                          {selectedKey.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Created</p>
                        <p>{formatDate(selectedKey.createdAt)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Expires</p>
                        <p>{formatDate(selectedKey.expiresAt)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Time Left</p>
                        <p className={isExpired(selectedKey.expiresAt) ? "text-red-600" : ""}>
                          {getTimeUntilExpiry(selectedKey.expiresAt)}
                        </p>
                      </div>
                    </div>
                    
                    {selectedKey.scope && (
                      <div>
                        <p className="text-sm text-muted-foreground mb-1">Scope</p>
                        <p className="text-sm font-mono bg-muted p-2 rounded">{selectedKey.scope}</p>
                      </div>
                    )}
                    
                    {selectedKey.metadata && (
                      <div>
                        <p className="text-sm text-muted-foreground mb-1">Metadata</p>
                        <pre className="text-sm bg-muted p-2 rounded overflow-auto">
                          {JSON.stringify(selectedKey.metadata, null, 2)}
                        </pre>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="usage" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Usage History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {keyUsage.length === 0 ? (
                      <p className="text-muted-foreground">No usage history available.</p>
                    ) : (
                      <div className="space-y-2">
                        {keyUsage.slice(0, 10).map((usage, index) => (
                          <div key={index} className="flex items-center justify-between text-sm">
                            <div>
                              <p className="font-medium">{usage.method} {usage.endpoint}</p>
                              <p className="text-muted-foreground">{formatDate(usage.timestamp)}</p>
                            </div>
                            <Badge variant={usage.success ? "default" : "destructive"}>
                              {usage.success ? "Success" : "Failed"}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="permissions" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Permissions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {selectedKey.permissions.map((permission) => {
                        const permissionInfo = AVAILABLE_PERMISSIONS.find(p => p.id === permission);
                        return (
                          <div key={permission} className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <div>
                              <p className="font-medium">{permissionInfo?.label || permission}</p>
                              <p className="text-sm text-muted-foreground">
                                {permissionInfo?.description || "Custom permission"}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <Key className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Select a session key</h3>
                  <p className="text-muted-foreground">
                    Choose a session key from the list to view its details and usage history.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
});