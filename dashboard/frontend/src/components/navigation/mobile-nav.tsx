'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Menu, X, Home, Key, Shield, Terminal, Settings, User } from 'iconoir-react';
import Link from 'next/link';

interface MobileNavProps {
  currentTab?: string;
  onTabChange?: (tab: string) => void;
}

export const MobileNav = ({ currentTab, onTabChange }: MobileNavProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const navItems = [
    { id: 'overview', label: 'Overview', icon: Home, href: '#overview' },
    { id: 'analytics', label: 'Analytics', icon: Key, href: '#analytics' },
    { id: 'session-keys', label: 'Session Keys', icon: Key, href: '#session-keys' },
    { id: 'sessions', label: 'Sessions', icon: Shield, href: '#sessions' },
    { id: 'developer-tools', label: 'Developer Tools', icon: Terminal, href: '#developer-tools' },
    { id: 'apps', label: 'Apps', icon: Home, href: '/apps' },
  ];

  const handleTabClick = (tabId: string) => {
    setIsOpen(false);
    onTabChange?.(tabId);
  };

  return (
    <div className="lg:hidden">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Toggle navigation"
        className="fixed top-4 right-4 z-50 bg-background/80 backdrop-blur-sm border border-border"
      >
        {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>
      
      {isOpen && (
        <div className="fixed inset-0 z-40 bg-background/95 backdrop-blur-sm">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-border">
              <h2 className="text-lg font-semibold">Navigation</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                aria-label="Close navigation"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Navigation Items */}
            <nav className="flex-1 p-4 space-y-2">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentTab === item.id;
                
                return (
                  <Card
                    key={item.id}
                    className={`p-4 cursor-pointer transition-all duration-200 ${
                      isActive 
                        ? 'bg-primary/10 border-primary/20' 
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleTabClick(item.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className={`h-5 w-5 ${
                        isActive ? 'text-primary' : 'text-muted-foreground'
                      }`} />
                      <span className={`font-medium ${
                        isActive ? 'text-primary' : 'text-foreground'
                      }`}>
                        {item.label}
                      </span>
                    </div>
                  </Card>
                );
              })}
            </nav>

            {/* Footer */}
            <div className="p-4 border-t border-border space-y-2">
              <Link href="/profile">
                <Card className="p-4 cursor-pointer hover:bg-muted/50 transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <User className="h-5 w-5 text-muted-foreground" />
                    <span className="font-medium">Profile</span>
                  </div>
                </Card>
              </Link>
              <Link href="/settings">
                <Card className="p-4 cursor-pointer hover:bg-muted/50 transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <Settings className="h-5 w-5 text-muted-foreground" />
                    <span className="font-medium">Settings</span>
                  </div>
                </Card>
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};