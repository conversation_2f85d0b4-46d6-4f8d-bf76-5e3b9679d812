'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { apiClient, SessionInfo } from '@/lib/api';
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  Globe, 
  Clock, 
  MapPin, 
  Trash2, 
  LogOut,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface SessionManagerProps {
  className?: string;
}

export function SessionManager({ className }: SessionManagerProps) {
  const [sessions, setSessions] = useState<SessionInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [revokingSession, setRevokingSession] = useState<string | null>(null);
  const [showLogoutAllDialog, setShowLogoutAllDialog] = useState(false);

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      setLoading(true);
      setError(null);
      const sessionsData = await apiClient.getUserSessions();
      setSessions(sessionsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load sessions');
    } finally {
      setLoading(false);
    }
  };

  const revokeSession = async (sessionId: string) => {
    try {
      setRevokingSession(sessionId);
      await apiClient.invalidateSession(sessionId);
      await loadSessions(); // Reload sessions
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to revoke session');
    } finally {
      setRevokingSession(null);
    }
  };

  const logoutAllDevices = async () => {
    try {
      await apiClient.logoutAll();
      setShowLogoutAllDialog(false);
      // Redirect to login page
      window.location.href = '/login';
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to logout from all devices');
    }
  };

  const getDeviceIcon = (deviceInfo?: string) => {
    if (!deviceInfo) return <Globe className="h-4 w-4" />;
    
    const info = deviceInfo.toLowerCase();
    if (info.includes('mobile') || info.includes('android') || info.includes('iphone')) {
      return <Smartphone className="h-4 w-4" />;
    } else if (info.includes('tablet') || info.includes('ipad')) {
      return <Tablet className="h-4 w-4" />;
    } else if (info.includes('mac')) {
      return <Monitor className="h-4 w-4" />;
    } else if (info.includes('windows') || info.includes('linux')) {
      return <Monitor className="h-4 w-4" />;
    }
    
    return <Monitor className="h-4 w-4" />;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} hours ago`;
    return date.toLocaleDateString();
  };

  const getStatusBadge = (session: SessionInfo) => {
    if (session.isCurrent) {
      return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Current</Badge>;
    }
    return <Badge variant="secondary">Active</Badge>;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Active Sessions</CardTitle>
          <CardDescription>Manage your active sessions across devices</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Active Sessions</CardTitle>
            <CardDescription>Manage your active sessions across devices</CardDescription>
          </div>
          <Dialog open={showLogoutAllDialog} onOpenChange={setShowLogoutAllDialog}>
            <DialogTrigger asChild>
              <Button variant="destructive" size="sm">
                <LogOut className="h-4 w-4 mr-2" />
                Logout All Devices
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Logout from All Devices</DialogTitle>
                <DialogDescription>
                  This will sign you out from all devices and invalidate all active sessions. 
                  You&apos;ll need to log in again on any device you want to use.
                </DialogDescription>
              </DialogHeader>
              <div className="flex items-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3" />
                <span className="text-sm text-yellow-800">
                  This action cannot be undone. All your active sessions will be terminated.
                </span>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowLogoutAllDialog(false)}>
                  Cancel
                </Button>
                <Button variant="destructive" onClick={logoutAllDevices}>
                  <LogOut className="h-4 w-4 mr-2" />
                  Logout All Devices
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent>
        {error && (
          <Alert className="mb-4">
            <XCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {sessions.length === 0 ? (
          <div className="text-center py-8">
            <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No active sessions</h3>
            <p className="text-muted-foreground">You don&apos;t have any active sessions at the moment.</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground mb-4">
              {sessions.length} active session{sessions.length !== 1 ? 's' : ''}
            </div>
            
            {sessions.map((session) => (
              <div key={session.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="mt-1">
                      {getDeviceIcon(session.deviceInfo)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="font-medium">
                          {session.deviceInfo || 'Unknown Device'}
                        </span>
                        {getStatusBadge(session)}
                      </div>
                      
                      <div className="space-y-1 text-sm text-muted-foreground">
                        {session.ipAddress && (
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-3 w-3" />
                            <span>{session.ipAddress}</span>
                          </div>
                        )}
                        
                        <div className="flex items-center space-x-2">
                          <Clock className="h-3 w-3" />
                          <span>Last used: {formatDate(session.lastUsedAt)}</span>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Clock className="h-3 w-3" />
                          <span>Created: {new Date(session.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {!session.isCurrent && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => revokeSession(session.id)}
                      disabled={revokingSession === session.id}
                      className="text-destructive hover:text-destructive"
                    >
                      {revokingSession === session.id ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                      ) : (
                        <>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Revoke
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
        
        <Separator className="my-6" />
        
        <div className="text-sm text-muted-foreground">
          <p className="mb-2">
            <strong>Security Tips:</strong>
          </p>
          <ul className="space-y-1 list-disc list-inside">
            <li>Regularly review and revoke sessions from devices you no longer use</li>
            <li>Use &quot;Logout All Devices&quot; if you suspect unauthorized access</li>
            <li>Your current session is automatically refreshed to maintain security</li>
            <li>Sessions expire after 7 days of inactivity</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}