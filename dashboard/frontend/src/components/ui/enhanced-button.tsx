import React from 'react';
import { But<PERSON> } from './button';
import { cn } from '@/lib/utils';

interface EnhancedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  ariaLabel?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

export const EnhancedButton = ({ 
  children, 
  onClick, 
  disabled, 
  loading,
  variant = 'default',
  size = 'default',
  className,
  ariaLabel,
  icon,
  iconPosition = 'left'
}: EnhancedButtonProps) => {
  return (
    <Button
      onClick={onClick}
      disabled={disabled || loading}
      variant={variant}
      size={size}
      aria-label={ariaLabel}
      className={cn(
        "focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-all duration-200",
        loading && "cursor-not-allowed opacity-75",
        className
      )}
    >
      {loading ? (
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
          <span>Loading...</span>
        </div>
      ) : (
        <div className="flex items-center space-x-2">
          {icon && iconPosition === 'left' && icon}
          <span>{children}</span>
          {icon && iconPosition === 'right' && icon}
        </div>
      )}
    </Button>
  );
};

export const GradientButton = ({ 
  children, 
  onClick, 
  disabled, 
  loading,
  className,
  ...props 
}: EnhancedButtonProps) => {
  return (
    <EnhancedButton
      onClick={onClick}
      disabled={disabled}
      loading={loading}
      className={cn(
        "bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300",
        className
      )}
      {...props}
    >
      {children}
    </EnhancedButton>
  );
};

export const IconButton = ({ 
  icon, 
  onClick, 
  disabled, 
  loading,
  className,
  ariaLabel,
  ...props 
}: EnhancedButtonProps) => {
  return (
    <EnhancedButton
      onClick={onClick}
      disabled={disabled}
      loading={loading}
      size="icon"
      ariaLabel={ariaLabel}
      className={cn(
        "rounded-full hover:scale-110 transition-transform duration-200",
        className
      )}
      {...props}
    >
      {icon}
    </EnhancedButton>
  );
};