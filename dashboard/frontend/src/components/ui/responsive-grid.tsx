import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: string;
}

export const ResponsiveGrid = ({ 
  children, 
  className,
  cols = { sm: 1, md: 2, lg: 3, xl: 4 },
  gap = "gap-4"
}: ResponsiveGridProps) => {
  const gridCols = {
    sm: cols.sm ? `sm:grid-cols-${cols.sm}` : '',
    md: cols.md ? `md:grid-cols-${cols.md}` : '',
    lg: cols.lg ? `lg:grid-cols-${cols.lg}` : '',
    xl: cols.xl ? `xl:grid-cols-${cols.xl}` : '',
  };

  return (
    <div 
      className={cn(
        "grid grid-cols-1",
        gridCols.sm,
        gridCols.md,
        gridCols.lg,
        gridCols.xl,
        gap,
        className
      )}
    >
      {children}
    </div>
  );
};

export const StatsGrid = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  return (
    <ResponsiveGrid 
      className={className}
      cols={{ sm: 1, md: 2, lg: 4, xl: 4 }}
      gap="gap-6"
    >
      {children}
    </ResponsiveGrid>
  );
};

export const ActionsGrid = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  return (
    <ResponsiveGrid 
      className={className}
      cols={{ sm: 1, md: 2, lg: 4, xl: 4 }}
      gap="gap-4"
    >
      {children}
    </ResponsiveGrid>
  );
};

export const AnalyticsGrid = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  return (
    <ResponsiveGrid 
      className={className}
      cols={{ sm: 1, lg: 2, xl: 2 }}
      gap="gap-6"
    >
      {children}
    </ResponsiveGrid>
  );
};