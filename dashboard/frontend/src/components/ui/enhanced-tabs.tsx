import React from 'react';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from './tabs';
import { cn } from '@/lib/utils';

interface EnhancedTabsProps {
  children: React.ReactNode;
  defaultValue: string;
  className?: string;
}

export const EnhancedTabs = ({ children, defaultValue, className }: EnhancedTabsProps) => {
  const triggers: React.ReactElement[] = [];
  const nonTriggers: React.ReactNode[] = [];

  React.Children.forEach(children, (child) => {
    if (React.isValidElement(child) && child.type === TabsTrigger) {
      triggers.push(
        React.cloneElement(child as React.ReactElement<any>, {
          className: cn(
            "data-[state=active]:bg-white data-[state=active]:dark:bg-gray-700 data-[state=active]:shadow-sm data-[state=active]:text-primary transition-all duration-200 rounded-md",
            "hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-foreground",
            "data-[state=active]:hover:bg-white data-[state=active]:dark:hover:bg-gray-700",
            (child.props as any)?.className
          ),
        })
      );
    } else {
      nonTriggers.push(child as React.ReactNode);
    }
  });

  return (
    <Tabs defaultValue={defaultValue} className={cn("space-y-8", className)}>
      <TabsList className="grid w-full grid-cols-6 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg shadow-sm">
        {triggers}
      </TabsList>
      {nonTriggers}
    </Tabs>
  );
};

export { TabsContent, TabsTrigger };