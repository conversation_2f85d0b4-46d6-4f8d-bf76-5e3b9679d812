'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { useAuth } from '@/lib/auth-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Check,
  WarningTriangle,
  Refresh,
  Mail,
  ArrowLeft,
} from 'iconoir-react';

interface EmailVerificationProps {
  onSuccess: () => void;
  onBack?: () => void;
}

export default function EmailVerification({ onSuccess, onBack }: EmailVerificationProps) {
  const { verifyEmail, sendVerificationEmail } = useAuth();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [email, setEmail] = useState('');
  const [isResending, setIsResending] = useState(false);

  const handleVerifyEmail = useCallback(async () => {
    if (!token) return;
    
    setIsLoading(true);
    setError(null);

    try {
      await verifyEmail(token);
      setSuccess('Email verified successfully!');
      setTimeout(() => {
        onSuccess();
      }, 2000);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Email verification failed';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [token, verifyEmail, onSuccess]);

  useEffect(() => {
    if (token) {
      handleVerifyEmail();
    }
  }, [token, handleVerifyEmail]);

  const handleResendVerification = async () => {
    if (!email) {
      setError('Please enter your email address');
      return;
    }

    setIsResending(true);
    setError(null);

    try {
      await sendVerificationEmail(email);
      setSuccess('Verification email sent successfully!');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send verification email';
      setError(errorMessage);
    } finally {
      setIsResending(false);
    }
  };

  if (token) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="h-12 w-12 logo-gradient rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">🚀</span>
              </div>
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-2">
              Email Verification
            </h1>
            <p className="text-muted-foreground">
              Verifying your email address...
            </p>
          </div>

          {/* Content */}
          <div className="card">
            {isLoading ? (
              <div className="text-center py-8">
                <Refresh className="h-8 w-8 mx-auto mb-4 animate-spin text-primary" />
                <p className="text-muted-foreground">Verifying your email...</p>
              </div>
            ) : error ? (
              <div className="space-y-4">
                <Alert variant="destructive">
                  <WarningTriangle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
                <div className="text-center">
                  <p className="text-sm text-muted-foreground mb-4">
                    The verification link may have expired or is invalid.
                  </p>
                  <div className="space-y-2">
                    <Input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                      className="w-full"
                    />
                    <Button
                      onClick={handleResendVerification}
                      disabled={isResending}
                      className="w-full"
                    >
                      {isResending ? (
                        <div className="flex items-center">
                          <Refresh className="h-4 w-4 mr-2 animate-spin" />
                          Sending...
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 mr-2" />
                          Resend Verification Email
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            ) : success ? (
              <div className="text-center py-8">
                <Check className="h-8 w-8 mx-auto mb-4 text-green-500" />
                <p className="text-green-600 font-medium">{success}</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Redirecting to dashboard...
                </p>
              </div>
            ) : null}
          </div>
        </div>
      </div>
    );
  }

  // Manual verification request
  return (
    <div className="min-h-screen gradient-bg flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center">
          {onBack && (
            <button
              onClick={onBack}
              className="absolute left-4 top-4 text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
          )}
          <div className="flex justify-center mb-6">
            <div className="h-12 w-12 logo-gradient rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-lg">🚀</span>
            </div>
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-2">
            Email Verification
          </h1>
          <p className="text-muted-foreground">
            Enter your email to receive a verification link
          </p>
        </div>

        {/* Form */}
        <div className="card">
          {error && (
            <Alert variant="destructive">
              <WarningTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <Check className="h-4 w-4" />
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={(e) => { e.preventDefault(); handleResendVerification(); }} className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground mb-2 block">
                Email Address
              </label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                required
                className="w-full"
              />
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isResending}
            >
              {isResending ? (
                <div className="flex items-center">
                  <Refresh className="h-4 w-4 mr-2 animate-spin" />
                  Sending Verification Email...
                </div>
              ) : (
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-2" />
                  Send Verification Email
                </div>
              )}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}