"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { apiClient, App } from "@/lib/api";
import { Edit, Eye, MoreVert, Plus, Refresh, Trash } from "iconoir-react";
import { useState } from "react";

interface AppsListViewProps {
  apps: App[];
  onCreateApp: (appData: {
    name: string;
    description?: string;
    domain?: string;
  }) => Promise<void>;
  onSelectApp: (app: App) => void;
  onRefresh: () => void;
}

const sortOptions = [
  { name: "Newest First", value: "newest" },
  { name: "Oldest First", value: "oldest" },
  { name: "Name A-Z", value: "name-asc" },
  { name: "Name Z-A", value: "name-desc" },
];

export function AppsListView({
  apps,
  onCreateApp,
  onSelectApp,
  onRefresh: _onRefresh,
}: AppsListViewProps) {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedSort, setSelectedSort] = useState(sortOptions[0]);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    domain: "",
  });
  const [isCreating, setIsCreating] = useState(false);
  const toast = useToast();

  const handleCreateApp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreating(true);

    try {
      await onCreateApp(formData);
      setIsCreateModalOpen(false);
      setFormData({ name: "", description: "", domain: "" });
      toast.success("App created successfully!");
    } catch (error) {
      console.error("Failed to create app:", error);
      toast.error("Failed to create app");
    } finally {
      setIsCreating(false);
    }
  };

  const sortedApps = [...apps].sort((a, b) => {
    switch (selectedSort.value) {
      case "newest":
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      case "oldest":
        return (
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
      case "name-asc":
        return a.name.localeCompare(b.name);
      case "name-desc":
        return b.name.localeCompare(a.name);
      default:
        return 0;
    }
  });

  const handleDeleteApp = async (appId: string) => {
    try {
      await apiClient.deleteApp(appId);
      toast.success("App deleted successfully!");
      _onRefresh();
    } catch (error) {
      console.error("Failed to delete app:", error);
      toast.error("Failed to delete app");
    }
  };

  if (apps.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="mx-auto h-16 w-16 text-muted-foreground mb-6">
          <Plus className="h-16 w-16" />
        </div>
        <h3 className="text-lg font-semibold text-foreground mb-2">No apps</h3>
        <p className="text-muted-foreground mb-8">
          Get started by creating your first app.
        </p>
        <div className="flex justify-center">
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button variant="default" size="lg" className="px-8 btn-gradient">
                <Plus className="h-5 w-5 mr-2" />
                Create Your First App
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Create New App</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleCreateApp} className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    App Name *
                  </label>
                  <Input
                    type="text"
                    required
                    value={formData.name}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                    placeholder="My E-commerce Site"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Description
                  </label>
                  <Textarea
                    rows={3}
                    value={formData.description}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    placeholder="Brief description of your application"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Domain (Optional)
                  </label>
                  <Input
                    type="url"
                    value={formData.domain}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        domain: e.target.value,
                      }))
                    }
                    placeholder="https://myapp.com"
                  />
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsCreateModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" variant="default">
                    {isCreating ? (
                      <>
                        <Refresh className="h-4 w-4 animate-spin mr-2" />
                        Creating...
                      </>
                    ) : (
                      "Create App"
                    )}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">Your Apps</h1>
          <p className="text-muted-foreground">
            Manage your applications and their configurations
          </p>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-foreground">Sort by:</span>
          <Select
            onValueChange={(value) =>
              setSelectedSort(
                sortOptions.find((opt) => opt.value === value) || sortOptions[0]
              )
            }
            defaultValue={selectedSort.value}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select a sort option" />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button variant="default" className="btn-gradient">
              <Plus className="h-4 w-4 mr-2" />
              Create App
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Create New App</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleCreateApp} className="space-y-4">
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  App Name *
                </label>
                <Input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  placeholder="My E-commerce Site"
                />
              </div>

              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Description
                </label>
                <Textarea
                  rows={3}
                  value={formData.description}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Brief description of your application"
                />
              </div>

              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Domain (Optional)
                </label>
                <Input
                  type="url"
                  value={formData.domain}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      domain: e.target.value,
                    }))
                  }
                  placeholder="https://myapp.com"
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" variant="default">
                  {isCreating ? (
                    <>
                      <Refresh className="h-4 w-4 animate-spin mr-2" />
                      Creating...
                    </>
                  ) : (
                    "Create App"
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Apps Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {sortedApps.map((app) => (
          <div key={app.id} className="app-card group">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1 min-w-0 pr-2">
                <h3 className="text-lg font-semibold text-foreground mb-2 truncate">
                  {app.name}
                </h3>
                {app.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {app.description}
                  </p>
                )}
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0"
                  >
                    <span className="sr-only">Open menu</span>
                    <MoreVert className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onSelectApp(app)}>
                    <Eye className="mr-2 h-4 w-4" />
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="text-destructive"
                    onClick={() => handleDeleteApp(app.id)}
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span
                  className={`status-badge ${app.status === "active" ? "status-active" : "status-inactive"}`}
                >
                  {app.status}
                </span>
                <span className="text-xs text-muted-foreground">
                  {new Date(app.created_at).toLocaleDateString()}
                </span>
              </div>

              <Button
                onClick={() => onSelectApp(app)}
                variant="outline"
                className="w-full"
              >
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
 