"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useApps, useCreateApp, useDeleteApp } from "@/hooks/useApps";
import { Edit, Eye, MoreVert, Plus, Refresh, Trash } from "iconoir-react";
import { useState } from "react";

interface AppsListViewRefactoredProps {
  onSelectApp: (app: any) => void;
}

const sortOptions = [
  { name: "Newest First", value: "newest" },
  { name: "Oldest First", value: "oldest" },
  { name: "Name A-Z", value: "name-asc" },
  { name: "Name Z-A", value: "name-desc" },
];

export function AppsListViewRefactored({
  onSelectApp,
}: AppsListViewRefactoredProps) {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedSort, setSelectedSort] = useState(sortOptions[0]);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    domain: "",
  });
  
  const toast = useToast();
  
  // TanStack Query hooks
  const { data: apps = [], isLoading, error, refetch } = useApps();
  const createAppMutation = useCreateApp();
  const deleteAppMutation = useDeleteApp();

  const handleCreateApp = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await createAppMutation.mutateAsync(formData);
      setIsCreateModalOpen(false);
      setFormData({ name: "", description: "", domain: "" });
      toast.success("App created successfully!");
    } catch (error) {
      toast.error("Failed to create app");
    }
  };

  const handleDeleteApp = async (appId: string) => {
    try {
      await deleteAppMutation.mutateAsync(appId);
      toast.success("App deleted successfully!");
    } catch (error) {
      toast.error("Failed to delete app");
    }
  };

  const sortedApps = [...apps].sort((a, b) => {
    switch (selectedSort.value) {
      case "newest":
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      case "oldest":
        return (
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
      case "name-asc":
        return a.name.localeCompare(b.name);
      case "name-desc":
        return b.name.localeCompare(a.name);
      default:
        return 0;
    }
  });

  // Loading state
  if (isLoading) {
    return (
      <div className="text-center py-16">
        <div className="mx-auto h-16 w-16 text-muted-foreground mb-6">
          <Refresh className="h-16 w-16 animate-spin" />
        </div>
        <h3 className="text-lg font-semibold text-foreground mb-2">Loading apps...</h3>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-16">
        <div className="mx-auto h-16 w-16 text-muted-foreground mb-6">
          <Refresh className="h-16 w-16" />
        </div>
        <h3 className="text-lg font-semibold text-foreground mb-2">Failed to load apps</h3>
        <p className="text-muted-foreground mb-4">
          {error instanceof Error ? error.message : "An error occurred"}
        </p>
        <Button onClick={() => refetch()} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  // Empty state
  if (apps.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="mx-auto h-16 w-16 text-muted-foreground mb-6">
          <Plus className="h-16 w-16" />
        </div>
        <h3 className="text-lg font-semibold text-foreground mb-2">No apps</h3>
        <p className="text-muted-foreground mb-8">
          Get started by creating your first app.
        </p>
        <div className="flex justify-center">
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button variant="default" size="lg" className="px-8 btn-gradient">
                <Plus className="h-5 w-5 mr-2" />
                Create Your First App
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Create New App</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleCreateApp} className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    App Name *
                  </label>
                  <Input
                    type="text"
                    required
                    value={formData.name}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                    placeholder="My E-commerce Site"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Description
                  </label>
                  <Textarea
                    rows={3}
                    value={formData.description}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    placeholder="Brief description of your application"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Domain
                  </label>
                  <Input
                    type="url"
                    value={formData.domain}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        domain: e.target.value,
                      }))
                    }
                    placeholder="https://myapp.com"
                  />
                </div>

                <div className="flex gap-3 pt-2">
                  <Button
                    type="submit"
                    variant="default"
                    disabled={createAppMutation.isPending}
                    className="flex-1"
                  >
                    {createAppMutation.isPending ? (
                      <Refresh className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    {createAppMutation.isPending ? "Creating..." : "Create App"}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsCreateModalOpen(false);
                      setFormData({ name: "", description: "", domain: "" });
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground mb-1">Apps</h2>
          <p className="text-muted-foreground">
            Manage your applications and their configurations.
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Select onValueChange={(value) => setSelectedSort(sortOptions.find(opt => opt.value === value) || sortOptions[0])}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button variant="default">
                <Plus className="h-4 w-4 mr-2" />
                Create App
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Create New App</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleCreateApp} className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    App Name *
                  </label>
                  <Input
                    type="text"
                    required
                    value={formData.name}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                    placeholder="My E-commerce Site"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Description
                  </label>
                  <Textarea
                    rows={3}
                    value={formData.description}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    placeholder="Brief description of your application"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Domain
                  </label>
                  <Input
                    type="url"
                    value={formData.domain}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        domain: e.target.value,
                      }))
                    }
                    placeholder="https://myapp.com"
                  />
                </div>

                <div className="flex gap-3 pt-2">
                  <Button
                    type="submit"
                    variant="default"
                    disabled={createAppMutation.isPending}
                    className="flex-1"
                  >
                    {createAppMutation.isPending ? (
                      <Refresh className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    {createAppMutation.isPending ? "Creating..." : "Create App"}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsCreateModalOpen(false);
                      setFormData({ name: "", description: "", domain: "" });
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Apps Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {sortedApps.map((app) => (
          <div key={app.id} className="app-card">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div className="h-10 w-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Eye className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-foreground truncate">
                    {app.name}
                  </h3>
                  <p className="text-sm text-muted-foreground truncate">
                    {app.domain || "No domain set"}
                  </p>
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVert className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onSelectApp(app)}>
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleDeleteApp(app.id)}
                    className="text-destructive"
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {app.description && (
              <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                {app.description}
              </p>
            )}

            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>Created {new Date(app.created_at).toLocaleDateString()}</span>
              <span className="px-2 py-1 bg-muted rounded text-xs">
                {app.status}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
