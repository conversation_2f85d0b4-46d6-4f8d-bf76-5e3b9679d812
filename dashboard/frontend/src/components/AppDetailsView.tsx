"use client";

import React, { Fragment, useCallback, useState, useEffect } from "react";
import { Api<PERSON><PERSON>, App, Wallet } from "@/lib/api";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Check,
  ArrowDown,
  ArrowUp,
  Settings,
  MoreVert,
  Plus,
  Trash,
  Wallet as WalletIcon,
  Key,
  Copy,
} from "iconoir-react";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Refresh } from "iconoir-react";
import { useAppDetails } from "@/hooks/useAppDetails";
import { apiClient } from "@/lib/api";

interface AppDetailsViewProps {
  app: App;
  onAppUpdated: (updatedApp: App) => void;
}

export const AppDetailsView = React.memo(function AppDetailsView({
  app,
  onAppUpdated: _onAppUpdated,
}: AppDetailsViewProps) {
  const {
    wallets,
    apiKeys,
    isLoading,
    error,
    loadData,
    refreshWallets,
    refreshApiKeys,
    clearError,
  } = useAppDetails(app);
  const toast = useToast();

  const tabs = [
    { name: "Wallets", icon: WalletIcon, component: "wallets" },
    { name: "API Keys", icon: Key, component: "api-keys" },
    { name: "Settings", icon: Settings, component: "settings" },
  ];

  return (
    <div className="space-y-8">
      {/* App Header */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-foreground">{app.name}</h1>
            <span className="status-badge status-active">{app.status}</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="text-sm font-medium text-muted-foreground mb-1 block">
              APP ID
            </label>
            <p className="font-mono text-sm text-foreground bg-muted/50 px-3 py-2 rounded-md border truncate">
              {app.id}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground mb-1 block">
              PRIMARY API KEY
            </label>
            <div className="flex items-center space-x-2">
              <p className="font-mono text-sm text-foreground bg-muted/50 px-3 py-2 rounded-md border flex-1 truncate min-w-0">
                ***...{app.api_key_preview}
              </p>
              <Button
                onClick={async () => {
                  try {
                    await navigator.clipboard.writeText(
                      `tk_${app.api_key_preview}`
                    );
                    toast.success("Primary API key copied to clipboard!");
                  } catch (error) {
                    toast.error("Failed to copy API key");
                  }
                }}
                variant="ghost"
                size="icon"
                title="Copy primary API key"
                className="h-9 w-9 flex-shrink-0"
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground mb-1 block">
              CREATED
            </label>
            <p className="text-sm text-foreground bg-muted/50 px-3 py-2 rounded-md border">
              {new Date(app.created_at).toLocaleDateString()}
            </p>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Tabs */}
      <div className="card p-0">
        <Tabs defaultValue="wallets" className="w-full">
          <div className="border-b border-border">
            <TabsList className="grid w-full grid-cols-3 bg-transparent h-auto p-0">
              {tabs.map((tab) => (
                <TabsTrigger
                  key={tab.name}
                  value={tab.component}
                  className="data-[state=active]:tab-active data-[state=inactive]:tab-inactive rounded-none border-b-2 border-transparent bg-transparent px-0 py-4 text-sm font-medium transition-all duration-200 w-full justify-center"
                >
                  <div className="flex items-center space-x-2">
                    <tab.icon className="h-5 w-5" />
                    <span>{tab.name}</span>
                  </div>
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          <div className="p-6">
            <TabsContent value="wallets" className="mt-0">
              <WalletsTab
                appId={app.id}
                wallets={wallets}
                onRefresh={loadData}
              />
            </TabsContent>
            <TabsContent value="api-keys" className="mt-0">
              <ApiKeysTab
                app={app}
                appId={app.id}
                apiKeys={apiKeys}
                onRefresh={loadData}
              />
            </TabsContent>
            <TabsContent value="settings" className="mt-0">
              <SettingsTab appId={app.id} />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
});

// Wallets Tab Component
function WalletsTab({
  appId,
  wallets,
  onRefresh,
}: {
  appId: string;
  wallets: Wallet[];
  onRefresh: () => void;
}) {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [network, setNetwork] = useState("ethereum");
  const [isCreating, setIsCreating] = useState(false);
  const toast = useToast();

  const networkOptions = [
    { value: "ethereum", label: "Ethereum" },
    { value: "polygon", label: "Polygon" },
    { value: "bsc", label: "BSC" },
    { value: "arbitrum", label: "Arbitrum" },
    { value: "optimism", label: "Optimism" },
    { value: "solana", label: "Solana" },
    { value: "bitcoin", label: "Bitcoin" },
  ];

  const handleCreateWallet = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreating(true);

    try {
      await apiClient.createAppWallet(appId, { network });
      toast.success("Wallet created successfully!");
      setShowCreateForm(false);
      setNetwork("ethereum");
      onRefresh();
    } catch (error) {
      console.error("Failed to create wallet:", error);
      toast.error("Failed to create wallet");
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground mb-1">
            Wallets
          </h2>
          <p className="text-muted-foreground">
            Manage wallets for this application.
          </p>
        </div>
        <Button
          onClick={() => setShowCreateForm(!showCreateForm)}
          variant="default"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Wallet
        </Button>
      </div>

      {/* Create Wallet Form */}
      {showCreateForm && (
        <div className="card">
          <form onSubmit={handleCreateWallet} className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground mb-2 block">
                Network
              </label>
              <Select onValueChange={setNetwork} defaultValue="ethereum">
                <SelectTrigger>
                  <SelectValue placeholder="Select a network" />
                </SelectTrigger>
                <SelectContent>
                  {networkOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-3">
              <Button type="submit" variant="default" disabled={isCreating}>
                {isCreating ? (
                  <Refresh className="h-4 w-4 animate-spin mr-1" />
                ) : (
                  <Check className="h-4 w-4 mr-1" />
                )}
                {isCreating ? "Creating..." : "Create Wallet"}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowCreateForm(false);
                  setNetwork("ethereum");
                }}
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Wallets List */}
      {wallets.length === 0 ? (
        <div className="text-center py-12">
          <div className="mx-auto h-16 w-16 text-muted-foreground mb-4">
            <WalletIcon className="h-16 w-16" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">
            No wallets
          </h3>
          <p className="text-muted-foreground">
            Create your first wallet to get started.
          </p>
        </div>
      ) : (
        <div className="grid gap-4">
          {wallets.map((wallet) => (
            <div key={wallet.id} className="app-card">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <div className="h-10 w-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <WalletIcon className="h-5 w-5 text-primary" />
                  </div>
                  <div className="min-w-0 flex-1 pr-4">
                    <h3 className="font-medium text-foreground">
                      {wallet.network}
                    </h3>
                    <p className="text-sm text-muted-foreground truncate">
                      {wallet.wallet_address}
                    </p>
                  </div>
                </div>
                <span className="status-badge status-active flex-shrink-0">
                  Active
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// API Keys Tab Component
function ApiKeysTab({
  app,
  appId,
  apiKeys,
  onRefresh,
}: {
  app: App;
  appId: string;
  apiKeys: ApiKey[];
  onRefresh: () => void;
}) {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [name, setName] = useState("");
  const [permissions, setPermissions] = useState("read");
  const [isCreating, setIsCreating] = useState(false);
  const [newApiKey, setNewApiKey] = useState<string | null>(null);
  const toast = useToast();

  const permissionsOptions = [
    { value: "read", label: "Read Only" },
    { value: "write", label: "Read & Write" },
    { value: "admin", label: "Admin" },
  ];

  const handleCreateApiKey = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreating(true);

    try {
      const response = await apiClient.createAppApiKey(appId, {
        name,
        permissions,
      });
      setNewApiKey(response.api_key);
      setShowCreateForm(false);
      setName("");
      setPermissions("read");
      onRefresh();
      toast.success("API key created successfully!");
    } catch (error) {
      console.error("Failed to create API key:", error);
      toast.error("Failed to create API key");
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteApiKey = async (keyId: string) => {
    try {
      await apiClient.deleteAppApiKey(appId, keyId);
      onRefresh();
      toast.success("API key deleted successfully!");
    } catch (error) {
      console.error("Failed to delete API key:", error);
      toast.error("Failed to delete API key");
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground mb-1">
            API Keys
          </h2>
          <p className="text-muted-foreground">
            Manage secondary API keys for this application.
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)} variant="default">
          <Plus className="h-4 w-4 mr-1" />
          Add
        </Button>
      </div>

      {/* Create API Key Form */}
      {showCreateForm && (
        <div className="card">
          <form onSubmit={handleCreateApiKey} className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground mb-2 block">
                API Key Name
              </label>
              <Input
                type="text"
                required
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g., Mobile App Key"
              />
            </div>
            <div>
              <label className="text-sm font-medium text-foreground mb-2 block">
                Permissions
              </label>
              <Select onValueChange={setPermissions} defaultValue="read">
                <SelectTrigger>
                  <SelectValue placeholder="Select permissions" />
                </SelectTrigger>
                <SelectContent>
                  {permissionsOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-3 pt-2">
              <Button type="submit" variant="default" disabled={isCreating}>
                {isCreating ? (
                  <Refresh className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Check className="h-4 w-4 mr-2" />
                )}
                {isCreating ? "Generating..." : "Save"}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowCreateForm(false);
                  setName("");
                  setPermissions("read");
                }}
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* New API Key Display */}
      {newApiKey && (
        <div className="card bg-green-50 border-green-200">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-green-800 mb-2">
                New API Key Generated!
              </h3>
              <p className="text-green-700 mb-3">
                Make sure to copy this key now. You won&apos;t be able to see it
                again.
              </p>
              <div className="bg-white border border-green-300 rounded-lg p-3">
                <p className="font-mono text-sm text-green-800 break-all">
                  {newApiKey}
                </p>
              </div>
            </div>
            <Button
              onClick={() => setNewApiKey(null)}
              variant="ghost"
              size="sm"
            >
              Got it
            </Button>
          </div>
        </div>
      )}

      {/* API Keys List */}
      {apiKeys.length === 0 ? (
        <div className="text-center py-12">
          <div className="mx-auto h-16 w-16 text-muted-foreground mb-4">
            <Key className="h-16 w-16" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">
            No API keys
          </h3>
          <p className="text-muted-foreground">
            Generate your first API key to get started.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {apiKeys.map((apiKey) => (
            <div key={apiKey.id} className="app-card">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0 pr-4">
                  <h3 className="font-medium text-foreground mb-1 truncate">
                    {apiKey.name}
                  </h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    {apiKey.permissions} • Created{" "}
                    {new Date(apiKey.created_at).toLocaleDateString()}
                  </p>
                  <div className="bg-muted/50 border border-border rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <p className="font-mono text-sm text-muted-foreground truncate min-w-0 flex-1 pr-2">
                        tk_***{apiKey.id.slice(-8)}
                      </p>
                      <Button
                        onClick={async () => {
                          try {
                            await navigator.clipboard.writeText(
                              `tk_***${apiKey.id.slice(-8)}`
                            );
                            toast.success("API key copied to clipboard!");
                          } catch (error) {
                            toast.error("Failed to copy API key");
                          }
                        }}
                        variant="ghost"
                        size="icon"
                        title="Copy API key"
                        className="h-8 w-8 flex-shrink-0"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open options</span>
                      <MoreVert className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => handleDeleteApiKey(apiKey.id)}
                      className="text-destructive"
                    >
                      <Trash className="mr-1 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Settings Tab Component
function SettingsTab({ appId }: { appId: string }) {
  const [settings, setSettings] = useState({
    googleClientId: "",
    googleClientSecret: "",
    defaultNetwork: "ethereum",
    autoCreateWallets: false,
    googleAuthEnabled: false,
    emailOtpEnabled: false,
    authType: "password" as "password" | "otp" | "both",
    emailVerificationRequired: false,
  });
  const [domains, setDomains] = useState<Array<{ id: string; origin: string; created_at: string }>>([]);
  const [newDomain, setNewDomain] = useState<string>("");
  const [isSaving, setIsSaving] = useState(false);
  const toast = useToast();

  const loadSettings = useCallback(async () => {
    try {
      const response = await apiClient.getAppSettings(appId);
      setSettings({
        googleClientId: response.google_client_id || "",
        googleClientSecret: response.google_client_secret || "",
        defaultNetwork: response.default_wallet_network || "ethereum",
        autoCreateWallets: response.auto_create_wallets ,
        googleAuthEnabled: response.google_auth_enabled ,
        emailOtpEnabled: response.email_otp_enabled ,
        authType: response.auth_type || "password",
        emailVerificationRequired:
          response.email_verification_required ,
      });
    } catch (error) {
      console.error("Failed to load settings:", error);
    }
  }, [appId]);

  const loadDomains = useCallback(async () => {
    try {
      const list = await apiClient.getAppDomains(appId);
      setDomains(list);
    } catch (error) {
      console.error("Failed to load app domains:", error);
    }
  }, [appId]);

  useEffect(() => {
    loadSettings();
    loadDomains();
  }, [loadSettings, loadDomains]);

  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      const backendSettings = {
        google_client_id: settings.googleClientId,
        google_client_secret: settings.googleClientSecret,
        default_wallet_network: settings.defaultNetwork,
        auto_create_wallets: settings.autoCreateWallets,
        google_auth_enabled: settings.googleAuthEnabled,
        email_otp_enabled: settings.emailOtpEnabled,
        auth_type: settings.authType,
        email_verification_required: settings.emailVerificationRequired,
      };

      await apiClient.updateAppSettings(appId, backendSettings);
      toast.success("Settings saved successfully!");
    } catch (error) {
      console.error("Failed to save settings:", error);
      toast.error("Failed to save settings");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-foreground mb-1">Settings</h2>
        <p className="text-muted-foreground">
          Configure your application settings.
        </p>
      </div>

      {/* Settings Form */}
      <div className="card">
        <form onSubmit={handleSaveSettings} className="space-y-6">
          {/* Google OAuth Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-foreground">
              Google OAuth Configuration
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Google Client ID
                </label>
                <Input
                  type="text"
                  value={settings.googleClientId}
                  onChange={(e) =>
                    setSettings((prev) => ({
                      ...prev,
                      googleClientId: e.target.value,
                    }))
                  }
                  placeholder="Enter your Google Client ID"
                />
              </div>
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Google Client Secret
                </label>
                <Input
                  type="password"
                  value={settings.googleClientSecret}
                  onChange={(e) =>
                    setSettings((prev) => ({
                      ...prev,
                      googleClientSecret: e.target.value,
                    }))
                  }
                  placeholder="Enter your Google Client Secret"
                />
              </div>
            </div>
          </div>

          {/* Authentication Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-foreground">
              Authentication Configuration
            </h3>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Authentication Type
                </label>
                <Select
                  onValueChange={(value) =>
                    setSettings((prev) => ({
                      ...prev,
                      authType: value as "password" | "otp" | "both",
                    }))
                  }
                  defaultValue={settings.authType}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select authentication type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="password">Password Only</SelectItem>
                    <SelectItem value="otp">OTP Only</SelectItem>
                    <SelectItem value="both">Password & OTP</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  Choose how users can authenticate with your app
                </p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="emailVerificationRequired"
                    checked={settings.emailVerificationRequired}
                    onCheckedChange={(checked) =>
                      setSettings((prev) => ({
                        ...prev,
                        emailVerificationRequired: checked === true,
                      }))
                    }
                  />
                  <label
                    htmlFor="emailVerificationRequired"
                    className="text-sm font-medium text-foreground"
                  >
                    Require Email Verification
                  </label>
                </div>
                <p className="text-xs text-muted-foreground ml-6">
                  Users must verify their email before they can use the app
                </p>
              </div>
            </div>
          </div>

          {/* Wallet Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-foreground">
              Wallet Configuration
            </h3>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Default Network
                </label>
                <Select
                  onValueChange={(value) =>
                    setSettings((prev) => ({ ...prev, defaultNetwork: value }))
                  }
                  defaultValue={settings.defaultNetwork}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select default network" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ethereum">Ethereum</SelectItem>
                    <SelectItem value="polygon">Polygon</SelectItem>
                    <SelectItem value="bsc">BSC</SelectItem>
                    <SelectItem value="arbitrum">Arbitrum</SelectItem>
                    <SelectItem value="optimism">Optimism</SelectItem>
                    <SelectItem value="solana">Solana</SelectItem>
                    <SelectItem value="bitcoin">Bitcoin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="autoCreateWallets"
                    checked={settings.autoCreateWallets}
                    onCheckedChange={(checked) =>
                      setSettings((prev) => ({
                        ...prev,
                        autoCreateWallets: checked === true,
                      }))
                    }
                  />
                  <label
                    htmlFor="autoCreateWallets"
                    className="text-sm font-medium text-foreground"
                  >
                    Auto-create wallets
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="googleAuthEnabled"
                    checked={settings.googleAuthEnabled}
                    onCheckedChange={(checked) =>
                      setSettings((prev) => ({
                        ...prev,
                        googleAuthEnabled: checked === true,
                      }))
                    }
                  />
                  <label
                    htmlFor="googleAuthEnabled"
                    className="text-sm font-medium text-foreground"
                  >
                    Google Auth Enabled
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="emailOtpEnabled"
                    checked={settings.emailOtpEnabled}
                    onCheckedChange={(checked) =>
                      setSettings((prev) => ({
                        ...prev,
                        emailOtpEnabled: checked === true,
                      }))
                    }
                  />
                  <label
                    htmlFor="emailOtpEnabled"
                    className="text-sm font-medium text-foreground"
                  >
                    Email OTP Enabled
                  </label>
                </div>
              </div>

      {/* Domain Whitelisting */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-foreground">Domain Whitelisting</h3>
        <p className="text-sm text-muted-foreground">
          Only requests from the following origins are allowed for this app. Localhost is whitelisted by default.
        </p>
        <div className="flex items-center gap-2">
          <Input
            type="text"
            placeholder="https://example.com"
            value={newDomain}
            onChange={(e) => setNewDomain(e.target.value)}
          />
          <Button
            type="button"
            onClick={async () => {
              if (!newDomain.trim()) return;
              try {
                const added = await apiClient.addAppDomain(appId, newDomain);
                setDomains((prev) => [...prev, added]);
                setNewDomain("");
                toast.success("Domain added");
              } catch (err) {
                console.error(err);
                toast.error("Failed to add domain");
              }
            }}
          >
            Add Domain
          </Button>
        </div>
        <div className="space-y-2">
          {domains.length === 0 ? (
            <p className="text-sm text-muted-foreground">No domains added yet.</p>
          ) : (
            <ul className="divide-y divide-border rounded-md border">
              {domains.map((d) => (
                <li key={d.id} className="flex items-center justify-between px-3 py-2">
                  <span className="text-sm">{d.origin}</span>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={async () => {
                      try {
                        await apiClient.deleteAppDomain(appId, d.id);
                        setDomains((prev) => prev.filter((x) => x.id !== d.id));
                        toast.success("Domain removed");
                      } catch (err) {
                        console.error(err);
                        toast.error("Failed to remove domain");
                      }
                    }}
                  >
                    Remove
                  </Button>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end pt-4">
            <Button type="submit" variant="default" disabled={isSaving}>
              {isSaving ? (
                <Refresh className="h-4 w-4 animate-spin mr-1" />
              ) : (
                <Check className="h-4 w-4 mr-1" />
              )}
              {isSaving ? "Saving..." : "Save Settings"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
