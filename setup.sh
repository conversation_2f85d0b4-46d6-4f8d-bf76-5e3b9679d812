#!/bin/bash

# Tokai Quick Setup Script
# Simple deployment for development and production

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed."
}

# Generate secure keys
generate_keys() {
    print_status "Generating secure keys..."
    
    JWT_SECRET=$(openssl rand -hex 32)
    WALLET_ENCRYPTION_KEY=$(openssl rand -hex 32)
    REDIS_PASSWORD=$(openssl rand -hex 16)
    
    print_success "Secure keys generated."
}

# Create environment file
create_env() {
    print_status "Creating environment configuration..."
    
    if [ -f .env ]; then
        print_warning ".env file already exists. Backing up to .env.backup"
        cp .env .env.backup
    fi
    
    cat > .env << EOF
# ========================================
# Tokai Environment Configuration
# ========================================

# Security Keys (Auto-generated)
JWT_SECRET=$JWT_SECRET
WALLET_ENCRYPTION_KEY=$WALLET_ENCRYPTION_KEY

# Database Configuration
DATABASE_URL=file:/app/data/wallet_service.db

# Application URLs
BASE_URL=http://localhost:3001
FRONTEND_URL=http://localhost:3000

# Redis Configuration
REDIS_PASSWORD=$REDIS_PASSWORD

# OAuth Providers (Configure as needed)
# Google
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Discord
DISCORD_CLIENT_ID=
DISCORD_CLIENT_SECRET=

# Twitter
TWITTER_CLIENT_ID=
TWITTER_CLIENT_SECRET=

# GitHub
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# AWS Configuration (for SMS)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1

# Feature Flags
AUTO_CREATE_WALLET=false
DEFAULT_WALLET_NETWORK=ethereum
EMAIL_VERIFICATION_REQUIRED=false

# Monitoring
GRAFANA_PASSWORD=admin

# PostgreSQL (Optional - for production)
POSTGRES_USER=tokai
POSTGRES_PASSWORD=tokai_password
POSTGRES_DB=tokai
EOF

    print_success ".env file created successfully."
    print_warning "Please edit .env file to configure OAuth providers and other settings."
}

# Deploy services
deploy_services() {
    print_status "Deploying Tokai services..."
    
    # Pull latest images
    docker-compose pull
    
    # Build and start services
    docker-compose up -d --build
    
    print_success "Services deployed successfully."
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for backend
    print_status "Waiting for backend API..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:3001/health &> /dev/null; then
            print_success "Backend API is ready."
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Backend API failed to start within 60 seconds."
        exit 1
    fi
    
    # Wait for frontend
    print_status "Waiting for frontend..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:3000 &> /dev/null; then
            print_success "Frontend is ready."
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_warning "Frontend may still be starting up."
    fi
}

# Show deployment info
show_info() {
    echo ""
    echo "🎉 Tokai has been deployed successfully!"
    echo ""
    echo "📱 Access URLs:"
    echo "   Dashboard:  http://localhost:3000"
    echo "   API:        http://localhost:3001"
    echo "   Health:     http://localhost:3001/health"
    echo ""
    echo "📊 Monitoring (Optional):"
    echo "   Prometheus: http://localhost:9090"
    echo "   Grafana:    http://localhost:3001 (admin/admin)"
    echo ""
    echo "🔧 Useful Commands:"
    echo "   View logs:     docker-compose logs -f"
    echo "   Stop services: docker-compose down"
    echo "   Restart:       docker-compose restart"
    echo "   Update:        git pull && docker-compose up -d --build"
    echo ""
    echo "📚 Documentation:"
    echo "   Deployment Guide: docs/DEPLOYMENT_GUIDE.md"
    echo "   API Reference:    docs/API_QUICK_REFERENCE.md"
    echo ""
    print_warning "Don't forget to configure OAuth providers in .env file for social login!"
}

# Production setup
setup_production() {
    print_status "Setting up production environment..."
    
    # Check if domain is provided
    if [ -z "$1" ]; then
        print_error "Please provide your domain: ./setup.sh production your-domain.com"
        exit 1
    fi
    
    DOMAIN=$1
    
    # Update .env for production
    sed -i "s|BASE_URL=http://localhost:3001|BASE_URL=https://$DOMAIN|" .env
    sed -i "s|FRONTEND_URL=http://localhost:3000|FRONTEND_URL=https://$DOMAIN|" .env
    
    print_success "Production environment configured for domain: $DOMAIN"
    print_warning "Please configure SSL certificates and update DNS records."
}

# Development setup
setup_development() {
    print_status "Setting up development environment..."
    
    # Use development compose file
    if [ -f docker-compose.dev.yml ]; then
        COMPOSE_FILE="docker-compose.dev.yml"
    else
        COMPOSE_FILE="docker-compose.yml"
    fi
    
    print_success "Development environment configured."
}

# Main function
main() {
    echo "🚀 Tokai Quick Setup"
    echo "===================="
    echo ""
    
    # Check dependencies
    check_docker
    
    # Generate keys
    generate_keys
    
    # Create environment file
    create_env
    
    # Setup based on environment
    if [ "$1" = "production" ]; then
        setup_production $2
    else
        setup_development
    fi
    
    # Deploy services
    deploy_services
    
    # Wait for services
    wait_for_services
    
    # Show info
    show_info
}

# Show usage
usage() {
    echo "Tokai Quick Setup Script"
    echo ""
    echo "Usage: $0 [ENVIRONMENT] [DOMAIN]"
    echo ""
    echo "Environments:"
    echo "  dev         Development setup (default)"
    echo "  production  Production setup with domain"
    echo ""
    echo "Examples:"
    echo "  $0                    # Development setup"
    echo "  $0 dev               # Development setup"
    echo "  $0 production example.com  # Production setup"
    echo ""
    echo "For more information, see docs/DEPLOYMENT_GUIDE.md"
}

# Handle arguments
case "${1:-dev}" in
    "dev"|"development")
        main "dev"
        ;;
    "prod"|"production")
        if [ -z "$2" ]; then
            print_error "Domain is required for production setup."
            usage
            exit 1
        fi
        main "production" "$2"
        ;;
    "help"|"-h"|"--help")
        usage
        ;;
    *)
        print_error "Unknown environment: $1"
        usage
        exit 1
        ;;
esac
