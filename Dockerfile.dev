# Development Dockerfile for Tokai
FROM oven/bun:1.1.38-alpine

# Install development dependencies
RUN apk add --no-cache \
    git \
    curl \
    sqlite \
    python3 \
    make \
    g++

# Set working directory
WORKDIR /app

# Install global development tools
RUN bun add -g nodemon tsx

# Copy package files first for better caching
COPY package.json bun.lockb* ./
COPY packages/ ./packages/
COPY dashboard/backend/package.json ./dashboard/backend/
COPY dashboard/frontend/package.json ./dashboard/frontend/

# Install all dependencies
RUN bun install

# Create directories for development
RUN mkdir -p /app/data /app/logs

# Expose development ports
EXPOSE 3001 3000

# Default command (can be overridden in docker-compose)
CMD ["bun", "run", "dashboard/backend/src/index.ts"]