# 🚀 Tokai - Self-Hosted Wallet Infrastructure

Complete self-hosted wallet infrastructure platform with embedded wallets, multi-chain support, and developer-friendly APIs.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)

## 🎯 What is Tokai?

Tokai is a **self-hosted wallet infrastructure platform** that provides everything you need to integrate wallets into your applications, with full control over your data and infrastructure.

### ✨ Key Features

- **🔐 Embedded Wallets** - Backend-generated, encrypted wallets
- **🌐 Multi-Chain Support** - Ethereum, Solana, Bitcoin, Cardano, and 10+ more
- **🔗 External Wallets** - MetaMask, Coinbase Wallet integration
- **📱 Mobile Deep Linking** - Cross-platform wallet connections
- **🔑 Smart Accounts** - ERC-4337 account abstraction
- **📊 Analytics** - Built-in event tracking and metrics
- **🔒 Security First** - Enterprise-grade security and encryption
- **🚀 Single Docker Image** - Easy deployment and scaling

### 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              🚀 Tokai Platform                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────────────────────┐    ┌─────────────────────────────────┐     │
│  │      🎨 Frontend Dashboard      │    │      🔧 Backend API Server      │     │
│  │         (Next.js 14)            │    │        (Express.js)             │     │
│  ├─────────────────────────────────┤    ├─────────────────────────────────┤     │
│  │  👤 User Management             │    │  🔐 Authentication              │     │
│  │  💰 Wallet Dashboard            │    │  🏦 Wallet Management           │     │
│  │  📊 Analytics & Metrics         │    │  🌐 Multi-Chain Support         │     │
│  │  ⚙️  Settings & Config          │    │  🔑 Smart Accounts (ERC-4337)   │     │
│  │  🔍 Developer Tools             │    │  📱 Mobile Deep Linking         │     │
│  └─────────────────────────────────┘    └─────────────────────────────────┘     │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│                              📦 Core Packages                                   │
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   ⚛️ React SDK  │  │  🏦 Wallet      │  │  🔗 Wallet      │  │  🎣 React   │ │
│  │                 │  │  Managers       │  │  Connectors     │  │  Hooks      │ │
│  │ • TokaiProvider │  │ • Multi-Chain   │  │ • External      │  │ • useWallet │ │
│  │ • TokaiAuth     │  │ • Embedded      │  │ • MetaMask      │  │ • useChain  │ │
│  │ • Components    │  │ • Smart Acc.    │  │ • Coinbase      │  │ • useAuth   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│                              🗄️  Data Layer                                     │
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   💾 Database   │  │   ⚡ Cache       │  │   📊 Analytics  │  │   🔒 Vault  │ │
│  │                 │  │                 │  │                 │  │             │ │
│  │ • SQLite        │  │ • Redis         │  │ • Event Tracking│  │ • Encrypted │ │
│  │ • PostgreSQL    │  │ • In-Memory     │  │ • User Metrics  │  │ • Wallets   │ │
│  │ • Migrations    │  │ • Session Store │  │ • Performance   │  │ • Keys      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 📁 Project Structure

```
tokai/
├── 📦 packages/                    # Core packages
│   ├── react/                     # React SDK & components
│   ├── wallet-managers/           # Multi-chain wallet managers
│   ├── wallet-connectors/         # External wallet connections
│   ├── wallet-types/              # TypeScript type definitions
│   └── hooks/                     # React hooks for wallets
├── 🏠 dashboard/                  # Admin dashboard
│   ├── frontend/                  # Next.js dashboard UI
│   └── backend/                   # Express.js API server
├── 🌐 web/                        # Documentation website
│   ├── docs/                      # Docusaurus documentation
│   └── website/                   # Marketing website
├── 🧪 sandbox/                    # Development sandbox
├── 📚 docs/                       # Project documentation
├── 🐳 docker-compose.yml          # Production deployment
├── 🐳 Dockerfile                  # Single image build
├── 🚀 setup.sh                    # Quick setup script
└── 📋 deploy.sh                   # Advanced deployment
```

### 📦 Core Packages

#### `packages/react` - React SDK
```typescript
import { TokaiProvider, useTokai } from '@tokai/react';

// Easy wallet integration
const App = () => (
  <TokaiProvider apiKey="your-api-key">
    <WalletConnect />
  </TokaiProvider>
);
```

#### `packages/wallet-managers` - Multi-Chain Support
```typescript
import { EthereumWalletManager } from '@tokai/wallet-managers';

// Manage wallets across chains
const manager = new EthereumWalletManager();
await manager.connect('metamask');
```

#### `packages/wallet-connectors` - External Wallets
```typescript
import { EthereumConnector } from '@tokai/wallet-connectors';

// Connect to external wallets
const connector = new EthereumConnector();
await connector.connect('metamask');
```

#### `packages/hooks` - React Hooks
```typescript
import { useEthereumWallet } from '@tokai/hooks';

// React hooks for wallet state
const { account, connect, disconnect } = useEthereumWallet();
```

### 🏠 Dashboard Components

#### `dashboard/frontend` - Admin Dashboard
- **User Management** - Register, login, profile management
- **Wallet Dashboard** - View balances, transactions, create wallets
- **Analytics** - User metrics, transaction analytics
- **Settings** - OAuth providers, security settings

#### `dashboard/backend` - API Server
- **Authentication** - JWT, OAuth, session management
- **Wallet Management** - Create, deploy, recover wallets
- **Multi-Chain Support** - Ethereum, Solana, Bitcoin, etc.
- **Analytics** - Event tracking, metrics collection

## 🚀 Quick Start

### 1. One-Command Deployment
```bash
# Clone repository
git clone https://github.com/your-org/tokai.git
cd tokai

# Development setup
./setup.sh

# Production setup
./setup.sh production your-domain.com
```

### 2. Manual Deployment
```bash
# Build and deploy
docker-compose up -d

# Access your deployment
# Dashboard: http://localhost:3000
# API: http://localhost:3001
# Health: http://localhost:3001/health
```

### 3. Development Setup
```bash
# Install dependencies
pnpm install

# Start development
pnpm dev

# Build packages
pnpm build
```

## 🔌 API Integration

### Authentication
```bash
# Register user
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password","fullName":"John Doe"}'

# Login
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

### Wallet Management
```bash
# Create wallet
curl -X POST http://localhost:3001/api/wallets \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"network":"ethereum"}'

# Get balance
curl -X GET http://localhost:3001/api/wallets/{walletId}/balance \
  -H "Authorization: Bearer <token>"
```

### React SDK Usage
```typescript
import { TokaiProvider, useTokai } from '@tokai/react';

function App() {
  return (
    <TokaiProvider apiKey="your-api-key" backendUrl="http://localhost:3001">
      <WalletApp />
    </TokaiProvider>
  );
}

function WalletApp() {
  const { user, wallets, createWallet, connectExternalWallet } = useTokai();

  const handleCreateWallet = async () => {
    await createWallet('ethereum');
  };

  return (
    <div>
      {user ? (
        <div>
          <h2>Welcome, {user.fullName}!</h2>
          <button onClick={handleCreateWallet}>Create Wallet</button>
          {wallets.map(wallet => (
            <div key={wallet.id}>
              {wallet.network}: {wallet.wallet_address}
            </div>
          ))}
        </div>
      ) : (
        <TokaiAuth />
      )}
    </div>
  );
}
```

## 🌐 Supported Chains & Wallets

### Chains
- **Ethereum** - Mainnet, Goerli, Sepolia
- **Solana** - Mainnet, Devnet, Testnet
- **Bitcoin** - Mainnet, Testnet
- **Cardano** - Mainnet, Testnet
- **Polygon** - Mainnet, Mumbai
- **BSC** - Mainnet, Testnet
- **Avalanche** - C-Chain, Fuji
- **Arbitrum** - One, Nova, Goerli
- **Optimism** - Mainnet, Goerli
- **Algorand** - Mainnet, Testnet
- **Cosmos** - Hub, Osmosis
- **Near** - Mainnet, Testnet
- **Polkadot** - Relay Chain, Parachains
- **Tron** - Mainnet, Shasta

### Wallets
- **Embedded** - Backend-generated, encrypted
- **MetaMask** - Browser extension
- **Coinbase Wallet** - Mobile & browser
- **Phantom** - Solana wallet
- **Backpack** - Multi-chain wallet
- **Solflare** - Solana wallet
- **And 20+ more...**

## 🛠️ Development

### Prerequisites
- **Node.js** 18+ or **Bun** 1.0+
- **Docker** & **Docker Compose**
- **pnpm** (recommended) or npm

### Development Commands
```bash
# Install dependencies
pnpm install

# Start development
pnpm dev

# Build all packages
pnpm build

# Run tests
pnpm test

# Type checking
pnpm type-check

# Linting
pnpm lint
```

### Package Development
```bash
# Build specific package
cd packages/react && pnpm build

# Watch mode
cd packages/react && pnpm dev

# Test package
cd packages/react && pnpm test
```

### Adding New Chains
```typescript
// 1. Create wallet manager
// packages/wallet-managers/src/newchain/NewChainWalletManager.ts

// 2. Add to index
// packages/wallet-managers/src/index.ts

// 3. Add types
// packages/wallet-types/src/wallet.ts

// 4. Add to dashboard
// dashboard/backend/src/services/walletService.ts
```

## 🚀 Deployment

### Production Deployment
```bash
# 1. Configure environment
cp .env.example .env
# Edit .env with your settings

# 2. Deploy with Docker
docker-compose up -d

# 3. Configure domain and SSL
# Update DNS and SSL certificates

# 4. Set up monitoring
# Access Prometheus: http://your-domain:9090
# Access Grafana: http://your-domain:3001
```

### Environment Variables
```bash
# Security
JWT_SECRET=your-super-secret-jwt-key
WALLET_ENCRYPTION_KEY=your-wallet-encryption-key

# Database
DATABASE_URL=file:/app/data/wallet_service.db

# URLs
BASE_URL=https://your-domain.com
FRONTEND_URL=https://your-domain.com

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
# ... other providers
```

### Monitoring & Health
```bash
# Health check
curl http://localhost:3001/health

# API status
curl http://localhost:3001/api/status

# View logs
docker-compose logs -f
```

## 📚 Documentation

- **[Deployment Guide](./docs/DEPLOYMENT_GUIDE.md)** - Complete deployment instructions
- **[API Reference](./docs/API_QUICK_REFERENCE.md)** - All API endpoints with examples
- **[Wallet Types](./docs/WALLET_TYPE_EXPLANATION.md)** - Supported wallets and chains
- **[Development Guide](./docs/README.md)** - Development documentation

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Development Guidelines
- Use **TypeScript** for all new code
- Follow **ESLint** rules
- Write **tests** for new features
- Update **documentation** for changes
- Use **conventional commits**

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](./docs/)
- **Issues**: [GitHub Issues](https://github.com/your-org/tokai/issues)
- **Discord**: [Join our community](https://discord.gg/tokai)
- **Email**: <EMAIL>

---

**🎉 Built with ❤️ by the Tokai team**

> **Self-hosted wallet infrastructure that scales with your needs.**
