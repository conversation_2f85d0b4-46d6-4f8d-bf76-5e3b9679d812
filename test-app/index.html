<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tokai Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2a1a2a 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .title {
            font-size: 48px;
            font-weight: 800;
            background: linear-gradient(45deg, #6366f1, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 16px;
        }
        
        .subtitle {
            font-size: 18px;
            color: #a0a0a0;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .demo-section {
            background: #2a2a2a;
            border-radius: 16px;
            border: 1px solid #404040;
            padding: 40px;
            margin-bottom: 40px;
        }
        
        .demo-title {
            font-size: 24px;
            margin-bottom: 16px;
            color: #ffffff;
        }
        
        .demo-description {
            color: #a0a0a0;
            margin-bottom: 32px;
            line-height: 1.6;
        }
        
        .demo-area {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
        }
        
        .connect-button {
            background: #6366f1;
            color: #ffffff;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .connect-button:hover {
            background: #5855eb;
            transform: translateY(-1px);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 60px;
        }
        
        .feature-card {
            background: #2a2a2a;
            border-radius: 16px;
            border: 1px solid #404040;
            padding: 32px;
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .feature-title {
            font-size: 20px;
            margin-bottom: 12px;
            color: #ffffff;
        }
        
        .feature-description {
            color: #a0a0a0;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            color: #10b981;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .feature-list li:before {
            content: "✓ ";
        }
        
        .status {
            background: #1a2e1a;
            border: 1px solid #16a34a;
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
            color: #16a34a;
        }
        
        .code-block {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
            font-family: 'Fira Code', monospace;
            font-size: 14px;
            color: #a0a0a0;
            overflow-x: auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">🚀 Tokai Demo</h1>
            <p class="subtitle">
                Experience seamless Web3 onboarding with social authentication and embedded wallets
            </p>
        </header>
        
        <div class="demo-section">
            <h2 class="demo-title">🔗 Connect Button Demo</h2>
            <p class="demo-description">
                Click the button below to see the authentication flow in action
            </p>
            
            <div class="demo-area">
                <button class="connect-button" onclick="simulateConnect()">
                    🔗 Connect Wallet
                </button>
                
                <div id="connection-status" style="margin-top: 20px;"></div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">💻 Integration Code</h2>
            <p class="demo-description">
                Here's how easy it is to integrate the authentication in your React app
            </p>
            
            <div class="code-block">
<pre>import React from 'react';
import { TokaiProvider, TokaiConnectButton } from '@tokai/react';

function App() {
  const config = {
    apiUrl: 'http://localhost:3001/api',
    apiKey: 'your-api-key',
    appName: 'My DApp',
    embeddedWallets: true,
    socialLogins: [
      { id: 'google', enabled: true },
      { id: 'discord', enabled: true },
      { id: 'twitter', enabled: true },
      { id: 'github', enabled: true },
    ],
  };

  return (
    &lt;TokaiProvider config={config}&gt;
      &lt;TokaiConnectButton /&gt;
    &lt;/TokaiProvider&gt;
  );
}</pre>
            </div>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🔐</div>
                <h3 class="feature-title">Social Authentication</h3>
                <p class="feature-description">
                    One-click login with Google, Discord, Twitter, and GitHub
                </p>
                <ul class="feature-list">
                    <li>OAuth 2.0 integration</li>
                    <li>Account linking</li>
                    <li>Progressive onboarding</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">💎</div>
                <h3 class="feature-title">Embedded Wallets</h3>
                <p class="feature-description">
                    Smart accounts with social recovery and gasless transactions
                </p>
                <ul class="feature-list">
                    <li>No seed phrases</li>
                    <li>Social recovery</li>
                    <li>Account abstraction</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🌐</div>
                <h3 class="feature-title">Cross-App Identity</h3>
                <p class="feature-description">
                    Portable identity and wallets across applications
                </p>
                <ul class="feature-list">
                    <li>Global user ID</li>
                    <li>Wallet portability</li>
                    <li>Unified identity</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">Developer Experience</h3>
                <p class="feature-description">
                    Easy-to-use React SDK with TypeScript support
                </p>
                <ul class="feature-list">
                    <li>React hooks</li>
                    <li>TypeScript first</li>
                    <li>Customizable UI</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <h3 class="feature-title">Customizable UI</h3>
                <p class="feature-description">
                    Fully customizable themes and branding options
                </p>
                <ul class="feature-list">
                    <li>Dark/light themes</li>
                    <li>Custom colors</li>
                    <li>Brand logos</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔒</div>
                <h3 class="feature-title">Enterprise Security</h3>
                <p class="feature-description">
                    Bank-grade security with encryption and compliance
                </p>
                <ul class="feature-list">
                    <li>AES-256 encryption</li>
                    <li>API key auth</li>
                    <li>Rate limiting</li>
                </ul>
            </div>
        </div>
        
        <div class="status">
            ✅ <strong>Status:</strong> Your Tokai demo is ready! The React SDK includes social authentication, embedded wallets, and seamless onboarding features.
        </div>
    </div>
    
    <script>
        let isConnected = false;
        
        function simulateConnect() {
            const button = document.querySelector('.connect-button');
            const statusDiv = document.getElementById('connection-status');
            
            if (isConnected) {
                // Disconnect
                isConnected = false;
                button.innerHTML = '🔗 Connect Wallet';
                statusDiv.innerHTML = '';
                return;
            }
            
            // Show loading state
            button.innerHTML = '<div class="spinner"></div> Connecting...';
            button.disabled = true;
            
            // Simulate connection process
            setTimeout(() => {
                isConnected = true;
                button.innerHTML = '👤 <EMAIL> ▼';
                button.disabled = false;
                
                statusDiv.innerHTML = `
                    <div style="background: #1a2e1a; border: 1px solid #16a34a; border-radius: 8px; padding: 16px; color: #16a34a;">
                        <strong>✅ Connected Successfully!</strong><br>
                        <div style="margin-top: 8px; font-size: 14px;">
                            <div>👤 User: <EMAIL></div>
                            <div>🔐 Embedded Wallet: 0x742d...35Cc</div>
                            <div>🌐 Network: Ethereum</div>
                            <div>💎 Account Type: Smart Account</div>
                        </div>
                    </div>
                `;
            }, 2000);
        }
    </script>
</body>
</html>