version: '3.8'

services:
  # Tokai Development Setup
  tokai-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: tokai-dev
    restart: unless-stopped
    ports:
      - "3001:3001"
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_URL=file:/app/data/wallet_service.db
      - JWT_SECRET=dev-jwt-secret-change-in-production
      - WALLET_ENCRYPTION_KEY=dev-wallet-encryption-key-change-in-production
      - BASE_URL=http://localhost:3001
      - FRONTEND_URL=http://localhost:3000
      
      # OAuth (development - use your own keys)
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - DISCORD_CLIENT_ID=${DISCORD_CLIENT_ID}
      - DISCORD_CLIENT_SECRET=${DISCORD_CLIENT_SECRET}
      
      # SMS (mock by default for development)
      - SMS_PROVIDER=mock
      
      # Auto wallet creation (disabled by default)
      - AUTO_CREATE_WALLET=false
      - DEFAULT_WALLET_NETWORK=ethereum
    volumes:
      - ./:/app
      - /app/node_modules
      - tokai-dev-data:/app/data
      - tokai-dev-logs:/app/logs
    networks:
      - tokai-dev-network
    command: ["sh", "-c", "cd dashboard/backend && bun run dev"]

  # Redis for development (optional)
  redis-dev:
    image: redis:7-alpine
    container_name: tokai-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - tokai-dev-network
    command: redis-server --requirepass devpassword

volumes:
  tokai-dev-data:
    driver: local
  tokai-dev-logs:
    driver: local

networks:
  tokai-dev-network:
    driver: bridge