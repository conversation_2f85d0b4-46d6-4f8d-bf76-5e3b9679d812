# Tokai Web

This directory contains the Tokai website and documentation.

## Structure

```
web/
├── website/     # Main Tokai website (Next.js)
├── docs/        # Documentation site (Docusaurus)
├── package.json # Workspace configuration
└── README.md    # This file
```

## Quick Start

### Install Dependencies
```bash
pnpm install
```

### Development

#### Start Website (Next.js)
```bash
pnpm dev:website
# or
cd website && pnpm dev
```

#### Start Documentation (Docusaurus)
```bash
pnpm dev:docs
# or
cd docs && pnpm start
```

### Build

#### Build Both
```bash
pnpm build
```

#### Build Individual
```bash
pnpm build:website  # Build website only
pnpm build:docs     # Build docs only
```

### Linting

#### Lint Both
```bash
pnpm lint
```

#### Lint Individual
```bash
pnpm lint:website  # Lint website only
pnpm lint:docs     # Lint docs only
```

## Website

The main Tokai website built with Next.js 15, TypeScript, and Tailwind CSS.

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS
- **Language**: TypeScript
- **Port**: 3000 (default)

### Features
- Landing page
- Product overview
- Pricing
- Contact
- Blog (planned)

## Documentation

The Tokai documentation site built with Docusaurus.

- **Framework**: Docusaurus 3
- **Styling**: Custom CSS + Tailwind
- **Language**: TypeScript + MDX
- **Port**: 3001 (default)

### Features
- Getting started guides
- API documentation
- Tutorials
- Troubleshooting
- Developer guides

## Deployment

### Website
- **Production**: Vercel (recommended)
- **Staging**: Vercel Preview
- **Domain**: `tokai.com`

### Documentation
- **Production**: GitHub Pages
- **Staging**: GitHub Pages (branch)
- **Domain**: `docs.tokai.com`

## Development Workflow

1. **Feature Development**: Work in `website/` for main site features
2. **Documentation**: Work in `docs/` for documentation updates
3. **Shared Components**: Consider creating a shared component library
4. **Styling**: Use Tailwind CSS for consistency across both sites

## Contributing

1. Create a feature branch
2. Make changes in the appropriate directory
3. Test both sites locally
4. Submit a pull request

## Scripts Reference

| Script | Description |
|--------|-------------|
| `pnpm dev:website` | Start website development server |
| `pnpm dev:docs` | Start docs development server |
| `pnpm build` | Build both sites |
| `pnpm build:website` | Build website only |
| `pnpm build:docs` | Build docs only |
| `pnpm lint` | Lint both projects |
| `pnpm lint:website` | Lint website only |
| `pnpm lint:docs` | Lint docs only |
