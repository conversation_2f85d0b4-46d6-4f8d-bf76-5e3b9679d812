---
sidebar_position: 4
---

# Delete webhook

### Endpoint

```http
DELETE /webhooks/{webhook_id}
```

### Request

Path params: `webhook_id`

### Response

```typescript
interface DeleteWebhookResponse {
  success: true;
  data: {
    id: string;
    deleted: boolean;
  };
  meta: Meta;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "wh_123",
    "deleted": true
  },
  "meta": {
    "request_id": "req_01WHDEL",
    "timestamp": "2025-08-10T12:12:00Z"
  }
}
```
