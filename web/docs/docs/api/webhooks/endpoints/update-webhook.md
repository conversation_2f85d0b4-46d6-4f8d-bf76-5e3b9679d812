---
sidebar_position: 3
---

# Update webhook

### Endpoint

```http
PATCH /webhooks/{webhook_id}
```

### Request

```typescript
interface UpdateWebhookRequest {
  url?: string;
  events?: string[];
  secret?: string;
  active?: boolean;
}
```

Request example

```json
{ "active": false }
```

### Response

```typescript
interface UpdateWebhookResponse {
  success: true;
  data: Webhook;
  meta: Meta;
}
interface Webhook {
  id: string;
  url: string;
  events: string[];
  secret?: string;
  active: boolean;
  created_at: string;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "wh_123",
    "url": "https://app.example.com/webhooks",
    "events": ["wallet.created"],
    "active": false,
    "created_at": "2025-08-10T12:00:00Z"
  },
  "meta": {
    "request_id": "req_01WHUPD",
    "timestamp": "2025-08-10T12:10:00Z"
  }
}
```
