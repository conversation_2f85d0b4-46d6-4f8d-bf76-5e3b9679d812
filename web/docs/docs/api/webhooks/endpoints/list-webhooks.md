---
sidebar_position: 2
---

# List webhooks

### Endpoint

```http
GET /webhooks
```

### Request

```typescript
interface ListWebhooksRequest {
  page?: number;
  page_size?: number;
}
```

Request example

```json
{ "page": 1, "page_size": 20 }
```

### Response

```typescript
interface ListWebhooksResponse {
  success: true;
  data: Webhook[];
  meta: Meta;
}
interface Webhook {
  id: string;
  url: string;
  events: string[];
  secret?: string;
  active: boolean;
  created_at: string;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": [
    {
      "id": "wh_123",
      "url": "https://app.example.com/webhooks",
      "events": ["wallet.created"],
      "active": true,
      "created_at": "2025-08-10T12:00:00Z"
    }
  ],
  "meta": {
    "request_id": "req_01WHLIST",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
