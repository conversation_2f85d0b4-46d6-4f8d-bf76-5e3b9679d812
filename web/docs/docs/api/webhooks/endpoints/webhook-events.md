---
sidebar_position: 5
---

# Webhook events

### Endpoint

```http
GET /webhooks/events
```

### Request

```typescript
interface WebhookEventsRequest {}
```

### Response

```typescript
interface WebhookEventsResponse {
  success: true;
  data: Array<{
    event: string;
    description: string;
    payload_schema: Record<string, unknown>;
  }>;
  meta: Meta;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": [
    {
      "event": "wallet.created",
      "description": "A new wallet was created",
      "payload_schema": {
        "id": "string",
        "address": "string"
      }
    }
  ],
  "meta": {
    "request_id": "req_01WHEVT",
    "timestamp": "2025-08-10T12:15:00Z"
  }
}
```
