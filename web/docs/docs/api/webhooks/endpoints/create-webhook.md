---
sidebar_position: 1
---

# Create webhook

### Endpoint

```http
POST /webhooks
```

### Request

```typescript
interface CreateWebhookRequest {
  url: string;
  events: string[];
  secret?: string;
  active?: boolean;
}
```

Request example

```json
{
  "url": "https://app.example.com/webhooks",
  "events": ["wallet.created", "transaction.sent"],
  "active": true
}
```

### Response

```typescript
interface CreateWebhookResponse {
  success: true;
  data: Webhook;
  meta: Meta;
}
interface Webhook {
  id: string;
  url: string;
  events: string[];
  secret?: string;
  active: boolean;
  created_at: string;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "wh_123",
    "url": "https://app.example.com/webhooks",
    "events": ["wallet.created", "transaction.sent"],
    "active": true,
    "created_at": "2025-08-10T12:00:00Z"
  },
  "meta": {
    "request_id": "req_01WHCREATE",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
