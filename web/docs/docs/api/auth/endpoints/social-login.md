---
sidebar_position: 1
---

# Social login

Provider-agnostic OAuth flow.

## Start social login

### Endpoint

```http
POST /auth/social/{provider}/init
```

### Request

```typescript
interface StartSocialLoginRequest {
  redirect_uri: string;
  state?: string;
  scopes?: string[];
}
```

Request example

```json
{
  "redirect_uri": "https://app.example.com/auth/callback",
  "state": "e6d0b1f7-1ac7-45e8-9f0c-0a4c3b8d2d12",
  "scopes": ["openid", "email", "profile"]
}
```

### Response

```typescript
interface StartSocialLoginResponse {
  success: true;
  data: {
    authorization_url: string;
    state: string;
  };
  meta: Meta;
}

interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "authorization_url": "https://accounts.google.com/o/oauth2/v2/auth?...",
    "state": "e6d0b1f7-1ac7-45e8-9f0c-0a4c3b8d2d12"
  },
  "meta": {
    "request_id": "req_01HXYZ",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```

## Complete social login

### Endpoint

```http
POST /auth/social/{provider}/callback
```

### Request

```typescript
interface CompleteSocialLoginRequest {
  code: string;
  state: string;
  code_verifier?: string;
}
```

Request example

```json
{
  "code": "4/0AfJohXn...",
  "state": "e6d0b1f7-1ac7-45e8-9f0c-0a4c3b8d2d12",
  "code_verifier": "dBjftJeZ4CVP-mB9..."
}
```

### Response

```typescript
interface CompleteSocialLoginResponse {
  success: true;
  data: {
    user: User;
    tokens: Tokens;
    session: { id: string; expires_at: string };
    is_new_user: boolean;
  };
  meta: Meta;
}

interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
}

interface Tokens {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: "Bearer";
}
```

Response example

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "usr_123",
      "email": "<EMAIL>",
      "name": "Jane Doe"
    },
    "tokens": {
      "access_token": "eyJhbGciOi...",
      "refresh_token": "eyJhbGciOi...",
      "expires_in": 900,
      "token_type": "Bearer"
    },
    "session": {
      "id": "sess_123",
      "expires_at": "2025-08-17T12:00:00Z"
    },
    "is_new_user": false
  },
  "meta": {
    "request_id": "req_01HABC",
    "timestamp": "2025-08-10T12:00:05Z"
  }
}
```
