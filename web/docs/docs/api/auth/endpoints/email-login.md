---
sidebar_position: 2
---

# Email login

## Register

### Endpoint

```http
POST /auth/email/register
```

### Request

```typescript
interface EmailRegisterRequest {
  email: string;
  password: string;
  name?: string;
}
interface EmailRegisterResponse {
  success: true;
  data: Auth;
  meta: Meta;
}
```

Request example

```json
{
  "email": "<EMAIL>",
  "password": "Str0ngP@ss!",
  "name": "<PERSON>"
}
```

### Response

```typescript
interface Auth {
  user: User;
  tokens: Tokens;
}

interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
}
interface Tokens {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: "Bearer";
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "usr_123",
      "email": "<EMAIL>",
      "name": "<PERSON>"
    },
    "tokens": {
      "access_token": "eyJhbGciOi...",
      "refresh_token": "eyJhbGciOi...",
      "expires_in": 900,
      "token_type": "Bearer"
    }
  },
  "meta": {
    "request_id": "req_01REG",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```

## Login

### Endpoint

```http
POST /auth/email/login
```

### Request

```typescript
interface EmailLoginRequest {
  email: string;
  password: string;
}
interface EmailLoginResponse {
  success: true;
  data: Auth;
  meta: Meta;
}
```

Request example

```json
{
  "email": "<EMAIL>",
  "password": "Str0ngP@ss!"
}
```

### Response

```typescript
interface Auth {
  user: User;
  tokens: Tokens;
}

interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
}
interface Tokens {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: "Bearer";
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "usr_123",
      "email": "<EMAIL>",
      "name": "Jane Doe"
    },
    "tokens": {
      "access_token": "eyJhbGciOi...",
      "refresh_token": "eyJhbGciOi...",
      "expires_in": 900,
      "token_type": "Bearer"
    }
  },
  "meta": {
    "request_id": "req_01LOGIN",
    "timestamp": "2025-08-10T12:01:00Z"
  }
}
```
