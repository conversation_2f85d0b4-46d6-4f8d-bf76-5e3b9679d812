---
sidebar_position: 4
---

# MFA

## Setup MFA

### Endpoint

```http
POST /auth/mfa/setup
```

### Request

```typescript
interface MfaSetupRequest {
  method: "totp" | "sms" | "email";
}
interface MfaSetupResponse {
  success: true;
  data: {
    secret?: string;
    qr_code?: string;
  };
  meta: Meta;
}
```

Request example

```json
{ "method": "totp" }
```

### Response

```typescript
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "secret": "JBSWY3DPEHPK3PXP",
    "qr_code": "data:image/png;base64,iVBORw0K..."
  },
  "meta": {
    "request_id": "req_01MFASET",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```

## Verify MFA

### Endpoint

```http
POST /auth/mfa/verify
```

### Request

```typescript
interface MfaVerifyRequest {
  code: string;
}
interface MfaVerifyResponse {
  success: true;
  data: {
    verified: boolean;
  };
  meta: Meta;
}
```

Request example

```json
{ "code": "123456" }
```

### Response

```typescript
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "verified": true
  },
  "meta": {
    "request_id": "req_01MFAVER",
    "timestamp": "2025-08-10T12:00:10Z"
  }
}
```
