---
sidebar_position: 3
---

# Magic link

## Send magic link

### Endpoint

```http
POST /auth/magic-link/send
```

### Request

```typescript
interface SendMagicLinkRequest {
  email: string;
  redirect_uri: string;
}
interface SendMagicLinkResponse {
  success: true;
  data: { sent: boolean };
  meta: Meta;
}
```

Request example

```json
{
  "email": "<EMAIL>",
  "redirect_uri": "https://app.example.com/auth/callback"
}
```

### Response

```typescript
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "sent": true
  },
  "meta": {
    "request_id": "req_01MLSEND",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```

## Verify magic link

### Endpoint

```http
POST /auth/magic-link/verify
```

### Request

```typescript
interface VerifyMagicLinkRequest {
  token: string;
  state?: string;
}
interface VerifyMagicLinkResponse {
  success: true;
  data: Auth;
  meta: Meta;
}

interface Auth {
  user: User;
  tokens: Tokens;
}
interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
}
interface Tokens {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: "Bearer";
}
```

Request example

```json
{
  "token": "ml_abcdefg",
  "state": "e6d0b1f7-1ac7-45e8-9f0c-0a4c3b8d2d12"
}
```

### Response

```typescript
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "usr_123",
      "email": "<EMAIL>"
    },
    "tokens": {
      "access_token": "eyJhbGciOi...",
      "refresh_token": "eyJhbGciOi...",
      "expires_in": 900,
      "token_type": "Bearer"
    }
  },
  "meta": {
    "request_id": "req_01MLVER",
    "timestamp": "2025-08-10T12:00:06Z"
  }
}
```
