---
sidebar_position: 5
---

# Sessions

## Refresh token

### Endpoint

```http
POST /auth/refresh
```

### Request

```typescript
interface RefreshTokenRequest {
  refresh_token: string;
}
interface RefreshTokenResponse {
  success: true;
  data: Tokens;
  meta: Meta;
}
```

Request example

```json
{
  "refresh_token": "eyJhbGciOi..."
}
```

### Response

```typescript
interface Tokens {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: "Bearer";
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOi...",
    "refresh_token": "eyJhbGciOi...",
    "expires_in": 900,
    "token_type": "Bearer"
  },
  "meta": {
    "request_id": "req_01REFRESH",
    "timestamp": "2025-08-10T12:02:00Z"
  }
}
```

## Logout

### Endpoint

```http
POST /auth/logout
```

### Request

```typescript
interface LogoutResponse {
  success: true;
  data: {
    logged_out: boolean;
  };
  meta: Meta;
}
```

Request example

```json
{}
```

### Response

```typescript
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "logged_out": true
  },
  "meta": {
    "request_id": "req_01LOGOUT",
    "timestamp": "2025-08-10T12:02:05Z"
  }
}
```
