---
sidebar_position: 3
---

# Get wallet

### Endpoint

```http
GET /wallets/{wallet_id}
```

### Request

Path params: `wallet_id`

### Response

```typescript
interface GetWalletResponse {
  success: true;
  data: Wallet;
  meta: Meta;
}
interface Wallet {
  id: string;
  address: string;
  chain: string;
  type: "embedded" | "external";
  created_at: string;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "wal_123",
    "address": "0xabc...",
    "chain": "ethereum",
    "type": "embedded",
    "created_at": "2025-08-10T12:00:00Z"
  },
  "meta": {
    "request_id": "req_01WGET",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
