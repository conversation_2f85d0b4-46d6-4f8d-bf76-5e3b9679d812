---
sidebar_position: 2
---

# List wallets

### Endpoint

```http
GET /wallets
```

### Request

```typescript
interface ListWalletsRequest {
  page?: number;
  page_size?: number;
  chain?: string;
}
```

Request example

```json
{
  "page": 1,
  "page_size": 20,
  "chain": "ethereum"
}
```

### Response

```typescript
interface ListWalletsResponse {
  success: true;
  data: {
    items: Wallet[];
    page: number;
    page_size: number;
    total: number;
  };
  meta: Meta;
}
interface Wallet {
  id: string;
  address: string;
  chain: string;
  type: "embedded" | "external";
  created_at: string;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "wal_123",
        "address": "0xabc...",
        "chain": "ethereum",
        "type": "embedded",
        "created_at": "2025-08-10T12:00:00Z"
      }
    ],
    "page": 1,
    "page_size": 20,
    "total": 1
  },
  "meta": {
    "request_id": "req_01WLIST",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
