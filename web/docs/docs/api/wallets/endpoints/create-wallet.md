---
sidebar_position: 1
---

# Create wallet

### Endpoint

```http
POST /wallets
```

### Request

```typescript
interface CreateWalletRequest {
  chain: string;
  type: "embedded" | "external";
}
```

Request example

```json
{
  "chain": "ethereum",
  "type": "embedded"
}
```

### Response

```typescript
interface CreateWalletResponse {
  success: true;
  data: Wallet;
  meta: Meta;
}

interface Wallet {
  id: string;
  address: string;
  chain: string;
  type: "embedded" | "external";
  created_at: string;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "wal_123",
    "address": "0xabc...",
    "chain": "ethereum",
    "type": "embedded",
    "created_at": "2025-08-10T12:00:00Z"
  },
  "meta": {
    "request_id": "req_01WCREATE",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
