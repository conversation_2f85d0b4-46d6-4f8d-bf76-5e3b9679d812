---
sidebar_position: 4
---

# Update wallet

### Endpoint

```http
PATCH /wallets/{wallet_id}
```

### Request

```typescript
interface UpdateWalletRequest {
  label?: string;
  metadata?: Record<string, unknown>;
}
```

Request example

```json
{ "label": "Primary wallet" }
```

### Response

```typescript
interface UpdateWalletResponse {
  success: true;
  data: Wallet;
  meta: Meta;
}
interface Wallet {
  id: string;
  address: string;
  chain: string;
  type: "embedded" | "external";
  created_at: string;
  label?: string;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "wal_123",
    "address": "0xabc...",
    "chain": "ethereum",
    "type": "embedded",
    "created_at": "2025-08-10T12:00:00Z",
    "label": "Primary wallet"
  },
  "meta": {
    "request_id": "req_01WUPD",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
