---
sidebar_position: 5
---

# Delete wallet

### Endpoint

```http
DELETE /wallets/{wallet_id}
```

### Request

Path params: `wallet_id`

### Response

```typescript
interface DeleteWalletResponse {
  success: true;
  data: {
    id: string;
    deleted: boolean;
  };
  meta: Meta;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "wal_123",
    "deleted": true
  },
  "meta": {
    "request_id": "req_01WDEL",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
