---
sidebar_position: 6
---

# Create smart account

### Endpoint

```http
POST /wallets/{wallet_id}/smart-account
```

### Request

```typescript
interface CreateSmartAccountRequest {
  chain: string;
  factory?: string;
  salt?: string;
}
```

Request example

```json
{ "chain": "ethereum" }
```

### Response

```typescript
interface CreateSmartAccountResponse {
  success: true;
  data: SmartAccount;
  meta: Meta;
}

interface SmartAccount {
  id: string;
  address: string;
  owner_wallet_id: string;
  chain: string;
  created_at: string;
}

interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "sacc_123",
    "address": "0xdef...",
    "owner_wallet_id": "wal_123",
    "chain": "ethereum",
    "created_at": "2025-08-10T12:00:00Z"
  },
  "meta": {
    "request_id": "req_01SACC",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
