---
sidebar_position: 2
---

# Transaction analytics

### Endpoint

```http
GET /analytics/transactions
```

### Request

```typescript
interface TransactionAnalyticsRequest {
  from?: string;
  to?: string;
  chain?: string;
  group_by?: "day" | "week" | "month";
}
```

Request example

```json
{
  "from": "2025-08-01",
  "to": "2025-08-10",
  "group_by": "day",
  "chain": "ethereum"
}
```

### Response

```typescript
interface TransactionAnalyticsResponse {
  success: true;
  data: Array<{
    period_start: string;
    total_transactions: number;
    success_rate: number;
  }>;
  meta: Meta;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": [
    {
      "period_start": "2025-08-10",
      "total_transactions": 4200,
      "success_rate": 0.98
    }
  ],
  "meta": {
    "request_id": "req_01TA",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
