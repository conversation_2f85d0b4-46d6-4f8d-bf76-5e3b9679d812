---
sidebar_position: 1
---

# User analytics

### Endpoint

```http
GET /analytics/users
```

### Request

```typescript
interface UserAnalyticsRequest {
  from?: string;
  to?: string;
  group_by?: "day" | "week" | "month";
}
```

Request example

```json
{ "from": "2025-08-01", "to": "2025-08-10", "group_by": "day" }
```

### Response

```typescript
interface UserAnalyticsResponse {
  success: true;
  data: Array<{
    period_start: string;
    active_users: number;
    new_users: number;
  }>;
  meta: Meta;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": [
    {
      "period_start": "2025-08-10",
      "active_users": 1200,
      "new_users": 50
    }
  ],
  "meta": {
    "request_id": "req_01UA",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
