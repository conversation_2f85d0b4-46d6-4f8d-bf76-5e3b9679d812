---
sidebar_position: 4
---

# Performance metrics

### Endpoint

```http
GET /analytics/performance
```

### Request

```typescript
interface PerformanceMetricsRequest {
  from?: string;
  to?: string;
  group_by?: "day" | "week" | "month";
}
```

Request example

```json
{
  "from": "2025-08-01",
  "to": "2025-08-10",
  "group_by": "day"
}
```

### Response

```typescript
interface PerformanceMetricsResponse {
  success: true;
  data: Array<{
    period_start: string;
    p50_latency_ms: number;
    p95_latency_ms: number;
    error_rate: number;
  }>;
  meta: Meta;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": [
    {
      "period_start": "2025-08-10",
      "p50_latency_ms": 120,
      "p95_latency_ms": 280,
      "error_rate": 0.01
    }
  ],
  "meta": {
    "request_id": "req_01PM",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
