---
sidebar_position: 3
---

# Wallet analytics

### Endpoint

```http
GET /analytics/wallets
```

### Request

```typescript
interface WalletAnalyticsRequest {
  from?: string;
  to?: string;
  group_by?: "day" | "week" | "month";
}
```

Request example

```json
{ "from": "2025-08-01", "to": "2025-08-10", "group_by": "day" }
```

### Response

```typescript
interface WalletAnalyticsResponse {
  success: true;
  data: Array<{
    period_start: string;
    wallets_created: number;
    active_wallets: number;
  }>;
  meta: Meta;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": [
    {
      "period_start": "2025-08-10",
      "wallets_created": 30,
      "active_wallets": 200
    }
  ],
  "meta": {
    "request_id": "req_01WA",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
