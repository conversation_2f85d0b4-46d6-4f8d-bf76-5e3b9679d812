---
sidebar_position: 2
---

# Get transaction

### Endpoint

```http
GET /transactions/{transaction_id}
```

### Request

Path params: `transaction_id`

### Response

```typescript
interface GetTransactionResponse {
  success: true;
  data: Transaction;
  meta: Meta;
}

interface Transaction {
  id: string;
  chain: string;
  hash: string;
  from: string;
  to?: string;
  value?: string;
  status: "pending" | "confirmed" | "failed";
  created_at: string;
  confirmed_at?: string;
}

interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "tx_123",
    "chain": "ethereum",
    "hash": "0xhash...",
    "from": "0xabc...",
    "to": "0xdef...",
    "value": "0.01",
    "status": "confirmed",
    "created_at": "2025-08-10T12:00:00Z",
    "confirmed_at": "2025-08-10T12:05:00Z"
  },
  "meta": {
    "request_id": "req_01TGET",
    "timestamp": "2025-08-10T12:06:00Z"
  }
}
```
