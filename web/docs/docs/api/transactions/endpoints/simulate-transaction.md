---
sidebar_position: 4
---

# Simulate transaction

### Endpoint

```http
POST /transactions/simulate
```

### Request

```typescript
interface SimulateTransactionRequest {
  chain: string;
  from: string;
  to?: string;
  value?: string;
  data?: string;
}
```

Request example

```json
{
  "chain": "ethereum",
  "from": "0xabc...",
  "to": "0xdef...",
  "value": "0.01"
}
```

### Response

```typescript
interface SimulateTransactionResponse {
  success: true;
  data: {
    gas_estimate: string;
    success: boolean;
    error?: string;
  };
  meta: Meta;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "gas_estimate": "21000",
    "success": true
  },
  "meta": {
    "request_id": "req_01TSIM",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
