---
sidebar_position: 5
---

# Batch transactions

### Endpoint

```http
POST /transactions/batch
```

### Request

```typescript
interface BatchTransactionsRequest {
  chain: string;
  wallet_id: string;
  transactions: Array<{
    to: string;
    value?: string;
    data?: string;
  }>;
}
```

Request example

```json
{
  "chain": "ethereum",
  "wallet_id": "wal_123",
  "transactions": [
    {
      "to": "0xdef...",
      "value": "0.01"
    },
    {
      "to": "0x123...",
      "data": "0xabcdef"
    }
  ]
}
```

### Response

```typescript
interface BatchTransactionsResponse {
  success: true;
  data: Array<{
    transaction_id: string;
    hash: string;
    status: "pending" | "confirmed" | "failed";
  }>;
  meta: Meta;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": [
    {
      "transaction_id": "tx_1",
      "hash": "0xhash1...",
      "status": "pending"
    },
    {
      "transaction_id": "tx_2",
      "hash": "0xhash2...",
      "status": "pending"
    }
  ],
  "meta": {
    "request_id": "req_01TBATCH",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
