---
sidebar_position: 1
---

# Send transaction

### Endpoint

```http
POST /transactions
```

### Request

```typescript
interface SendTransactionRequest {
  wallet_id: string;
  chain: string;
  to: string;
  value?: string;
  data?: string;
}
```

Request example

```json
{
  "wallet_id": "wal_123",
  "chain": "ethereum",
  "to": "0xdef...",
  "value": "0.01"
}
```

### Response

```typescript
interface SendTransactionResponse {
  success: true;
  data: {
    transaction_id: string;
    chain: string;
    hash: string;
    status: "pending" | "confirmed" | "failed";
    created_at: string;
  };
  meta: Meta;
}

interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "transaction_id": "tx_123",
    "chain": "ethereum",
    "hash": "0xhash...",
    "status": "pending",
    "created_at": "2025-08-10T12:00:00Z"
  },
  "meta": {
    "request_id": "req_01TSEND",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
