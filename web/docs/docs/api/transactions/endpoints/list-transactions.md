---
sidebar_position: 3
---

# List transactions

### Endpoint

```http
GET /transactions
```

### Request

```typescript
interface ListTransactionsRequest {
  wallet_id?: string;
  chain?: string;
  status?: "pending" | "confirmed" | "failed";
  page?: number;
  page_size?: number;
}
```

Request example

```json
{
  "wallet_id": "wal_123",
  "page": 1,
  "page_size": 20
}
```

### Response

```typescript
interface ListTransactionsResponse {
  success: true;
  data: {
    items: Transaction[];
    page: number;
    page_size: number;
    total: number;
  };
  meta: Meta;
}

interface Transaction {
  id: string;
  chain: string;
  hash: string;
  from: string;
  to?: string;
  value?: string;
  status: "pending" | "confirmed" | "failed";
  created_at: string;
  confirmed_at?: string;
}

interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "tx_123",
        "chain": "ethereum",
        "hash": "0xhash...",
        "from": "0xabc...",
        "to": "0xdef...",
        "value": "0.01",
        "status": "confirmed",
        "created_at": "2025-08-10T12:00:00Z"
      }
    ],
    "page": 1,
    "page_size": 20,
    "total": 1
  },
  "meta": {
    "request_id": "req_01TLIST",
    "timestamp": "2025-08-10T12:06:00Z"
  }
}
```
