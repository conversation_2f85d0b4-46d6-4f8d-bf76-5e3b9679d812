---
sidebar_position: 2
---

# List session keys

### Endpoint

```http
GET /session-keys
```

### Request

```typescript
interface ListSessionKeysRequest {
  wallet_id: string;
  page?: number;
  page_size?: number;
}
```

Request example

```json
{
  "wallet_id": "wal_123",
  "page": 1,
  "page_size": 20
}
```

### Response

```typescript
interface ListSessionKeysResponse {
  success: true;
  data: {
    items: SessionKey[];
    page: number;
    page_size: number;
    total: number;
  };
  meta: Meta;
}
interface SessionKey {
  id: string;
  wallet_id: string;
  chain: string;
  public_key: string;
  created_at: string;
  expires_at: string;
  permissions: string[];
  revoked: boolean;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "sk_123",
        "wallet_id": "wal_123",
        "chain": "ethereum",
        "public_key": "0xpub...",
        "created_at": "2025-08-10T12:00:00Z",
        "expires_at": "2025-08-10T13:00:00Z",
        "permissions": ["transfer"],
        "revoked": false
      }
    ],
    "page": 1,
    "page_size": 20,
    "total": 1
  },
  "meta": {
    "request_id": "req_01SKLIST",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
