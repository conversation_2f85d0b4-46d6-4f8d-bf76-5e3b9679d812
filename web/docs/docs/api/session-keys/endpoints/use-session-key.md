---
sidebar_position: 6
---

# Use session key

### Endpoint

```http
POST /session-keys/{session_key_id}/use
```

### Request

```typescript
interface UseSessionKeyRequest {
  action: "signMessage" | "sendTransaction";
  payload: unknown;
}
```

Request example

```json
{
  "action": "signMessage",
  "payload": {
    "message": "hello"
  }
}
```

### Response

```typescript
interface UseSessionKeyResponse {
  success: true;
  data: {
    result: unknown;
  };
  meta: Meta;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "result": "0xsignature..."
  },
  "meta": {
    "request_id": "req_01SKUSE",
    "timestamp": "2025-08-10T12:50:00Z"
  }
}
```
