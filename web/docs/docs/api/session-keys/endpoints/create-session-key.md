---
sidebar_position: 1
---

# Create session key

### Endpoint

```http
POST /session-keys
```

### Request

```typescript
interface CreateSessionKeyRequest {
  wallet_id: string;
  chain: string;
  permissions: string[];
  expires_in_seconds?: number;
}
```

Request example

```json
{
  "wallet_id": "wal_123",
  "chain": "ethereum",
  "permissions": ["transfer", "signMessage"],
  "expires_in_seconds": 3600
}
```

### Response

```typescript
interface CreateSessionKeyResponse {
  success: true;
  data: SessionKey;
  meta: Meta;
}
interface SessionKey {
  id: string;
  wallet_id: string;
  chain: string;
  public_key: string;
  created_at: string;
  expires_at: string;
  permissions: string[];
  revoked: boolean;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "sk_123",
    "wallet_id": "wal_123",
    "chain": "ethereum",
    "public_key": "0xpub...",
    "created_at": "2025-08-10T12:00:00Z",
    "expires_at": "2025-08-10T13:00:00Z",
    "permissions": ["transfer", "signMessage"],
    "revoked": false
  },
  "meta": {
    "request_id": "req_01SKCREATE",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
