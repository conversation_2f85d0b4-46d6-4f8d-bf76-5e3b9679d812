---
sidebar_position: 3
---

# Get session key

### Endpoint

```http
GET /session-keys/{session_key_id}
```

### Request

Path params: `session_key_id`

### Response

```typescript
interface GetSessionKeyResponse {
  success: true;
  data: SessionKey;
  meta: Meta;
}
interface SessionKey {
  id: string;
  wallet_id: string;
  chain: string;
  public_key: string;
  created_at: string;
  expires_at: string;
  permissions: string[];
  revoked: boolean;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "sk_123",
    "wallet_id": "wal_123",
    "chain": "ethereum",
    "public_key": "0xpub...",
    "created_at": "2025-08-10T12:00:00Z",
    "expires_at": "2025-08-10T13:00:00Z",
    "permissions": ["transfer"],
    "revoked": false
  },
  "meta": {
    "request_id": "req_01SKGET",
    "timestamp": "2025-08-10T12:00:00Z"
  }
}
```
