---
sidebar_position: 4
---

# Update session key

### Endpoint

```http
PATCH /session-keys/{session_key_id}
```

### Request

```typescript
interface UpdateSessionKeyRequest {
  permissions?: string[];
  expires_at?: string;
}
```

Request example

```json
{
  "permissions": ["transfer", "signMessage"],
  "expires_at": "2025-08-10T14:00:00Z"
}
```

### Response

```typescript
interface UpdateSessionKeyResponse {
  success: true;
  data: SessionKey;
  meta: Meta;
}
interface SessionKey {
  id: string;
  wallet_id: string;
  chain: string;
  public_key: string;
  created_at: string;
  expires_at: string;
  permissions: string[];
  revoked: boolean;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "sk_123",
    "wallet_id": "wal_123",
    "chain": "ethereum",
    "public_key": "0xpub...",
    "created_at": "2025-08-10T12:00:00Z",
    "expires_at": "2025-08-10T14:00:00Z",
    "permissions": ["transfer", "signMessage"],
    "revoked": false
  },
  "meta": {
    "request_id": "req_01SKUPD",
    "timestamp": "2025-08-10T12:30:00Z"
  }
}
```
