---
sidebar_position: 5
---

# Revoke session key

### Endpoint

```http
POST /session-keys/{session_key_id}/revoke
```

### Request

Path params: `session_key_id`

### Response

```typescript
interface RevokeSessionKeyResponse {
  success: true;
  data: {
    id: string;
    revoked: boolean;
  };
  meta: Meta;
}
interface Meta {
  request_id: string;
  timestamp: string;
}
```

Response example

```json
{
  "success": true,
  "data": {
    "id": "sk_123",
    "revoked": true
  },
  "meta": {
    "request_id": "req_01SKREV",
    "timestamp": "2025-08-10T12:45:00Z"
  }
}
```
