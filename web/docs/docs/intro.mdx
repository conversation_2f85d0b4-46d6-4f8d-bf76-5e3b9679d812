---
sidebar_position: 1
---

import { FeatureCard } from "@site/src/components/FeatureCard";
import { ChainGrid } from "@site/src/components/ChainGrid";
import { HeroSection } from "@site/src/components/HeroSection";
import { CodeExample } from "@site/src/components/CodeExample";

<HeroSection
  title="Tokai"
  subtitle="Build with Tokai. Onboard users, provision wallets, and ship transactions with one straightforward API."
/>

## Core Features

<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem', marginBottom: '3rem' }}>
  <FeatureCard
    title="Authentication"
    icon="🔐"
    description="Flexible auth flows that work for your users"
    items={[
      "Email + password login",
      "Magic link authentication",
      "Social login providers",
      "Multi-factor auth (MFA)",
    ]}
    link="./api/auth/overview"
  />

  <FeatureCard
    title="Wallets"
    icon="👜"
    description="One API for all wallet operations"
    items={[
      "Embedded wallets",
      "External wallet connectors",
      "Smart account support",
      "Multi-chain compatibility",
    ]}
    link="./api/wallets/overview"
  />

  <FeatureCard
    title="Transactions"
    icon="⚡"
    description="Send, simulate, and monitor transactions"
    items={[
      "Transaction sending",
      "Batch operations",
      "Gas estimation",
      "Transaction simulation",
    ]}
    link="./api/transactions/overview"
  />
</div>

## Supported Chains

<ChainGrid
  chains={[
    { name: "Ethereum" },
    { name: "Solana" },
    { name: "Bitcoin" },
    { name: "Cardano" },
    { name: "Polygon" },
    { name: "BSC" },
    { name: "Avalanche" },
    { name: "Arbitrum" },
    { name: "Optimism" },
    { name: "Algorand" },
    { name: "Cosmos" },
    { name: "Near" },
    { name: "Polkadot" },
    { name: "Tron" },
  ]}
/>

## Developer Tools

<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem', marginBottom: '3rem' }}>
  <FeatureCard
    title="Security"
    icon="🔒"
    description="Enterprise-grade security features"
    items={[
      "Session key management",
      "Scoped API access",
      "Audit logging",
      "Rate limiting",
    ]}
    link="./api/session-keys/overview"
  />

  <FeatureCard
    title="Integration"
    icon="🔌"
    description="Multiple integration options"
    items={["REST API", "React SDK", "Mobile SDKs", "Webhook support"]}
    link="./api/webhooks/overview"
  />

  <FeatureCard
    title="Analytics"
    icon="📊"
    description="Understand your app usage"
    items={[
      "User activity tracking",
      "Wallet usage metrics",
      "Performance monitoring",
      "Custom insights",
    ]}
    link="./api/analytics/overview"
  />
</div>

## Where to go next

- **Browse endpoints** → [API Overview](./api/)
- **Start with Auth** → [Social login](./api/auth/endpoints/social-login)
- **Build a flow** → [Quickstart](./tutorials/quickstart)

## Featured guides

- Spin up a wallet and send a tx in minutes → [Quickstart](./tutorials/quickstart)
- Add passwordless auth → [Magic link](./api/auth/endpoints/magic-link)
- Connect existing wallets, then provision embedded → [Social login](./api/auth/endpoints/social-login)

## Environments

- Production: `https://api.tokai.com/v1`
- Staging: `https://staging-api.tokai.com/v1`
- Development: `https://dev-api.tokai.com/v1`
