import Link from "@docusaurus/Link";
import useDocusaurusContext from "@docusaurus/useDocusaurusContext";
import { ChainGrid } from "@site/src/components/ChainGrid";
import { FeatureCard } from "@site/src/components/FeatureCard";
import { HeroSection } from "@site/src/components/HeroSection";
import Layout from "@theme/Layout";

import styles from "./index.module.css";

export default function Home() {
  const { siteConfig } = useDocusaurusContext();
  return (
    <Layout
      title={`${siteConfig.title}`}
      description="Multi-chain wallet infrastructure for developers. Build onboarding and multi-chain flows fast with one straightforward API."
    >
      <main>
        <HeroSection
          title="Build with Tokai"
          subtitle="Multi-chain wallet infrastructure that makes onboarding feel like magic and transactions feel boring (in a good way)."
        >
          <div className={styles.heroButtons}>
            <Link
              className="button button--primary button--lg"
              style={{
                background: "linear-gradient(135deg, #3b82f6, #8b5cf6)",
              }}
              to="/docs/api/"
            >
              Get Started  🚀
            </Link>
          </div>
        </HeroSection>

        <section className={styles.chains}>
          <h2 className={styles.sectionTitle}>Supported Chains</h2>
          <ChainGrid
            chains={[
              { name: "Ethereum" },
              { name: "Solana" },
              { name: "Bitcoin" },
              { name: "Cardano" },
              { name: "Polygon" },
              { name: "BSC" },
              { name: "Avalanche" },
              { name: "Arbitrum" },
              { name: "Optimism" },
              { name: "Algorand" },
              { name: "Cosmos" },
              { name: "Near" },
              { name: "Polkadot" },
              { name: "Tron" },
            ]}
          />
        </section>

        <div className="container">
          <section className={styles.features}>
            <h2 className={styles.sectionTitle}>
              Everything you need to build
            </h2>
            <div className={styles.featureGrid}>
              <FeatureCard
                title="Authentication"
                icon="🔐"
                description="Flexible auth flows that work for your users"
                items={[
                  "Email + password login",
                  "Magic link authentication",
                  "Social login providers",
                  "Multi-factor auth (MFA)",
                ]}
                link="/docs/api/auth/overview"
              />

              <FeatureCard
                title="Wallets"
                icon="👜"
                description="One API for all wallet operations"
                items={[
                  "Embedded wallets",
                  "External wallet connectors",
                  "Smart account support",
                  "Multi-chain compatibility",
                ]}
                link="/docs/api/wallets/overview"
              />

              <FeatureCard
                title="Transactions"
                icon="⚡"
                description="Send, simulate, and monitor transactions"
                items={[
                  "Transaction sending",
                  "Batch operations",
                  "Gas estimation",
                  "Transaction simulation",
                ]}
                link="/docs/api/transactions/overview"
              />
            </div>
          </section>

          {/* <section className={styles.quickStart}>
            <h2 className={styles.sectionTitle}>Get started in minutes</h2>
            <div className={styles.quickStartButtons}>
              <Link
                className="button button--primary button--lg"
                to="/docs/tutorials/quickstart"
              >
                Follow the Tutorial 🚀
              </Link>
              <Link
                className="button button--outline button--lg"
                to="/docs/api/"
              >
                Explore the API 📖
              </Link>
            </div>
          </section> */}

          <section className={styles.tools}>
            <h2 className={styles.sectionTitle}>Developer Tools</h2>
            <div className={styles.featureGrid}>
              <FeatureCard
                title="Security"
                icon="🔒"
                description="Enterprise-grade security features"
                items={[
                  "Session key management",
                  "Scoped API access",
                  "Audit logging",
                  "Rate limiting",
                ]}
                link="/docs/api/session-keys/overview"
              />

              <FeatureCard
                title="Integration"
                icon="🔌"
                description="Multiple integration options"
                items={[
                  "REST API",
                  "React SDK",
                  "Mobile SDKs",
                  "Webhook support",
                ]}
                link="/docs/api/webhooks/overview"
              />

              <FeatureCard
                title="Analytics"
                icon="📊"
                description="Understand your app usage"
                items={[
                  "User activity tracking",
                  "Wallet usage metrics",
                  "Performance monitoring",
                  "Custom insights",
                ]}
                link="/docs/api/analytics/overview"
              />
            </div>
          </section>
        </div>
      </main>
    </Layout>
  );
}
