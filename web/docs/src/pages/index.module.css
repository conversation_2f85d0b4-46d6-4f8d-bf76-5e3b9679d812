body {
  width: 100dvw;
  height: 100dvh;
  overflow: hidden;
}

.heroBanner {
  padding: 4rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.heroButtons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.features {
  padding: 4rem 0;
  text-align: center;
}

.sectionTitle {
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  margin-bottom: 3rem;
  letter-spacing: -0.025em;
}

.featureGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.chains {
  padding: 4rem 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%);
  margin: 2rem -2rem;
  padding: 4rem 2rem;
}

.quickStart {
  padding: 4rem 0;
  text-align: center;
}

.codeSection {
  max-width: 800px;
  margin: 0 auto 3rem auto;
}

.quickStartButtons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.tools {
  padding: 4rem 0;
  text-align: center;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.02) 0%, rgba(59, 130, 246, 0.02) 100%);
  margin: 2rem -2rem;
  padding: 4rem 2rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .heroButtons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .buttons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .quickStartButtons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .featureGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .chains,
  .tools {
    margin: 1rem -1rem;
    padding: 3rem 1rem;
  }
}

/* Animation for sections */
.features,
.chains,
.quickStart,
.tools {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button styling overrides */
.button--primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.button--outline {
  background: transparent;
  border: 2px solid #3b82f6;
  color: #fff !important;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.button--outline:hover {
  background: #3b82f6;
  color: #fff !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}
