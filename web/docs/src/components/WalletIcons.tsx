import React from 'react';
import {
  WalletMetamask,
  WalletLedger,
  WalletSafe,
  WalletCoinbase,
  WalletPhantom,
  WalletBackpack,
  WalletSolflare,
  WalletBrave,
  WalletTrust,
  WalletRainbow,
  WalletArgent,
  WalletImToken,
} from '@web3icons/react';

interface WalletIconProps {
  name: string;
  size?: number;
  className?: string;
}

export function WalletIcon({ name, size = 32, className = '' }: WalletIconProps) {
  const icons: Record<string, React.ReactNode> = {
    metamask: <WalletMetamask size={size} />,
    ledger: <WalletLedger size={size} />,
    safe: <WalletSafe size={size} />,
    coinbase: <WalletCoinbase size={size} />,
    phantom: <WalletPhantom size={size} />,
    backpack: <WalletBackpack size={size} />,
    solflare: <WalletSolflare size={size} />,
    brave: <WalletBrave size={size} />,
    trust: <WalletTrust size={size} />,
    rainbow: <WalletRainbow size={size} />,
    argent: <WalletArgent size={size} />,
    imtoken: <WalletImToken size={size} />,
  };

  const icon = icons[name.toLowerCase()];
  
  if (!icon) {
    return (
      <div 
        style={{ 
          width: size, 
          height: size, 
          backgroundColor: '#6B7280',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: size * 0.4,
          fontWeight: 'bold'
        }}
        className={className}
      >
        {name.charAt(0).toUpperCase()}
      </div>
    );
  }

  return <div className={className}>{icon}</div>;
}
