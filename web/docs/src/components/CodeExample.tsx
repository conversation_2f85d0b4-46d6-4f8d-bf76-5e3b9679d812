import React, { useState } from 'react';
import styles from './CodeExample.module.css';

interface CodeExampleProps {
  title: string;
  language: string;
  children: string;
}

export function CodeExample({ title, language, children }: CodeExampleProps) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(children);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <span className={styles.title}>{title}</span>
        <button 
          className={`${styles.copyButton} ${copied ? styles.copied : ''}`}
          onClick={copyToClipboard}
        >
          {copied ? '✓ Copied!' : 'Copy'}
        </button>
      </div>
      <div className={styles.codeContainer}>
        <pre className={styles.code}>
          <code className={`language-${language}`}>{children}</code>
        </pre>
      </div>
    </div>
  );
}
