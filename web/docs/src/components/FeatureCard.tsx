import React from 'react';
import styles from './FeatureCard.module.css';

interface FeatureCardProps {
  title: string;
  icon: string;
  description: string;
  items: string[];
  link?: string;
}

export function FeatureCard({ title, icon, description, items, link }: FeatureCardProps) {
  const content = (
    <div className={styles.card}>
      <div className={styles.header}>
        <span className={styles.icon}>{icon}</span>
        <h3 className={styles.title}>{title}</h3>
      </div>
      <p className={styles.description}>{description}</p>
      <ul className={styles.list}>
        {items.map((item, i) => (
          <li key={i} className={styles.item}>{item}</li>
        ))}
      </ul>
    </div>
  );

  if (link) {
    return (
      <a href={link} className={styles.link}>
        {content}
      </a>
    );
  }

  return content;
}
