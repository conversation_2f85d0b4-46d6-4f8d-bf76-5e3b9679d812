.hero {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin: -2rem -2rem 4rem -2rem;
  padding: 4rem 2rem;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.gradient1 {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

.gradient2 {
  position: absolute;
  top: -30%;
  right: -30%;
  width: 160%;
  height: 160%;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  animation: float 25s ease-in-out infinite reverse;
}

.gradient3 {
  position: absolute;
  bottom: -40%;
  left: 20%;
  width: 140%;
  height: 140%;
  background: radial-gradient(circle, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
  animation: float 30s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

.content {
  text-align: center;
  max-width: 800px;
  z-index: 1;
}

.title {
  font-size: clamp(3rem, 8vw, 5rem);
  font-weight: 900;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 1.5rem 0;
  letter-spacing: -0.02em;
  line-height: 1.1;
  animation: fadeInUp 1s ease-out;
}

.subtitle {
  font-size: clamp(1.2rem, 4vw, 1.5rem);
  color: var(--ifm-color-emphasis-600);
  margin: 0 0 2rem 0;
  line-height: 1.6;
  font-weight: 500;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.children {
  animation: fadeInUp 1s ease-out 0.4s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
