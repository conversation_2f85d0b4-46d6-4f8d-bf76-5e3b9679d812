.card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.link {
  text-decoration: none;
  display: block;
}

.link:hover .card {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(59, 130, 246, 0.2);
}

.link:hover .card::before {
  opacity: 1;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.icon {
  font-size: 2rem;
  margin-right: 1rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.link:hover .icon {
  transform: scale(1.1) rotate(5deg);
}

.title {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.025em;
}

.description {
  color: var(--ifm-color-emphasis-600);
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 1.5rem;
  font-weight: 500;
}

.list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.item {
  color: var(--ifm-color-emphasis-700);
  font-size: 0.95rem;
  line-height: 1.8;
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
  transition: color 0.2s ease;
}

.item::before {
  content: "→";
  color: #3b82f6;
  position: absolute;
  left: 0;
  font-weight: bold;
  transition: transform 0.2s ease;
}

.link:hover .item::before {
  transform: translateX(4px);
}
