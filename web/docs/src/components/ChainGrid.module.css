.container {
  margin: 3rem 0;
}

.title {
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  letter-spacing: -0.025em;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.chain {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1.5rem 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.chain::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chain:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(59, 130, 246, 0.2);
}

.chain:hover::before {
  opacity: 1;
}

.icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.chain:hover .icon {
  transform: scale(1.2) rotate(10deg);
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.3));
}

.name {
  font-weight: 600;
  color: var(--ifm-font-color-base);
  font-size: 1rem;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
}

.chain:hover .name {
  color: #3b82f6;
}

.description {
  font-size: 0.85rem;
  color: var(--ifm-color-emphasis-600);
  margin-top: 0.5rem;
  position: relative;
  z-index: 1;
}
