import React from 'react';
import {
  NetworkEthereum,
  NetworkSolana,
  TokenBTC,
  NetworkCardano,
  NetworkPolygon,
  NetworkBinanceSmartChain,
  NetworkAvalanche,
  NetworkArbitrumOne,
  NetworkOptimism,
  NetworkAlgorand,
  NetworkCosmos,
  NetworkNearProtocol,
  NetworkPolkadot,
  NetworkTron,
} from '@web3icons/react';

interface ChainIconProps {
  name: string;
  size?: number;
  className?: string;
}

export function ChainIcon({ name, size = 32, className = '' }: ChainIconProps) {
  const icons: Record<string, React.ReactNode> = {
    ethereum: <NetworkEthereum size={size} />,
    solana: <NetworkSolana size={size} />,
    bitcoin: <TokenBTC size={size} />,
    cardano: <NetworkCardano size={size} />,
    polygon: <NetworkPolygon size={size} />,
    bsc: <NetworkBinanceSmartChain size={size} />,
    avalanche: <NetworkAvalanche size={size} />,
    arbitrum: <NetworkArbitrumOne size={size} />,
    optimism: <NetworkOptimism size={size} />,
    algorand: <NetworkAlgorand size={size} />,
    cosmos: <NetworkCosmos size={size} />,
    near: <NetworkNearProtocol size={size} />,
    polkadot: <NetworkPolkadot size={size} />,
    tron: <NetworkTron size={size} />,
  };

  const icon = icons[name.toLowerCase()];
  
  if (!icon) {
    return (
      <div 
        style={{ 
          width: size, 
          height: size, 
          backgroundColor: '#6B7280',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: size * 0.4,
          fontWeight: 'bold'
        }}
        className={className}
      >
        {name.charAt(0).toUpperCase()}
      </div>
    );
  }

  return <div className={className}>{icon}</div>;
}
