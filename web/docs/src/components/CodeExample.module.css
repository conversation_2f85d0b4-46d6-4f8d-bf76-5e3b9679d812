.container {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  margin: 2rem 0;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.title {
  color: var(--ifm-font-color-base);
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.copyButton {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border: none;
  border-radius: 8px;
  color: white;
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.copyButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.copyButton.copied {
  background: linear-gradient(135deg, #10b981, #059669);
}

.codeContainer {
  padding: 1.5rem;
  overflow-x: auto;
}

.code {
  margin: 0;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  color: #e5e7eb;
  background: transparent;
}

.code code {
  background: transparent;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
  font-family: inherit;
}

/* Syntax highlighting overrides */
.code .token.keyword {
  color: #f472b6;
}

.code .token.string {
  color: #a7f3d0;
}

.code .token.number {
  color: #fbbf24;
}

.code .token.operator {
  color: #93c5fd;
}

.code .token.punctuation {
  color: #d1d5db;
}

.code .token.comment {
  color: #6b7280;
  font-style: italic;
}
