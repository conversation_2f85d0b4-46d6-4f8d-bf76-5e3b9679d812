import React from 'react';
import { ChainIcon } from './ChainIcons';
import styles from './ChainGrid.module.css';

interface Chain {
  name: string;
  description?: string;
}

interface ChainGridProps {
  chains: Chain[];
  title?: string;
}

export function ChainGrid({ chains, title }: ChainGridProps) {
  return (
    <div className={styles.container}>
      {title && <h3 className={styles.title}>{title}</h3>}
      <div className={styles.grid}>
        {chains.map((chain, i) => (
          <div key={i} className={styles.chain}>
            <div className={styles.icon}>
              <ChainIcon name={chain.name} size={64} />
            </div>
            <span className={styles.name}>{chain.name}</span>
            {chain.description && (
              <span className={styles.description}>{chain.description}</span>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
