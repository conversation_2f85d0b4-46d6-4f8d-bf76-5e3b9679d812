import { themes as prismThemes } from "prism-react-renderer";
import type { Config } from "@docusaurus/types";
import type * as Preset from "@docusaurus/preset-classic";

const config: Config = {
  title: "Tokai Documentation",
  tagline: "Multi-chain wallet infrastructure for developers",
  favicon: "img/favicon.ico",

  future: { v4: true },
  url: "https://docs.tokai.com",
  baseUrl: "/",

  organizationName: "tokai",
  projectName: "tokai-docs",

  onBrokenLinks: "warn",
  onBrokenMarkdownLinks: "warn",

  i18n: { defaultLocale: "en", locales: ["en"] },

  presets: [
    [
      "classic",
      {
        docs: {
          sidebarPath: "./sidebars.ts",
          editUrl: "https://github.com/tokai/tokai-docs/tree/main/",
        },
        blog: false,
        theme: { customCss: "./src/css/custom.css" },
      } satisfies Preset.Options,
    ],
  ],

  themeConfig: {
    image: "img/docusaurus-social-card.jpg",
    navbar: {
      title: "Tokai Docs",
      // logo: { alt: 'Tokai Logo', src: 'img/logo.svg' },
      items: [
        {
          type: "docSidebar",
          sidebarId: "tutorialSidebar",
          position: "left",
          label: "Documentation",
        },
        {
          href: "https://github.com/tokai/tokai",
          label: "GitHub",
          position: "right",
        },
        {
          href: "https://tokai.com",
          label: "Website",
          position: "right",
        },
      ],
    },
    // footer: {
    //   style: 'dark',
    //   links: [
    //     {
    //       title: 'Documentation',
    //       items: [
    //         { label: 'Getting Started', to: '/docs/intro' },
    //         { label: 'API', to: '/docs/api' },
    //         { label: 'Tutorials', to: '/docs/tutorials/quickstart' },
    //       ],
    //     },
    //     {
    //       title: 'Community',
    //       items: [
    //         { label: 'Discord', href: 'https://discord.gg/tokai' },
    //         { label: 'Twitter', href: 'https://twitter.com/tokai' },
    //         { label: 'GitHub', href: 'https://github.com/tokai/tokai' },
    //       ],
    //     },
    //     {
    //       title: 'Resources',
    //       items: [
    //         { label: 'Website', href: 'https://tokai.com' },
    //         { label: 'Status', href: 'https://status.tokai.com' },
    //       ],
    //     },
    //   ],
    //   copyright: `Copyright © ${new Date().getFullYear()} Tokai. Built with Docusaurus.`,
    // },
    prism: { theme: prismThemes.github, darkTheme: prismThemes.dracula },
  } satisfies Preset.ThemeConfig,
};

export default config;
