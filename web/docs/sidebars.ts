import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";

const sidebars: SidebarsConfig = {
  tutorialSidebar: [
    {
      type: "category",
      label: "API Reference",
      items: [
        "api/index",
        {
          type: "category",
          label: "Auth",
          items: [
            "api/auth/overview",
            "api/auth/endpoints/social-login",
            "api/auth/endpoints/email-login",
            "api/auth/endpoints/magic-link",
            "api/auth/endpoints/mfa-setup",
            "api/auth/endpoints/session-refresh",
          ],
        },
        {
          type: "category",
          label: "Wallets",
          items: [
            "api/wallets/overview",
            "api/wallets/endpoints/create-wallet",
            "api/wallets/endpoints/list-wallets",
            "api/wallets/endpoints/get-wallet",
            "api/wallets/endpoints/update-wallet",
            "api/wallets/endpoints/delete-wallet",
            "api/wallets/endpoints/create-smart-account",
          ],
        },
        {
          type: "category",
          label: "Transactions",
          items: [
            "api/transactions/overview",
            "api/transactions/endpoints/send-transaction",
            "api/transactions/endpoints/get-transaction",
            "api/transactions/endpoints/list-transactions",
            "api/transactions/endpoints/simulate-transaction",
            "api/transactions/endpoints/batch-transactions",
          ],
        },
        {
          type: "category",
          label: "Session Keys",
          items: [
            "api/session-keys/overview",
            "api/session-keys/endpoints/create-session-key",
            "api/session-keys/endpoints/list-session-keys",
            "api/session-keys/endpoints/get-session-key",
            "api/session-keys/endpoints/update-session-key",
            "api/session-keys/endpoints/revoke-session-key",
            "api/session-keys/endpoints/use-session-key",
          ],
        },
        {
          type: "category",
          label: "Analytics",
          items: [
            "api/analytics/overview",
            "api/analytics/endpoints/user-analytics",
            "api/analytics/endpoints/transaction-analytics",
            "api/analytics/endpoints/wallet-analytics",
            "api/analytics/endpoints/performance-metrics",
          ],
        },
        {
          type: "category",
          label: "Webhooks",
          items: [
            "api/webhooks/overview",
            "api/webhooks/endpoints/create-webhook",
            "api/webhooks/endpoints/list-webhooks",
            "api/webhooks/endpoints/update-webhook",
            "api/webhooks/endpoints/delete-webhook",
            "api/webhooks/endpoints/webhook-events",
          ],
        },
      ],
    },
    {
      type: "category",
      label: "Tutorials",
      items: ["tutorials/quickstart"],
    },
  ],
};

export default sidebars;
