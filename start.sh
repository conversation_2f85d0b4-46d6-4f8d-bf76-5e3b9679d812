#!/bin/bash

# Tokai Wallet System - Quick Start Script
# This script helps you get the backend and dashboard running quickly

set -e

echo "🚀 Tokai Wallet System - Quick Start"
echo "====================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

echo "✅ Docker and Docker Compose are installed"

# Generate secure keys if .env doesn't exist
if [ ! -f .env ]; then
    echo "🔐 Generating secure environment variables..."
    
    # Generate JWT secret
    JWT_SECRET=$(openssl rand -base64 32)
    
    # Generate encryption key
    WALLET_ENCRYPTION_KEY=$(openssl rand -base64 32)
    
    # Create .env file
    cat > .env << EOF
# Tokai Wallet System Environment Variables
JWT_SECRET=$JWT_SECRET
WALLET_ENCRYPTION_KEY=$WALLET_ENCRYPTION_KEY
FRONTEND_URL=http://localhost:3000
EOF
    
    echo "✅ Created .env file with secure keys"
else
    echo "✅ .env file already exists"
fi

# Check if backend directory exists
if [ ! -d "backend" ]; then
    echo "❌ Backend directory not found. Please ensure you're in the correct directory."
    exit 1
fi

# Check if dashboard directory exists
if [ ! -d "dashboard" ]; then
    echo "❌ Dashboard directory not found. Please ensure you're in the correct directory."
    exit 1
fi

# Check if packages/react directory exists
if [ ! -d "packages/react" ]; then
    echo "❌ React package directory not found. Please ensure you're in the correct directory."
    exit 1
fi

echo "🏗️  Building and starting services..."

# Build and start services
docker-compose up --build -d

echo "⏳ Waiting for services to start..."

# Wait for backend to be healthy
echo "🔍 Checking backend health..."
for i in {1..30}; do
    if curl -f http://localhost:3001/health &> /dev/null; then
        echo "✅ Backend is healthy"
        break
    fi
    
    if [ $i -eq 30 ]; then
        echo "❌ Backend failed to start within 30 seconds"
        docker-compose logs backend
        exit 1
    fi
    
    echo "⏳ Waiting for backend... ($i/30)"
    sleep 2
done

# Wait for frontend to be ready
echo "🔍 Checking frontend..."
for i in {1..30}; do
    if curl -f http://localhost:3000 &> /dev/null; then
        echo "✅ Frontend is ready"
        break
    fi
    
    if [ $i -eq 30 ]; then
        echo "❌ Frontend failed to start within 30 seconds"
        docker-compose logs frontend
        exit 1
    fi
    
    echo "⏳ Waiting for frontend... ($i/30)"
    sleep 2
done

echo ""
echo "🎉 Tokai Wallet System is now running!"
echo ""
echo "📱 Services:"
echo "   Frontend:     http://localhost:3000"
echo "   Backend API:  http://localhost:3001"
echo "   Health Check: http://localhost:3001/health"
echo ""
echo "🔧 Useful Commands:"
echo "   View logs:     docker-compose logs -f"
echo "   Stop services: docker-compose down"
echo "   Restart:       docker-compose restart"
echo "   Update:        docker-compose pull && docker-compose up --build -d"
echo ""
echo "📚 Documentation:"
echo "   Backend:       BACKEND_README.md"
echo "   Auto-Creation: AUTO_WALLET_CREATION.md"
echo ""
echo "🔐 Security Notes:"
echo "   - Change the JWT_SECRET and WALLET_ENCRYPTION_KEY in production"
echo "   - Enable HTTPS in production"
echo "   - Set up proper firewall rules"
echo ""
echo "🚀 Happy wallet creation!" 