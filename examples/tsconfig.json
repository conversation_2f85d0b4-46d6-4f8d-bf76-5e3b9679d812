{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@tokai/react": ["../packages/react/src"], "@tokai/wallet-managers": ["../packages/wallet-managers/src"], "@tokai/wallet-types": ["../packages/wallet-types/src"]}}, "include": ["**/*.ts", "**/*.tsx"], "references": [{"path": "./tsconfig.node.json"}]}