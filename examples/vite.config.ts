import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@tokai/react': path.resolve(__dirname, '../packages/react/src'),
      '@tokai/wallet-managers': path.resolve(__dirname, '../packages/wallet-managers/src'),
      '@tokai/wallet-types': path.resolve(__dirname, '../packages/wallet-types/src')
    }
  }
})
