{"name": "tokai-examples", "version": "1.0.0", "description": "Tokai SDK examples and demos", "main": "tokai-demo.tsx", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@tokai/react": "workspace:*", "@tokai/wallet-managers": "workspace:*", "@tokai/wallet-types": "workspace:*", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.0.0", "vite": "^4.0.0"}}