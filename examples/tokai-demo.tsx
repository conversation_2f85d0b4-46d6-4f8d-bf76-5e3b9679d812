import React, { useState } from "react";
import {
  TokaiProvider,
  TokaiConnectButton,
  TokaiAuth,
  useTokai,
} from "@tokai/react";

// Demo App Component
const TokaiDemoApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"connect" | "auth" | "embedded">(
    "connect"
  );

  const config = {
    apiUrl: "http://localhost:3001/api",
    apiKey: "demo-api-key",
    appName: "Tokai Demo",
    theme: "dark" as const,
    socialLogins: [
      { id: "google" as const, enabled: true },
      { id: "discord" as const, enabled: true },
      { id: "twitter" as const, enabled: true },
      { id: "github" as const, enabled: true },
    ],
    embeddedWallets: true,
    crossAppIdentity: true,
    customization: {
      primaryColor: "#6366f1",
      backgroundColor: "#1a1a1a",
      textColor: "#ffffff",
      borderRadius: "12px",
      fontFamily: '"Inter", sans-serif',
      brandLogo: undefined,
    },
  };

  return (
    <TokaiProvider config={config}>
      <div
        style={{
          minHeight: "100vh",
          background: "linear-gradient(135deg, #1a1a1a 0%, #2a1a2a 100%)",
          color: "#ffffff",
          fontFamily: '"Inter", sans-serif',
          padding: "20px",
        }}
      >
        {/* Header */}
        <header
          style={{
            textAlign: "center",
            marginBottom: "40px",
          }}
        >
          <h1
            style={{
              fontSize: "48px",
              margin: "0 0 16px 0",
              background: "linear-gradient(45deg, #6366f1, #8b5cf6)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
            }}
          >
            🚀 Tokai Demo
          </h1>
          <p
            style={{
              fontSize: "18px",
              color: "#a0a0a0",
              maxWidth: "600px",
              margin: "0 auto",
            }}
          >
            Experience seamless wallet creation and social authentication with
            the Tokai SDK
          </p>
        </header>

        {/* Navigation */}
        <nav
          style={{
            display: "flex",
            justifyContent: "center",
            marginBottom: "40px",
            gap: "8px",
          }}
        >
          {[
            {
              id: "connect",
              label: "🔗 Connect Button",
              desc: "One-click wallet connection",
            },
            {
              id: "auth",
              label: "🔐 Auth Modal",
              desc: "Social & email authentication",
            },
            {
              id: "embedded",
              label: "💎 Embedded Wallets",
              desc: "Smart account features",
            },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              style={{
                padding: "16px 24px",
                backgroundColor: activeTab === tab.id ? "#6366f1" : "#2a2a2a",
                color: "#ffffff",
                border: "none",
                borderRadius: "12px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "500",
                transition: "all 0.2s ease",
                textAlign: "center",
                minWidth: "200px",
              }}
            >
              <div>{tab.label}</div>
              <div style={{ fontSize: "12px", opacity: 0.7, marginTop: "4px" }}>
                {tab.desc}
              </div>
            </button>
          ))}
        </nav>

        {/* Demo Content */}
        <main
          style={{
            maxWidth: "800px",
            margin: "0 auto",
          }}
        >
          {activeTab === "connect" && <ConnectButtonDemo />}
          {activeTab === "auth" && <AuthModalDemo />}
          {activeTab === "embedded" && <EmbeddedWalletDemo />}
        </main>

        {/* Features Grid */}
        <section
          style={{
            marginTop: "80px",
            maxWidth: "1200px",
            margin: "80px auto 0",
          }}
        >
          <h2
            style={{
              textAlign: "center",
              marginBottom: "40px",
              fontSize: "32px",
            }}
          >
            ✨ Key Features
          </h2>

          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
              gap: "24px",
            }}
          >
            {[
              {
                icon: "🔐",
                title: "Social Authentication",
                description:
                  "Google, Discord, Twitter, GitHub login with OAuth 2.0",
                features: [
                  "One-click signup",
                  "Account linking",
                  "Profile sync",
                ],
              },
              {
                icon: "💎",
                title: "Embedded Wallets",
                description:
                  "Smart accounts with social recovery and gasless transactions",
                features: [
                  "No seed phrases",
                  "Social recovery",
                  "Account abstraction",
                ],
              },
              {
                icon: "🌐",
                title: "Cross-App Identity",
                description:
                  "Portable identity and wallets across applications",
                features: [
                  "Global user ID",
                  "Wallet portability",
                  "Unified identity",
                ],
              },
              {
                icon: "⚡",
                title: "Developer Experience",
                description: "Easy-to-use React SDK with TypeScript support",
                features: ["React hooks", "TypeScript", "Customizable UI"],
              },
              {
                icon: "🎨",
                title: "Customizable UI",
                description: "Fully customizable themes and branding options",
                features: ["Dark/light themes", "Custom colors", "Brand logos"],
              },
              {
                icon: "🔒",
                title: "Enterprise Security",
                description:
                  "Bank-grade security with encryption and compliance",
                features: [
                  "AES-256 encryption",
                  "API key auth",
                  "Rate limiting",
                ],
              },
            ].map((feature, index) => (
              <div
                key={index}
                style={{
                  padding: "32px",
                  backgroundColor: "#2a2a2a",
                  borderRadius: "16px",
                  border: "1px solid #404040",
                }}
              >
                <div style={{ fontSize: "48px", marginBottom: "16px" }}>
                  {feature.icon}
                </div>
                <h3
                  style={{
                    fontSize: "20px",
                    marginBottom: "12px",
                    color: "#ffffff",
                  }}
                >
                  {feature.title}
                </h3>
                <p
                  style={{
                    color: "#a0a0a0",
                    marginBottom: "20px",
                    lineHeight: "1.6",
                  }}
                >
                  {feature.description}
                </p>
                <ul
                  style={{
                    listStyle: "none",
                    padding: 0,
                    margin: 0,
                  }}
                >
                  {feature.features.map((item, i) => (
                    <li
                      key={i}
                      style={{
                        color: "#10b981",
                        marginBottom: "8px",
                        fontSize: "14px",
                      }}
                    >
                      ✓ {item}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </section>
      </div>
    </TokaiProvider>
  );
};

// Connect Button Demo
const ConnectButtonDemo: React.FC = () => {
  const [buttonVariant, setButtonVariant] = useState<
    "primary" | "secondary" | "outline"
  >("primary");
  const [buttonSize, setButtonSize] = useState<"sm" | "md" | "lg">("md");

  return (
    <div
      style={{
        padding: "40px",
        backgroundColor: "#2a2a2a",
        borderRadius: "16px",
        border: "1px solid #404040",
      }}
    >
      <h2 style={{ marginBottom: "24px", fontSize: "24px" }}>
        🔗 Connect Button Component
      </h2>

      <p style={{ color: "#a0a0a0", marginBottom: "32px", lineHeight: "1.6" }}>
        The TokaiConnectButton provides a seamless authentication experience
        with social logins, email authentication, and automatic wallet creation.
      </p>

      {/* Controls */}
      <div
        style={{
          display: "flex",
          gap: "20px",
          marginBottom: "32px",
          flexWrap: "wrap",
        }}
      >
        <div>
          <label
            style={{ display: "block", marginBottom: "8px", fontSize: "14px" }}
          >
            Variant:
          </label>
          <select
            value={buttonVariant}
            onChange={(e) => setButtonVariant(e.target.value as any)}
            style={{
              padding: "8px 12px",
              backgroundColor: "#1a1a1a",
              color: "#ffffff",
              border: "1px solid #404040",
              borderRadius: "6px",
            }}
          >
            <option value="primary">Primary</option>
            <option value="secondary">Secondary</option>
            <option value="outline">Outline</option>
          </select>
        </div>

        <div>
          <label
            style={{ display: "block", marginBottom: "8px", fontSize: "14px" }}
          >
            Size:
          </label>
          <select
            value={buttonSize}
            onChange={(e) => setButtonSize(e.target.value as any)}
            style={{
              padding: "8px 12px",
              backgroundColor: "#1a1a1a",
              color: "#ffffff",
              border: "1px solid #404040",
              borderRadius: "6px",
            }}
          >
            <option value="sm">Small</option>
            <option value="md">Medium</option>
            <option value="lg">Large</option>
          </select>
        </div>
      </div>

      {/* Demo */}
      <div
        style={{
          padding: "40px",
          backgroundColor: "#1a1a1a",
          borderRadius: "12px",
          textAlign: "center",
        }}
      >
                  <TokaiConnectButton
            variant={buttonVariant}
            size={buttonSize}
          />
      </div>

      {/* Code Example */}
      <details style={{ marginTop: "24px" }}>
        <summary
          style={{
            cursor: "pointer",
            fontSize: "16px",
            fontWeight: "500",
            marginBottom: "12px",
          }}
        >
          📝 View Code
        </summary>
        <pre
          style={{
            backgroundColor: "#1a1a1a",
            padding: "16px",
            borderRadius: "8px",
            overflow: "auto",
            fontSize: "14px",
            color: "#a0a0a0",
          }}
        >
          {`<TokaiConnectButton
  appName="Tokai Demo"
  variant="${buttonVariant}"
  size="${buttonSize}"
  embeddedWallets={true}
  onSuccess={(user) => console.log('User connected:', user)}
  onError={(error) => console.error('Connection error:', error)}
/>`}
        </pre>
      </details>
    </div>
  );
};

// Auth Modal Demo
const AuthModalDemo: React.FC = () => {
  const [showModal, setShowModal] = useState(false);

  return (
    <div
      style={{
        padding: "40px",
        backgroundColor: "#2a2a2a",
        borderRadius: "16px",
        border: "1px solid #404040",
      }}
    >
      <h2 style={{ marginBottom: "24px", fontSize: "24px" }}>
        🔐 Authentication Modal
      </h2>

      <p style={{ color: "#a0a0a0", marginBottom: "32px", lineHeight: "1.6" }}>
        The TokaiAuth component provides a complete authentication flow with
        social logins, email registration, and seamless wallet creation.
      </p>

      <div
        style={{
          padding: "40px",
          backgroundColor: "#1a1a1a",
          borderRadius: "12px",
          textAlign: "center",
        }}
      >
        <button
          onClick={() => setShowModal(true)}
          style={{
            padding: "12px 24px",
            backgroundColor: "#6366f1",
            color: "#ffffff",
            border: "none",
            borderRadius: "8px",
            cursor: "pointer",
            fontSize: "16px",
            fontWeight: "500",
          }}
        >
          Open Auth Modal
        </button>
      </div>

      {showModal && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 10000,
          }}
        >
          <div
            style={{
              backgroundColor: "#1a1a1a",
              borderRadius: "12px",
              padding: "24px",
              maxWidth: "400px",
              width: "90%",
              maxHeight: "90vh",
              overflowY: "auto",
              position: "relative",
            }}
          >
            <button
              onClick={() => setShowModal(false)}
              style={{
                position: "absolute",
                top: "16px",
                right: "16px",
                background: "none",
                border: "none",
                fontSize: "18px",
                cursor: "pointer",
                color: "#ffffff",
              }}
            >
              ✕
            </button>

            <TokaiAuth
              appName="Tokai Demo"
              embeddedWallets={true}
              onSuccess={(user) => {
                console.log('Auth success:', user);
                setShowModal(false);
              }}
              onError={(error) => console.error('Auth error:', error)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

// Embedded Wallet Demo
const EmbeddedWalletDemo: React.FC = () => {
  const { user, wallets } = useTokai();

  return (
    <div
      style={{
        padding: "40px",
        backgroundColor: "#2a2a2a",
        borderRadius: "16px",
        border: "1px solid #404040",
      }}
    >
      <h2 style={{ marginBottom: "24px", fontSize: "24px" }}>
        💎 Embedded Wallets
      </h2>

      <p style={{ color: "#a0a0a0", marginBottom: "32px", lineHeight: "1.6" }}>
        Embedded wallets provide a seamless Web3 experience with smart accounts,
        social recovery, and gasless transactions.
      </p>

      {user ? (
        <div>
          <h3 style={{ marginBottom: "16px" }}>Your Wallets</h3>
          {wallets.length > 0 ? (
            <div
              style={{ display: "flex", flexDirection: "column", gap: "12px" }}
            >
              {wallets.map((wallet) => (
                <div
                  key={wallet.id}
                  style={{
                    padding: "16px",
                    backgroundColor: "#1a1a1a",
                    borderRadius: "8px",
                    border: "1px solid #404040",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div>
                      <div style={{ fontWeight: "500", marginBottom: "4px" }}>
                        {wallet.network.toUpperCase()}
                      </div>
                      <div style={{ fontSize: "14px", color: "#a0a0a0" }}>
                        {wallet.wallet_address.substring(0, 6)}...
                        {wallet.wallet_address.substring(
                          wallet.wallet_address.length - 4
                        )}
                      </div>
                    </div>
                    <div style={{ textAlign: "right" }}>
                      <div
                        style={{
                          fontSize: "12px",
                          color: wallet.is_embedded ? "#10b981" : "#6366f1",
                          marginBottom: "4px",
                        }}
                      >
                        {wallet.is_embedded ? "🔐 Embedded" : "🔗 Standard"}
                      </div>
                      <div
                        style={{
                          fontSize: "12px",
                          color: wallet.is_active ? "#10b981" : "#ef4444",
                        }}
                      >
                        {wallet.is_active ? "✓ Active" : "○ Inactive"}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p style={{ color: "#a0a0a0" }}>
              No wallets found. Connect to create your first wallet!
            </p>
          )}
        </div>
      ) : (
        <div
          style={{
            padding: "40px",
            backgroundColor: "#1a1a1a",
            borderRadius: "12px",
            textAlign: "center",
          }}
        >
          <p style={{ color: "#a0a0a0", marginBottom: "20px" }}>
            Connect your wallet to see embedded wallet features
          </p>
          <TokaiConnectButton />
        </div>
      )}
    </div>
  );
};

export default TokaiDemoApp;
