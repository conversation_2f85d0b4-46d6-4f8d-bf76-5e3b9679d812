#!/bin/bash

# Tokai Deployment Script
# Self-hosted Wallet Infrastructure Platform

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to generate random string
generate_random() {
    openssl rand -hex 32
}

# Check dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "All dependencies are installed."
}

# Setup environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        print_status "Creating .env file from template..."
        cp .env.example .env
        
        # Generate secure random keys
        JWT_SECRET=$(generate_random)
        WALLET_ENCRYPTION_KEY=$(generate_random)
        
        # Update .env with generated keys
        sed -i "s/your-jwt-secret-here/$JWT_SECRET/" .env
        sed -i "s/your-wallet-encryption-key-here/$WALLET_ENCRYPTION_KEY/" .env
        
        print_success ".env file created with secure random keys."
        print_warning "Please edit .env file to configure OAuth providers and other settings."
    else
        print_status ".env file already exists."
    fi
}

# Function to display usage
usage() {
    echo "Tokai Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  dev       Start development environment"
    echo "  prod      Start production environment"
    echo "  stop      Stop all services"
    echo "  restart   Restart all services"
    echo "  logs      Show logs"
    echo "  clean     Clean up containers and volumes"
    echo "  backup    Backup database and configuration"
    echo "  restore   Restore from backup"
    echo "  help      Show this help message"
    echo ""
    echo "Options:"
    echo "  --build   Force rebuild of containers"
    echo "  --pull    Pull latest images before starting"
    echo ""
}

# Development deployment
deploy_dev() {
    print_status "Starting Tokai in development mode..."
    
    if [ "$BUILD" = true ]; then
        print_status "Building development containers..."
        docker-compose -f docker-compose.dev.yml build
    fi
    
    print_status "Starting development services..."
    docker-compose -f docker-compose.dev.yml up -d
    
    print_success "Tokai development environment is starting!"
    print_status "Backend API: http://localhost:3001"
    print_status "Frontend Dashboard: http://localhost:3000"
    print_status "Use 'docker-compose -f docker-compose.dev.yml logs -f' to view logs"
}

# Production deployment
deploy_prod() {
    print_status "Starting Tokai in production mode..."
    
    # Check if .env exists and has required variables
    if [ ! -f .env ]; then
        print_error ".env file not found. Run setup first."
        exit 1
    fi
    
    if [ "$BUILD" = true ]; then
        print_status "Building production containers..."
        docker-compose build
    fi
    
    if [ "$PULL" = true ]; then
        print_status "Pulling latest images..."
        docker-compose pull
    fi
    
    print_status "Starting production services..."
    docker-compose up -d
    
    # Wait for services to be healthy
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if backend is healthy
    if curl -f http://localhost:3001/health >/dev/null 2>&1; then
        print_success "Tokai is running successfully!"
        print_status "API: http://localhost:3001"
        print_status "Dashboard: http://localhost:3000"
        print_status "Health Check: http://localhost:3001/health"
    else
        print_error "Backend service failed to start properly."
        print_status "Check logs with: docker-compose logs"
        exit 1
    fi
}

# Stop services
stop_services() {
    print_status "Stopping Tokai services..."
    
    if [ -f docker-compose.yml ]; then
        docker-compose down
    fi
    
    if [ -f docker-compose.dev.yml ]; then
        docker-compose -f docker-compose.dev.yml down
    fi
    
    print_success "All services stopped."
}

# Restart services
restart_services() {
    print_status "Restarting Tokai services..."
    stop_services
    sleep 2
    
    if [ "$ENVIRONMENT" = "dev" ]; then
        deploy_dev
    else
        deploy_prod
    fi
}

# Show logs
show_logs() {
    if [ "$ENVIRONMENT" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml logs -f
    else
        docker-compose logs -f
    fi
}

# Clean up
cleanup() {
    print_warning "This will remove all containers, networks, and volumes."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up..."
        
        # Stop and remove containers
        docker-compose down -v --remove-orphans 2>/dev/null || true
        docker-compose -f docker-compose.dev.yml down -v --remove-orphans 2>/dev/null || true
        
        # Remove images
        docker images "tokai*" -q | xargs -r docker rmi -f 2>/dev/null || true
        
        # Prune system
        docker system prune -f
        
        print_success "Cleanup completed."
    else
        print_status "Cleanup cancelled."
    fi
}

# Backup
backup_data() {
    print_status "Creating backup..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup database
    if docker volume ls | grep -q tokai-data; then
        print_status "Backing up database..."
        docker run --rm -v tokai-data:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine \
            sh -c "cd /data && tar czf /backup/database.tar.gz ."
    fi
    
    # Backup configuration
    print_status "Backing up configuration..."
    cp .env "$BACKUP_DIR/" 2>/dev/null || true
    cp docker-compose.yml "$BACKUP_DIR/" 2>/dev/null || true
    
    print_success "Backup created in $BACKUP_DIR"
}

# Restore
restore_data() {
    print_status "Available backups:"
    ls -la backups/ 2>/dev/null || {
        print_error "No backups found."
        exit 1
    }
    
    read -p "Enter backup directory name: " BACKUP_NAME
    BACKUP_DIR="backups/$BACKUP_NAME"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        print_error "Backup directory not found."
        exit 1
    fi
    
    print_warning "This will overwrite current data."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Restoring backup..."
        
        # Restore database
        if [ -f "$BACKUP_DIR/database.tar.gz" ]; then
            docker run --rm -v tokai-data:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine \
                sh -c "cd /data && tar xzf /backup/database.tar.gz"
        fi
        
        # Restore configuration
        cp "$BACKUP_DIR/.env" . 2>/dev/null || true
        
        print_success "Restore completed."
    fi
}

# Parse arguments
BUILD=false
PULL=false
ENVIRONMENT="prod"

while [[ $# -gt 0 ]]; do
    case $1 in
        --build)
            BUILD=true
            shift
            ;;
        --pull)
            PULL=true
            shift
            ;;
        dev)
            ENVIRONMENT="dev"
            shift
            ;;
        prod)
            ENVIRONMENT="prod"
            shift
            ;;
        stop)
            check_dependencies
            stop_services
            exit 0
            ;;
        restart)
            check_dependencies
            restart_services
            exit 0
            ;;
        logs)
            show_logs
            exit 0
            ;;
        clean)
            cleanup
            exit 0
            ;;
        backup)
            backup_data
            exit 0
            ;;
        restore)
            restore_data
            exit 0
            ;;
        help|--help|-h)
            usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_status "🚀 Tokai Self-hosted Wallet Infrastructure Platform"
    print_status "======================================"
    
    check_dependencies
    setup_environment
    
    if [ "$ENVIRONMENT" = "dev" ]; then
        deploy_dev
    else
        deploy_prod
    fi
}

# Run main function
main