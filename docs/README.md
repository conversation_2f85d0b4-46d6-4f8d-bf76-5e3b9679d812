# 📚 Tokai Documentation

Welcome to the Tokai documentation! This directory contains comprehensive guides and references for deploying, configuring, and using Tokai - your self-hosted wallet infrastructure platform.

## 📋 Documentation Index

### 🚀 Getting Started
- **[Deployment Guide](./DEPLOYMENT_GUIDE.md)** - Complete deployment guide for development and production
- **[Getting Started Guide](./GETTING_STARTED.md)** - Quick start guide for developers
- **[API Quick Reference](./API_QUICK_REFERENCE.md)** - Quick reference for all API endpoints with examples

### 🛠️ Development & Configuration
- **[Wallet Type Explanation](./WALLET_TYPE_EXPLANATION.md)** - Detailed explanation of supported wallet types
- **[Development Checklist](./TOKAI_DEVELOPMENT_CHECKLIST.md)** - Development status and roadmap
- **[Cookie-Based Authentication](./COOKIE_BASED_AUTHENTICATION.md)** - Authentication implementation details
- **[Email Verification & OTP Guide](./EMAIL_VERIFICATION_AND_OTP_GUIDE.md)** - Email verification system
- **[Frontend State Management](./FRONTEND_STATE_MANAGEMENT_REFACTOR.md)** - State management architecture

### 🔧 Technical Documentation
- **[Technical Debt Q3 2025](./TECHNICAL_DEBT_Q3_2025.md)** - Technical debt analysis and roadmap

## 🚀 Quick Start

### 1. One-Command Setup
```bash
# Development setup
./setup.sh

# Production setup
./setup.sh production your-domain.com
```

### 2. Manual Setup
```bash
# Clone repository
git clone https://github.com/your-org/tokai.git
cd tokai

# Deploy with Docker
docker-compose up -d
```

### 3. Access Your Deployment
- **Dashboard**: http://localhost:3000
- **API**: http://localhost:3001
- **Health Check**: http://localhost:3001/health

## 📖 Documentation Structure

```
docs/
├── README.md                                    # This file
├── DEPLOYMENT_GUIDE.md                          # Complete deployment guide
├── GETTING_STARTED.md                           # Developer quick start guide
├── API_QUICK_REFERENCE.md                       # API endpoint reference
├── WALLET_TYPE_EXPLANATION.md                   # Wallet types and usage
├── TOKAI_DEVELOPMENT_CHECKLIST.md               # Development status
├── COOKIE_BASED_AUTHENTICATION.md               # Auth implementation
├── EMAIL_VERIFICATION_AND_OTP_GUIDE.md          # Email verification
├── FRONTEND_STATE_MANAGEMENT_REFACTOR.md        # State management
└── TECHNICAL_DEBT_Q3_2025.md                    # Technical debt analysis
```

## 🎯 Key Features Covered

### 🔐 Authentication & Security
- **Multi-provider OAuth** (Google, Discord, Twitter, GitHub)
- **Email/password authentication**
- **Magic link authentication**
- **MFA/2FA support**
- **Session key management**
- **Cookie-based authentication**

### 💰 Wallet Management
- **Embedded wallets** (backend-generated, encrypted)
- **External wallet connections** (MetaMask, Coinbase)
- **Multi-chain support** (Ethereum, Solana, Bitcoin, etc.)
- **Smart accounts** (ERC-4337)
- **Wallet recovery systems**

### 📊 Analytics & Monitoring
- **Event tracking**
- **User analytics**
- **Transaction analytics**
- **Health monitoring**
- **Performance metrics**

### 🏗️ Infrastructure
- **Single Docker image** (frontend + backend)
- **SQLite/PostgreSQL support**
- **Redis caching**
- **Nginx reverse proxy**
- **Prometheus + Grafana monitoring**

## 🔧 Common Tasks

### Deploy to Production
1. Read the [Deployment Guide](./DEPLOYMENT_GUIDE.md)
2. Set up your environment variables
3. Run the deployment script
4. Configure your domain and SSL

### API Integration
1. Check the [API Quick Reference](./API_QUICK_REFERENCE.md)
2. Set up authentication
3. Start with basic wallet operations
4. Implement advanced features

### Development Setup
1. Follow the [Getting Started Guide](./GETTING_STARTED.md)
2. Set up your development environment
3. Run the development servers
4. Start building features

## 📚 Additional Resources

### Web Documentation
For a more interactive documentation experience, visit our web documentation at `/web/docs` which includes:
- Interactive API documentation
- Code examples
- Visual guides
- Search functionality

### Package Documentation
- **React SDK**: `/packages/react/README.md`
- **Wallet Managers**: `/packages/wallet-managers/README.md`
- **Wallet Connectors**: `/packages/wallet-connectors/README.md`
- **Hooks**: `/packages/hooks/README.md`

## 🤝 Contributing

1. Read the [Development Checklist](./TOKAI_DEVELOPMENT_CHECKLIST.md)
2. Check the [Technical Debt](./TECHNICAL_DEBT_Q3_2025.md) for areas that need attention
3. Follow the coding standards and guidelines
4. Submit pull requests with clear descriptions

## 📞 Support

- **Issues**: Create an issue on GitHub
- **Discussions**: Use GitHub Discussions
- **Documentation**: Check this docs folder and web docs
- **API**: Use the API Quick Reference for endpoint details

---

**Last Updated**: December 2024  
**Version**: 1.0.0
