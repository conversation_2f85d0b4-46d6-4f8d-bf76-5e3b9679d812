# 🔌 Tokai API Quick Reference

Quick reference for all Tokai API endpoints with examples.

## 📋 Base URLs

```
Development: http://localhost:3001
Production:  https://your-domain.com/api
```

## 🔐 Authentication

### Register User
```bash
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure-password",
  "fullName": "John Doe"
}

Response:
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "fullName": "<PERSON>",
      "created_at": "2023-12-01T10:00:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2023-12-08T10:00:00Z"
  }
}
```

### Login User
```bash
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure-password"
}

Response:
{
  "success": true,
  "data": {
    "user": { ... },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2023-12-08T10:00:00Z"
  }
}
```

### OAuth Login
```bash
# Initiate OAuth flow
GET /api/auth/google
GET /api/auth/discord
GET /api/auth/twitter
GET /api/auth/github

# Complete OAuth flow
POST /api/auth/google/callback
POST /api/auth/discord/callback
POST /api/auth/twitter/callback
POST /api/auth/github/callback

{
  "code": "oauth_code",
  "state": "state_parameter"
}
```

### Get Current User
```bash
GET /api/auth/me
Authorization: Bearer <token>  
Note: In the dashboard app, tokens are managed via secure HTTP-only cookies. Bearer headers are used in API examples and for session keys.

Response:
{
  "success": true,
  "data": {
    "id": "user_123",
    "email": "<EMAIL>",
    "fullName": "John Doe",
    "avatar_url": "https://...",
    "created_at": "2023-12-01T10:00:00Z"
  }
}
```

## 💰 Wallet Management

### List User Wallets
```bash
GET /api/wallets
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "id": "wallet_123",
      "network": "ethereum",
      "wallet_address": "0x1234...",
      "balance": "1.5",
      "is_embedded": true,
      "created_at": "2023-12-01T10:00:00Z"
    }
  ]
}
```

### Create Wallet
```bash
POST /api/wallets
Authorization: Bearer <token>
Content-Type: application/json

{
  "network": "ethereum"
}

Response:
{
  "success": true,
  "data": {
    "id": "wallet_123",
    "network": "ethereum",
    "wallet_address": "0x1234...",
    "private_key": "encrypted_private_key",
    "created_at": "2023-12-01T10:00:00Z"
  }
}
```

### Get Wallet Details
```bash
GET /api/wallets/{walletId}
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "id": "wallet_123",
    "network": "ethereum",
    "wallet_address": "0x1234...",
    "balance": "1.5",
    "is_embedded": true,
    "created_at": "2023-12-01T10:00:00Z"
  }
}
```

### Get Wallet Balance
```bash
GET /api/wallets/{walletId}/balance
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "balance": "1.5",
    "currency": "ETH",
    "network": "ethereum",
    "last_updated": "2023-12-01T10:00:00Z"
  }
}
```

### Send Transaction
```bash
POST /api/wallets/{walletId}/send
Authorization: Bearer <token>
Content-Type: application/json

{
  "to": "0x5678...",
  "amount": "0.1",
  "network": "ethereum",
  "gas_limit": "21000"
}

Response:
{
  "success": true,
  "data": {
    "transaction_hash": "0xabcd...",
    "status": "pending",
    "gas_used": "21000",
    "created_at": "2023-12-01T10:00:00Z"
  }
}
```

### Get Wallet Transactions
```bash
GET /api/wallets/{walletId}/transactions?limit=10&offset=0
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "hash": "0xabcd...",
      "from": "0x1234...",
      "to": "0x5678...",
      "amount": "0.1",
      "status": "confirmed",
      "block_number": 12345678,
      "created_at": "2023-12-01T10:00:00Z"
    }
  ]
}
```

## 🔐 Embedded Wallets

### Create Embedded Wallet
```bash
POST /api/wallets/embedded
Authorization: Bearer <token>
Content-Type: application/json

{
  "network": "ethereum",
  "recoveryMethod": "email"
}

Response:
{
  "success": true,
  "data": {
    "id": "embedded_123",
    "network": "ethereum",
    "wallet_address": "0x1234...",
    "recovery_code": "recovery_code_here",
    "is_deployed": false,
    "created_at": "2023-12-01T10:00:00Z"
  }
}
```

### Deploy Embedded Wallet
```bash
POST /api/wallets/embedded/{walletId}/deploy
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "id": "embedded_123",
    "network": "ethereum",
    "wallet_address": "0x1234...",
    "is_deployed": true,
    "deployed_at": "2023-12-01T10:00:00Z"
  }
}
```

### Recover Embedded Wallet
```bash
POST /api/wallets/embedded/recover
Content-Type: application/json

{
  "recoveryCode": "your_recovery_code"
}

Response:
{
  "success": true,
  "data": {
    "id": "embedded_123",
    "network": "ethereum",
    "wallet_address": "0x1234...",
    "is_deployed": true,
    "recovered_at": "2023-12-01T10:00:00Z"
  }
}
```

## 🔑 Session Keys

### Create Session Key
```bash
POST /api/session-keys
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Trading Bot",
  "permissions": ["read", "send"],
  "expiryHours": 24
}

Response:
{
  "success": true,
  "data": {
    "id": "session_123",
    "name": "Trading Bot",
    "key": "sk_1234567890...",
    "permissions": ["read", "send"],
    "expires_at": "2023-12-02T10:00:00Z",
    "created_at": "2023-12-01T10:00:00Z"
  }
}
```

### List Session Keys
```bash
GET /api/session-keys
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "id": "session_123",
      "name": "Trading Bot",
      "permissions": ["read", "send"],
      "expires_at": "2023-12-02T10:00:00Z",
      "created_at": "2023-12-01T10:00:00Z"
    }
  ]
}
```

### Revoke Session Key
```bash
DELETE /api/session-keys/{keyId}
Authorization: Bearer <token>

Response:
{
  "success": true,
  "message": "Session key revoked successfully"
}
```

## 📊 Analytics

### Track Event
```bash
POST /api/analytics/track
Authorization: Bearer <token>
Content-Type: application/json

{
  "event": "wallet_created",
  "properties": {
    "network": "ethereum",
    "walletType": "embedded",
    "userId": "user_123"
  }
}

Response:
{
  "success": true,
  "data": {
    "event_id": "event_123",
    "tracked_at": "2023-12-01T10:00:00Z"
  }
}
```

### Get Analytics
```bash
# Get events
GET /api/analytics/events?start_date=2023-12-01&end_date=2023-12-31
Authorization: Bearer <token>

# Get user analytics
GET /api/analytics/users?period=30d
Authorization: Bearer <token>

# Get transaction analytics
GET /api/analytics/transactions?network=ethereum
Authorization: Bearer <token>
```

## 🏥 Health & Status

### Health Check
```bash
GET /health

Response:
{
  "status": "healthy",
  "timestamp": "2023-12-01T10:00:00Z",
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "api": "healthy"
  }
}
```

### API Status
```bash
GET /api/status

Response:
{
  "success": true,
  "data": {
    "status": "operational",
    "uptime": "86400",
    "version": "1.0.0",
    "environment": "production"
  }
}
```

### Database Status
```bash
GET /api/status/database

Response:
{
  "success": true,
  "data": {
    "status": "connected",
    "type": "sqlite",
    "tables": ["users", "wallets", "transactions"],
    "size": "1024000"
  }
}
```

## 🔧 Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid email format",
    "details": {
      "field": "email",
      "value": "invalid-email"
    }
  }
}
```

### Common Error Codes
- `AUTHENTICATION_ERROR` - Invalid or missing token
- `AUTHORIZATION_ERROR` - Insufficient permissions
- `VALIDATION_ERROR` - Invalid request data
- `NOT_FOUND` - Resource not found
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `INTERNAL_ERROR` - Server error

## 📝 Usage Examples

### Complete Wallet Flow
```bash
# 1. Register user
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password","fullName":"John Doe"}'

# 2. Login and get token
TOKEN=$(curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}' | jq -r '.data.token')

# 3. Create wallet
curl -X POST http://localhost:3001/api/wallets \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"network":"ethereum"}'

# 4. Get wallet balance
curl -X GET http://localhost:3001/api/wallets/wallet_123/balance \
  -H "Authorization: Bearer $TOKEN"
```

### Session Key Usage
```bash
# Create session key
SESSION_KEY=$(curl -X POST http://localhost:3001/api/session-keys \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"API Client","permissions":["read"],"expiryHours":24}' | jq -r '.data.key')

# Use session key
curl -X GET http://localhost:3001/api/wallets \
  -H "Authorization: Bearer $SESSION_KEY"
```

## 🔐 Security Headers

### Required Headers
```bash
Authorization: Bearer <token>
Content-Type: application/json
```

### Optional Headers
```bash
X-Request-ID: unique-request-id
X-Client-Version: 1.0.0
User-Agent: YourApp/1.0.0
```

## 📊 Rate Limits

### Default Limits
- **Authentication**: 5 requests per minute
- **Wallet Operations**: 100 requests per minute
- **Analytics**: 1000 requests per minute
- **General API**: 1000 requests per minute

### Rate Limit Headers
```bash
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

---

**📚 For more details, see the full [Deployment Guide](./DEPLOYMENT_GUIDE.md)**
