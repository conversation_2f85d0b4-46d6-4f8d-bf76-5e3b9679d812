# Cookie-Based Authentication

**Last Updated**: August 8, 2025 at 05:13 AM IST (GMT+5:30)  
**Status**: ✅ **COMPLETED**  
**Implementation**: HTTP-only cookies with automatic token refresh

---

## Overview

This implementation provides secure authentication using HTTP-only cookies instead of localStorage, offering better security and simplified frontend code.

## Key Features

### Security Improvements
- **HTTP-only cookies**: Protected from XSS attacks
- **Automatic expiration**: Cookies expire automatically
- **Secure flags**: HTTPS-only in production
- **SameSite protection**: CSRF protection
- **Backend controlled**: No client-side token management

### Simplified Frontend
- **No localStorage**: No manual token storage
- **Automatic headers**: Cookie handling by browser
- **Simplified API client**: Removed token management complexity
- **Automatic refresh**: Axios interceptor handles token refresh

## Implementation

### Backend Configuration

```typescript
// Set secure HTTP-only cookies
setTokenCookies(res: Response, tokens: TokenPair): void {
  res.cookie('accessToken', tokens.accessToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 15 * 60 * 1000, // 15 minutes
    path: '/'
  });

  res.cookie('refreshToken', tokens.refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    path: '/'
  });
}
```

### Frontend Configuration

```typescript
// Simplified API client
export class ApiClient {
  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL,
      withCredentials: true, // Important for cookies
    });

    // Automatic token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401 && !error.config._retry) {
          error.config._retry = true;
          try {
            await this.client.post('/auth/refresh');
            return this.client(error.config);
          } catch (refreshError) {
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }
        return Promise.reject(error);
      }
    );
  }
}
```

## Security Features

### Cookie Security
```typescript
{
  httpOnly: true,        // JavaScript can't access
  secure: true,          // HTTPS only in production
  sameSite: 'strict',    // CSRF protection
  maxAge: 15 * 60 * 1000, // Automatic expiration
  path: '/'              // Cookie scope
}
```

### XSS Protection
```javascript
// This won't work with httpOnly cookies
document.cookie = "accessToken=stolen"; // ❌ Can't access
localStorage.getItem('accessToken');     // ❌ No localStorage needed
```

## Benefits

### Better Security
- **XSS Protection**: Tokens protected from JavaScript access
- **Automatic Expiration**: No manual token cleanup needed
- **CSRF Protection**: SameSite cookies prevent CSRF attacks
- **Backend Control**: Server manages all token lifecycle

### Simpler Frontend
- **No Token Management**: No localStorage or manual headers
- **Automatic Refresh**: Axios interceptor handles token refresh
- **Cleaner Code**: Removed complex token management logic
- **Better UX**: Seamless authentication experience

## Configuration

### Environment Variables
```bash
JWT_SECRET=your-secure-jwt-secret
NODE_ENV=production  # Enables secure cookies
FRONTEND_URL=http://localhost:3000  # CORS origin
```

### CORS Setup
```typescript
app.use(cors({
  origin: allowedOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
```

## Testing

### Cookie Integration Tests
```typescript
it('should set secure cookies on login', async () => {
  const response = await request(app)
    .post('/login')
    .send({ email: '<EMAIL>', password: 'password123' })
    .expect(200);

  const cookies = response.headers['set-cookie'];
  expect(cookies).toHaveLength(2);
  expect(cookies[0]).toContain('HttpOnly');
  expect(cookies[0]).toContain('SameSite=Strict');
});
```

## Migration Guide

### From localStorage to Cookies

#### Before (localStorage)
```typescript
// Frontend token management
localStorage.setItem('accessToken', token);
const token = localStorage.getItem('accessToken');
localStorage.removeItem('accessToken');

// Manual headers
headers: { 'Authorization': `Bearer ${token}` }
```

#### After (Cookies)
```typescript
// Backend cookie management
authService.setTokenCookies(res, tokens);
const { accessToken } = authService.getTokensFromCookies(req);
authService.clearTokenCookies(res);

// Automatic cookie handling
withCredentials: true
```

## Best Practices

### ✅ Do
- Use HTTP-only cookies for tokens
- Set secure flags in production
- Use SameSite=strict for CSRF protection
- Implement automatic token refresh
- Clear cookies on logout

### ❌ Don't
- Store tokens in localStorage
- Send tokens in response bodies
- Use client-side token management
- Skip cookie security flags
- Forget to handle token expiration

## Conclusion

This cookie-based authentication implementation provides:

1. **Enhanced Security**: HTTP-only cookies protect against XSS
2. **Simplified Frontend**: No manual token management
3. **Better UX**: Seamless authentication experience
4. **Production Ready**: Proper security configurations
5. **Comprehensive Testing**: Full test coverage

The implementation is now more secure, simpler to maintain, and provides a better user experience while following modern web security best practices.