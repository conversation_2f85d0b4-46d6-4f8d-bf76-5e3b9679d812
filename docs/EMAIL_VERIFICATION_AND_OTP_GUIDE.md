# Email Verification & OTP Authentication Guide

**Last Updated**: August 8, 2025 at 05:13 AM IST (GMT+5:30)  
**Version**: 1.0.0  
**Status**: ✅ Complete Implementation

---

## Overview

This guide covers the complete implementation of email verification and OTP (One-Time Password) authentication in Tokai. The system supports both email verification for new user registration and OTP-based login for existing users.

### Features Implemented

- ✅ **Email Verification**: Secure token-based email verification for new registrations
- ✅ **OTP Authentication**: 6-digit OTP login system for existing users
- ✅ **App-Level Configuration**: Per-app authentication type settings
- ✅ **Nodemailer Integration**: Production-ready email delivery
- ✅ **OTP Generator**: Secure OTP generation using otp-generator library
- ✅ **Database Storage**: Secure storage of tokens and OTPs with expiration
- ✅ **Automatic Cleanup**: Scheduled cleanup of expired tokens
- ✅ **Rate Limiting**: Protection against abuse
- ✅ **Beautiful Email Templates**: Professional HTML and text email templates

---

## Quick Start

### Environment Configuration

Add these variables to your `.env` file:

```bash
# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_VERIFICATION_REQUIRED=false

# Frontend URL (for verification links)
FRONTEND_URL=http://localhost:3000
```

### Install Dependencies

```bash
cd dashboard/backend
bun add nodemailer @types/nodemailer otp-generator
```

### Test the Setup

```bash
# Start the backend
bun run dev

# Test email verification
curl -X POST http://localhost:3001/api/auth/send-verification-email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'

# Test OTP login
curl -X POST http://localhost:3001/api/auth/send-otp \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

---

## API Endpoints

### Email Verification

#### POST /api/auth/send-verification-email
Send verification email to user.

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Verification email sent"
}
```

#### POST /api/auth/verify-email
Verify email with token.

**Request:**
```json
{
  "token": "verification-token-from-email"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Email verified successfully",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>"
  }
}
```

### OTP Authentication

#### POST /api/auth/send-otp
Send OTP to user's email.

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "OTP sent to your email"
}
```

#### POST /api/auth/verify-otp
Verify OTP and login user.

**Request:**
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "full_name": "User Name",
    "created_at": "2024-12-19T10:30:00Z"
  }
}
```

---

## Configuration Options

### Authentication Types

You can configure the authentication type per app:

- **`password`**: Traditional email/password login
- **`otp`**: Email OTP-based login only
- **`both`**: Both password and OTP options available

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `EMAIL_HOST` | SMTP server hostname | Required |
| `EMAIL_PORT` | SMTP server port | Required |
| `EMAIL_SECURE` | Use SSL/TLS | `false` |
| `EMAIL_USER` | SMTP username | Required |
| `EMAIL_PASS` | SMTP password | Required |
| `EMAIL_FROM` | From email address | `EMAIL_USER` |
| `EMAIL_VERIFICATION_REQUIRED` | Require email verification | `false` |
| `FRONTEND_URL` | Frontend URL for verification links | `http://localhost:3000` |

---

## Database Schema

### New Tables Added

#### email_verification_tokens
```sql
CREATE TABLE email_verification_tokens (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  email TEXT NOT NULL,
  token TEXT UNIQUE NOT NULL,
  expires_at DATETIME NOT NULL,
  used BOOLEAN DEFAULT 0,
  verified_at DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);
```

#### email_otps
```sql
CREATE TABLE email_otps (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  email TEXT NOT NULL,
  otp TEXT NOT NULL,
  expires_at DATETIME NOT NULL,
  used BOOLEAN DEFAULT 0,
  verified_at DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);
```

### Updated Tables

#### users
Added email verification fields:
```sql
ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT 0;
ALTER TABLE users ADD COLUMN email_verified_at DATETIME;
```

---

## Security Features

### Token Security
- **Verification tokens**: 32-character hexadecimal strings
- **OTP codes**: 6-digit numeric codes
- **Expiration**: 24 hours for verification, 10 minutes for OTP
- **One-time use**: Tokens and OTPs are marked as used after verification
- **Rate limiting**: Protection against brute force attacks

### Email Security
- **SMTP authentication**: Secure email delivery
- **HTML sanitization**: Safe email content
- **Secure links**: HTTPS verification URLs
- **CSRF protection**: Token-based verification

---

## Testing

### Run Tests
```bash
cd dashboard/backend
bun test src/tests/emailService.test.ts
```

### Test Coverage
- ✅ OTP generation and validation
- ✅ Email verification token handling
- ✅ Database operations
- ✅ Expiration handling
- ✅ Cleanup operations
- ✅ Configuration detection

---

## Production Deployment

### Email Provider Setup

#### Gmail (Recommended for Development)
```bash
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

#### SendGrid (Recommended for Production)
```bash
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=apikey
EMAIL_PASS=your-sendgrid-api-key
```

#### AWS SES
```bash
EMAIL_HOST=email-smtp.us-east-1.amazonaws.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=your-ses-smtp-username
EMAIL_PASS=your-ses-smtp-password
```

### Environment Configuration
```bash
# Production settings
NODE_ENV=production
EMAIL_VERIFICATION_REQUIRED=true
FRONTEND_URL=https://yourdomain.com
```

---

## Frontend Integration

### React SDK Integration

The email verification and OTP system integrates seamlessly with the existing React SDK:

```typescript
import { useAuth } from '@tokai/react';

const { sendVerificationEmail, sendOTP, verifyOTP } = useAuth();

// Send verification email
await sendVerificationEmail('<EMAIL>');

// Send OTP
await sendOTP('<EMAIL>');

// Verify OTP
await verifyOTP('<EMAIL>', '123456');
```

### App Configuration

Apps can configure their authentication type:

```typescript
// Get app auth config
const authConfig = await getAppAuthConfig(appId);

if (authConfig.auth_type === 'otp') {
  // Show OTP login form
} else if (authConfig.auth_type === 'both') {
  // Show both password and OTP options
} else {
  // Show password login form
}
```

---

## Troubleshooting

### Common Issues

#### Email Not Sending
1. Check SMTP configuration
2. Verify email credentials
3. Check firewall/network settings
4. Review email provider limits

#### Verification Links Not Working
1. Verify `FRONTEND_URL` environment variable
2. Check frontend route handling
3. Ensure HTTPS in production
4. Verify token expiration

#### OTP Not Received
1. Check spam folder
2. Verify email address
3. Check rate limiting
4. Review email provider logs

### Debug Commands

```bash
# Test email configuration
curl -X POST http://localhost:3001/api/auth/send-verification-email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'

# Check email service status
curl http://localhost:3001/health

# View logs
tail -f logs/app.log
```

---

## Summary

The email verification and OTP system provides:

- ✅ **Complete email verification flow**
- ✅ **Secure OTP-based authentication**
- ✅ **App-level configuration options**
- ✅ **Production-ready email delivery**
- ✅ **Comprehensive security features**
- ✅ **Automatic cleanup and monitoring**
- ✅ **Beautiful email templates**
- ✅ **Full API documentation**
- ✅ **Comprehensive testing**
- ✅ **Easy frontend integration**

This implementation provides a robust, secure, and user-friendly authentication system that can be configured per application to meet different security and user experience requirements.