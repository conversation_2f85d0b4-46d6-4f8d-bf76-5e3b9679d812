# 🚀 Tokai Deployment Guide

A comprehensive guide to deploy Tokai - your self-hosted wallet infrastructure platform.

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Architecture Overview](#architecture-overview)
- [Prerequisites](#prerequisites)
- [Development Setup](#development-setup)
- [Production Deployment](#production-deployment)
- [Configuration](#configuration)
- [API Endpoints](#api-endpoints)
- [Monitoring & Health Checks](#monitoring--health-checks)
- [Troubleshooting](#troubleshooting)
- [Backup & Recovery](#backup--recovery)

## ⚡ Quick Start

### 1. Clone and Setup
```bash
git clone https://github.com/your-org/tokai.git
cd tokai
```

### 2. Deploy with Docker
```bash
# Setup environment and deploy
./deploy.sh prod

# Or manually
docker-compose up -d
```

### 3. Access Dashboard
- **Dashboard**: http://localhost:3000
- **API**: http://localhost:3001
- **Health Check**: http://localhost:3001/health

## 🏗️ Architecture Overview

Tokai uses a **single Docker image** that contains both frontend and backend:

```
┌─────────────────────────────────────┐
│           Tokai Container           │
├─────────────────────────────────────┤
│  Frontend (Next.js) - Port 3000    │
│  Backend (Express) - Port 3001     │
│  Database (SQLite) - /app/data     │
│  Logs - /app/logs                  │
└─────────────────────────────────────┘
```

### Services Included:
- **Dashboard Frontend**: React/Next.js admin interface
- **Backend API**: Express.js REST API
- **Database**: SQLite (production) or PostgreSQL (optional)
- **Redis**: Caching and session storage
- **Nginx**: Reverse proxy and load balancer
- **Monitoring**: Prometheus + Grafana (optional)

## 📋 Prerequisites

### Required Software
- **Docker** (v20.10+)
- **Docker Compose** (v2.0+)
- **Git**

### Optional Software
- **PostgreSQL** (for production database)
- **Redis** (for caching)
- **Nginx** (for reverse proxy)

### System Requirements
- **CPU**: 2+ cores
- **RAM**: 4GB+ (8GB recommended)
- **Storage**: 20GB+ available space
- **Network**: Internet access for OAuth providers

## 🛠️ Development Setup

### 1. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

### 2. Start Development Environment
```bash
# Using deployment script
./deploy.sh dev

# Or manually
docker-compose -f docker-compose.dev.yml up -d
```

### 3. Development URLs
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Docs**: http://localhost:3001/docs

## 🚀 Production Deployment

### 1. Environment Setup
```bash
# Generate secure keys
JWT_SECRET=$(openssl rand -hex 32)
WALLET_ENCRYPTION_KEY=$(openssl rand -hex 32)

# Create production .env
cat > .env << EOF
# Security
JWT_SECRET=$JWT_SECRET
WALLET_ENCRYPTION_KEY=$WALLET_ENCRYPTION_KEY

# Database
DATABASE_URL=file:/app/data/wallet_service.db

# URLs
BASE_URL=https://your-domain.com
FRONTEND_URL=https://your-domain.com

# OAuth Providers (configure as needed)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
DISCORD_CLIENT_ID=your-discord-client-id
DISCORD_CLIENT_SECRET=your-discord-client-secret
TWITTER_CLIENT_ID=your-twitter-client-id
TWITTER_CLIENT_SECRET=your-twitter-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# AWS (for SMS)
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_REGION=us-east-1

# Redis
REDIS_PASSWORD=your-redis-password

# PostgreSQL (optional)
POSTGRES_USER=tokai
POSTGRES_PASSWORD=your-postgres-password
POSTGRES_DB=tokai

# Monitoring
GRAFANA_PASSWORD=your-grafana-password
EOF
```

### 2. Deploy Production
```bash
# Deploy with monitoring
./deploy.sh prod

# Or deploy specific services
docker-compose up -d tokai-backend redis nginx
```

### 3. Production URLs
- **Dashboard**: https://your-domain.com
- **API**: https://your-domain.com/api
- **Health**: https://your-domain.com/health
- **Monitoring**: https://your-domain.com:9090 (Prometheus)

## ⚙️ Configuration

### Environment Variables

#### Security
```bash
JWT_SECRET=your-super-secret-jwt-key
WALLET_ENCRYPTION_KEY=your-wallet-encryption-key
```

#### Database
```bash
# SQLite (default)
DATABASE_URL=file:/app/data/wallet_service.db

# PostgreSQL (production)
DATABASE_URL=************************************/tokai
```

#### OAuth Providers
```bash
# Google
GOOGLE_CLIENT_ID=your-client-id
GOOGLE_CLIENT_SECRET=your-client-secret

# Discord
DISCORD_CLIENT_ID=your-client-id
DISCORD_CLIENT_SECRET=your-client-secret

# Twitter
TWITTER_CLIENT_ID=your-client-id
TWITTER_CLIENT_SECRET=your-client-secret

# GitHub
GITHUB_CLIENT_ID=your-client-id
GITHUB_CLIENT_SECRET=your-client-secret
```

#### Feature Flags
```bash
# Auto wallet creation
AUTO_CREATE_WALLET=true
DEFAULT_WALLET_NETWORK=ethereum

# Email verification
EMAIL_VERIFICATION_REQUIRED=true
```

## 🔌 API Endpoints

### Base URL
```
Production: https://your-domain.com/api
Development: http://localhost:3001
```

### Authentication Endpoints
```bash
# Register
POST /api/auth/register
{
  "email": "<EMAIL>",
  "password": "secure-password",
  "fullName": "John Doe"
}

# Login
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "secure-password"
}

# OAuth Login
GET /api/auth/google
GET /api/auth/discord
GET /api/auth/twitter
GET /api/auth/github
```

### Wallet Management
```bash
# Get user wallets
GET /api/wallets
Authorization: Bearer <token>

# Create wallet
POST /api/wallets
{
  "network": "ethereum"
}

# Get wallet details
GET /api/wallets/{walletId}

# Get wallet balance
GET /api/wallets/{walletId}/balance

# Send transaction
POST /api/wallets/{walletId}/send
{
  "to": "0x...",
  "amount": "0.1",
  "network": "ethereum"
}
```

### Embedded Wallets
```bash
# Create embedded wallet
POST /api/wallets/embedded
{
  "network": "ethereum",
  "recoveryMethod": "email"
}

# Deploy embedded wallet
POST /api/wallets/embedded/{walletId}/deploy

# Recover embedded wallet
POST /api/wallets/embedded/recover
{
  "recoveryCode": "your-recovery-code"
}
```

### Session Keys
```bash
# Create session key
POST /api/session-keys
{
  "name": "Trading Bot",
  "permissions": ["read", "send"],
  "expiryHours": 24
}

# List session keys
GET /api/session-keys

# Revoke session key
DELETE /api/session-keys/{keyId}
```

### Analytics
```bash
# Track event
POST /api/analytics/track
{
  "event": "wallet_created",
  "properties": {
    "network": "ethereum",
    "walletType": "embedded"
  }
}

# Get analytics
GET /api/analytics/events
GET /api/analytics/users
GET /api/analytics/transactions
```

### Health & Status
```bash
# Health check
GET /health

# API status
GET /api/status

# Database status
GET /api/status/database
```

## 📊 Monitoring & Health Checks

### Health Check Endpoints
```bash
# Overall health
curl http://localhost:3001/health

# Database health
curl http://localhost:3001/api/status/database

# Redis health
curl http://localhost:3001/api/status/redis
```

### Monitoring Dashboard
```bash
# Prometheus metrics
http://localhost:9090

# Grafana dashboard
http://localhost:3001 (Grafana port)
Username: admin
Password: admin (or GRAFANA_PASSWORD)
```

### Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f tokai-backend
docker-compose logs -f tokai-frontend

# View logs from deployment script
./deploy.sh logs
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the port
sudo lsof -i :3001
sudo lsof -i :3000

# Kill process or change ports in docker-compose.yml
```

#### 2. Database Connection Issues
```bash
# Check database status
docker-compose exec tokai-backend bun run check-db

# Reset database (WARNING: loses data)
docker-compose down -v
docker-compose up -d
```

#### 3. OAuth Provider Issues
```bash
# Check OAuth configuration
curl http://localhost:3001/api/auth/config

# Verify environment variables
docker-compose exec tokai-backend env | grep -i oauth
```

#### 4. Wallet Creation Fails
```bash
# Check wallet service logs
docker-compose logs -f tokai-backend | grep -i wallet

# Verify encryption key
docker-compose exec tokai-backend echo $WALLET_ENCRYPTION_KEY
```

### Debug Commands
```bash
# Enter container
docker-compose exec tokai-backend sh

# Check application status
docker-compose exec tokai-backend bun run status

# View application logs
docker-compose exec tokai-backend tail -f /app/logs/combined.log

# Check database
docker-compose exec tokai-backend sqlite3 /app/data/wallet_service.db ".tables"
```

## 💾 Backup & Recovery

### Backup Database
```bash
# Create backup
./deploy.sh backup

# Manual backup
docker-compose exec tokai-backend sqlite3 /app/data/wallet_service.db ".backup /app/backup/backup-$(date +%Y%m%d).db"

# Backup with compression
docker-compose exec tokai-backend tar -czf /app/backup/tokai-$(date +%Y%m%d).tar.gz /app/data
```

### Restore Database
```bash
# Restore from backup
./deploy.sh restore

# Manual restore
docker-compose stop tokai-backend
docker-compose exec tokai-backend sqlite3 /app/data/wallet_service.db ".restore /app/backup/backup-20231201.db"
docker-compose start tokai-backend
```

### Backup Configuration
```bash
# Backup environment and config
tar -czf tokai-config-$(date +%Y%m%d).tar.gz .env docker-compose.yml docker/

# Restore configuration
tar -xzf tokai-config-20231201.tar.gz
```

## 🔐 Security Best Practices

### 1. Environment Security
```bash
# Use strong secrets
JWT_SECRET=$(openssl rand -hex 64)
WALLET_ENCRYPTION_KEY=$(openssl rand -hex 64)

# Restrict file permissions
chmod 600 .env
chmod 600 docker-compose.yml
```

### 2. Network Security
```bash
# Use HTTPS in production
# Configure SSL certificates
# Set up firewall rules
# Use VPN for admin access
```

### 3. Database Security
```bash
# Use PostgreSQL in production
# Enable SSL connections
# Regular backups
# Monitor access logs
```

### 4. Application Security
```bash
# Keep dependencies updated
# Regular security audits
# Monitor application logs
# Implement rate limiting
```

## 📞 Support

### Getting Help
- **Documentation**: Check this guide and other docs in `/docs`
- **Issues**: Create an issue on GitHub
- **Discord**: Join our community server
- **Email**: <EMAIL>

### Useful Commands
```bash
# Check system status
./deploy.sh status

# View logs
./deploy.sh logs

# Restart services
./deploy.sh restart

# Clean up
./deploy.sh clean

# Update deployment
git pull
./deploy.sh prod --build
```

---

**🎉 Congratulations!** Your Tokai deployment is now ready to handle wallet infrastructure at scale.
