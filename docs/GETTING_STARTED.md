# Getting Started with Tokai

**Last Updated**: August 8, 2025 at 05:13 AM IST (GMT+5:30)  
**Status**: Ready for Development

---

## Overview

Tokai is a comprehensive **wallet infrastructure SDK** that provides seamless Web3 onboarding with social authentication and embedded wallets.

## What's Implemented

### Authentication Components
- **TokaiAuth** - Authentication UI with social logins
- **TokaiConnectButton** - One-click connect button with user menu
- **Social provider support** - Google, Discord, Twitter, GitHub, Telegram, Farcaster
- **Progressive onboarding** - Seamless user experience

### SDK Architecture
- **TypeScript interfaces** for embedded wallets and social auth
- **Extended API client** with social authentication methods
- **React hooks** (`useAuth`, `useDashboard`, `useAppDetails`, `useSessionKeys`) for state
- **Theme customization** system with dark/light modes

### Modern UI/UX
- **Responsive design** with mobile-first approach
- **Custom theming** with brand colors and logos
- **Interactive demo** application
- **Professional styling**

---

## Quick Start

### Backend Setup
Your backend is running at `http://localhost:3001`
- Health check: `http://localhost:3001/health`
- API endpoints ready for social auth implementation

### Test the Demo
```bash
# Open in browser
open /workspace/test-app/index.html
# or navigate to file:///workspace/test-app/index.html
```

### Use in Your React App
```bash
# Install the package
pnpm add @tokai/react

# Or use locally
cd /workspace/packages/react
pnpm link --global
```

```tsx
import React from 'react';
import { TokaiProvider, TokaiConnectButton } from '@tokai/react';

function App() {
  const config = {
    apiUrl: 'http://localhost:3001/api',
    apiKey: 'your-api-key',
    appName: 'My DApp',
    embeddedWallets: true,
    socialLogins: [
      { id: 'google', enabled: true },
      { id: 'discord', enabled: true },
      { id: 'twitter', enabled: true },
      { id: 'github', enabled: true },
      { id: 'telegram', enabled: true },
      { id: 'farcaster', enabled: true },
    ],
  };

  return (
    <TokaiProvider config={config}>
      <TokaiConnectButton />
    </TokaiProvider>
  );
}
```

---

## Implementation Phases

### Phase 1: Backend Implementation ✅ COMPLETED

1. **Social OAuth Endpoints** - All implemented and working:
   ```bash
   # OAuth flow endpoints
   POST /api/auth/social/initiate
   GET  /api/auth/social/callback
   POST /api/auth/social/link
   DELETE /api/auth/social/unlink/:provider
   
   # Telegram-specific endpoint
   POST /api/auth/telegram/callback
   ```

2. **Set Up OAuth Applications**
   - Google: [Google Cloud Console](https://console.cloud.google.com/)
   - Discord: [Discord Developer Portal](https://discord.com/developers/applications)
   - Twitter: [Twitter Developer Portal](https://developer.twitter.com/)
   - GitHub: [GitHub OAuth Apps](https://github.com/settings/applications/new)
   - Telegram: Create a bot with [@BotFather](https://t.me/botfather) on Telegram
   - Farcaster: [Warpcast Developer Portal](https://warpcast.com/~/developers)

3. **Add Environment Variables**
   ```bash
   # Add to your backend .env file
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   DISCORD_CLIENT_ID=your_discord_client_id
   DISCORD_CLIENT_SECRET=your_discord_client_secret
   TWITTER_CLIENT_ID=your_twitter_client_id
   TWITTER_CLIENT_SECRET=your_twitter_client_secret
   GITHUB_CLIENT_ID=your_github_client_id
   GITHUB_CLIENT_SECRET=your_github_client_secret
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token
   TELEGRAM_BOT_USERNAME=your_bot_username
   FARCASTER_CLIENT_ID=your_farcaster_client_id
   FARCASTER_CLIENT_SECRET=your_farcaster_client_secret
   
   # Add to your frontend .env file
   NEXT_PUBLIC_TELEGRAM_BOT_USERNAME=your_bot_username
   ```

### Phase 2: Embedded Wallets

1. **Database Schema Updates**
   ```sql
   -- Add to your database
   ALTER TABLE users ADD COLUMN avatar_url VARCHAR(255);
   ALTER TABLE users ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;
   ALTER TABLE users ADD COLUMN global_user_id VARCHAR(255);
   
   CREATE TABLE social_accounts (
     id VARCHAR(255) PRIMARY KEY,
     user_id VARCHAR(255) REFERENCES users(id),
     provider VARCHAR(50) NOT NULL,
     provider_id VARCHAR(255) NOT NULL,
     email VARCHAR(255),
     name VARCHAR(255),
     avatar_url VARCHAR(255),
     verified BOOLEAN DEFAULT FALSE,
     connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   
   CREATE TABLE embedded_wallets (
     id VARCHAR(255) PRIMARY KEY,
     user_id VARCHAR(255) REFERENCES users(id),
     type VARCHAR(50) DEFAULT 'smart_account',
     address VARCHAR(255) UNIQUE NOT NULL,
     recovery_method VARCHAR(50) DEFAULT 'social',
     is_deployed BOOLEAN DEFAULT FALSE,
     supported_chains JSON,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

2. **Smart Account Integration**
   - Implement Account Abstraction (ERC-4337)
   - Set up bundler and paymaster services
   - Add gasless transaction capabilities

### Phase 3: Advanced Features

1. **Cross-App Identity**
   - Implement global user ID system
   - Add wallet portability across apps
   - Create identity verification system

2. **Enhanced Security**
   - Add social recovery mechanisms
   - Implement multi-factor authentication
   - Add biometric authentication support

---

## Testing

### Component Testing
```tsx
// Test the components directly
import { TokaiAuth, TokaiConnectButton } from '@tokai/react';

// Test different configurations
const configs = [
  { theme: 'light', variant: 'primary' },
  { theme: 'dark', variant: 'secondary' },
  { theme: 'auto', variant: 'outline' },
];
```

### API Testing
```bash
# Test backend endpoints
curl http://localhost:3001/health
curl http://localhost:3001/api/auth/me

# Test with different social providers
curl -X POST http://localhost:3001/api/auth/social/initiate \
  -H "Content-Type: application/json" \
  -d '{"provider": "google", "redirectUrl": "http://localhost:3000"}'
```

### Integration Testing
```bash
# Build and test the React package
cd /workspace/packages/react
pnpm build
pnpm test  # If you add tests

# Test in sandbox environment
cd /workspace/sandbox
pnpm dev
```

---

## Integration Examples

### Simple Integration
```tsx
import { TokaiConnectButton } from '@tokai/react';

<TokaiConnectButton 
  appName="My DApp"
  embeddedWallets={true}
/>
```

### Advanced Integration
```tsx
import { TokaiProvider, TokaiAuth, useTokai } from '@tokai/react';

function MyApp() {
  const { user, wallets, isLoading } = useTokai();
  
  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      {user ? (
        <div>
          <h1>Welcome {user.full_name}!</h1>
          <p>Wallets: {wallets.length}</p>
          {user.embedded_wallet && (
            <p>Embedded Wallet: {user.embedded_wallet.address}</p>
          )}
        </div>
      ) : (
        <TokaiAuth embeddedWallets={true} />
      )}
    </div>
  );
}
```

---

## Key Benefits

### For Users
- ✅ **No seed phrases** - Social login replaces complex onboarding
- ✅ **Instant wallets** - Embedded wallets created automatically
- ✅ **Cross-app identity** - Same wallet across different apps
- ✅ **Social recovery** - Recover access through social accounts

### For Developers
- ✅ **Easy integration** - Drop-in React components
- ✅ **TypeScript support** - Full type safety
- ✅ **Customizable UI** - Match your brand
- ✅ **Comprehensive API** - All wallet functions covered

### Comparison with Traditional Wallets
| Traditional | Tokai |
|-------------|-------|
| 12-24 word seed phrases | Social login |
| Manual wallet installation | Automatic embedded wallets |
| Complex onboarding | One-click signup |
| App-specific wallets | Cross-app identity |
| User manages security | Social recovery |

---

## Troubleshooting

### Common Issues
1. **"Cannot find module @tokai/react"**
   - Run `pnpm build` in `/workspace/packages/react`
   - Use `pnpm link` for local development

2. **"Backend not responding"**
   - Check if backend is running: `curl http://localhost:3001/health`
   - Restart: `cd /workspace/dashboard/backend && bun run dev`

3. **"Social login not working"**
   - Implement OAuth endpoints in backend
   - Add OAuth application credentials

### Development Commands
```bash
# Build all packages
cd /workspace && pnpm build

# Start backend
cd /workspace/dashboard/backend && bun run dev

# Start frontend
cd /workspace/dashboard/frontend && pnpm dev

# Build React package
cd /workspace/packages/react && pnpm build

# Test demo
open /workspace/test-app/index.html
```

---

## Conclusion

Your **Tokai setup** is now ready for development! You have:

✅ **Modern UI components**  
✅ **Comprehensive SDK** with TypeScript support  
✅ **Social authentication framework** ready for OAuth  
✅ **Embedded wallet interfaces** prepared for smart accounts  
✅ **Professional documentation** and examples  

**Next steps:** Implement the backend OAuth endpoints and start building amazing Web3 experiences with seamless onboarding!