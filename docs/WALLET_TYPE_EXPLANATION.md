# Wallet Type Parameter Guide

**Last Updated**: August 8, 2025 at 05:13 AM IST (GMT+5:30)  
**Status**: Complete Documentation

---

## Overview

The `useWallet` hook is designed to be flexible and user-friendly. Here's how the `walletType` parameter works:

## Three Ways to Use `useWallet`

### 1. No `walletType` - Uses Default
```tsx
// ✅ This works! Uses default wallet for the chain
const { connect } = useWallet('ethereum');

// When you call connect(), it uses the default wallet type
await connect(); // Automatically uses 'metamask' for Ethereum
```

### 2. Specify `walletType` at Hook Level
```tsx
// ✅ Specify wallet type when creating the hook
const { connect } = useWallet('ethereum', 'coinbase');

// When you call connect(), it uses the specified wallet type
await connect(); // Uses 'coinbase' for Ethereum
```

### 3. Specify `walletType` at Connect Time
```tsx
// ✅ Override wallet type when connecting
const { connect } = useWallet('ethereum');

// Override the wallet type when connecting
await connect('phantom'); // Uses 'phantom' instead of default
```

---

## Default Wallet Types by Chain

When you don't specify a `walletType`, the hook automatically uses these defaults:

| Chain | Default Wallet | Alternative Wallets |
|-------|---------------|-------------------|
| **Ethereum** | `metamask` | `coinbase` |
| **Solana** | `phantom` | `backpack`, `solflare` |
| **Polkadot** | `polkadotjs` | `subwallet`, `talisman` |
| **Algorand** | `pera` | `defly`, `exodus` |
| **Fivire** | `5ire` | `metamask` |
| **Astar** | `subwallet` | `polkadotjs` |
| **Avalanche** | `core` | `metamask` |
| **Cardano** | `nami` | `eternl`, `flint` |
| **Cosmos** | `keplr` | `leap`, `cosmostation` |
| **NEAR** | `sender` | `meteor`, `here` |

---

## Real-World Examples

### Simple Usage (No walletType)
```tsx
import { useWallet } from '@tokai/react';

function SimpleWalletConnect() {
  const { connect, state, disconnect } = useWallet('ethereum');

  const handleConnect = async () => {
    try {
      // Automatically uses 'metamask' for Ethereum
      await connect();
      console.log('Connected with MetaMask!');
    } catch (error) {
      console.error('Connection failed:', error);
    }
  };

  return (
    <div>
      {state.isConnected ? (
        <div>
          <p>Connected: {state.account}</p>
          <button onClick={disconnect}>Disconnect</button>
        </div>
      ) : (
        <button onClick={handleConnect}>
          Connect MetaMask (Default)
        </button>
      )}
    </div>
  );
}
```

### Multi-Wallet Support
```tsx
import { useWallet } from '@tokai/react';

function MultiWalletConnect() {
  const { connect, state } = useWallet('ethereum');

  const connectMetaMask = async () => {
    await connect('metamask');
  };

  const connectCoinbase = async () => {
    await connect('coinbase');
  };

  return (
    <div>
      <button onClick={connectMetaMask}>Connect MetaMask</button>
      <button onClick={connectCoinbase}>Connect Coinbase</button>
      
      {state.isConnected && (
        <p>Connected with: {state.walletType}</p>
      )}
    </div>
  );
}
```

### Chain-Specific Defaults
```tsx
import { useWallet } from '@tokai/react';

function ChainSpecificConnect() {
  // Each chain uses its default wallet
  const ethereumWallet = useWallet('ethereum'); // Default: MetaMask
  const solanaWallet = useWallet('solana');     // Default: Phantom
  const polkadotWallet = useWallet('polkadot'); // Default: Polkadot.js

  const connectAll = async () => {
    await ethereumWallet.connect(); // Uses MetaMask
    await solanaWallet.connect();   // Uses Phantom
    await polkadotWallet.connect(); // Uses Polkadot.js
  };

  return (
    <button onClick={connectAll}>
      Connect All Chains (Default Wallets)
    </button>
  );
}
```

---

## How It Works Internally

### The Logic Flow
```tsx
const connect = useCallback(async (walletTypeToConnect?: string) => {
  // 1. Check if chain is provided
  if (!chain) {
    throw new Error('Chain is required for wallet connection');
  }

  // 2. Determine which wallet type to use
  let targetWalletType: string;
  
  if (walletTypeToConnect) {
    // Use the explicitly passed wallet type
    targetWalletType = walletTypeToConnect;
  } else if (walletType) {
    // Use the wallet type from the hook initialization
    targetWalletType = walletType;
  } else {
    // Use the default wallet type for this chain
    targetWalletType = getDefaultWalletType(chain);
    console.log(`No wallet type specified, using default for ${chain}: ${targetWalletType}`);
  }

  // 3. Connect using the determined wallet type
  const walletManager = await getWalletManager(chain);
  await walletManager.connectWallet(targetWalletType);
}, [chain, walletType, getWalletManager]);
```

### Default Wallet Type Function
```tsx
const getDefaultWalletType = (chain: Chain): string => {
  const defaults: Record<Chain, string> = {
    ethereum: 'metamask',
    solana: 'phantom',
    polkadot: 'polkadotjs',
    algorand: 'pera',
    fivire: '5ire',
    astar: 'subwallet',
    avalanche: 'core',
    cardano: 'nami',
    cosmos: 'keplr',
    near: 'sender'
  };
  return defaults[chain] || 'metamask';
};
```

---

## Error Handling

### What happens if the default wallet isn't available?
```tsx
const { connect, error } = useWallet('ethereum');

const handleConnect = async () => {
  try {
    await connect(); // Tries MetaMask first
  } catch (error) {
    // MetaMask not available, try alternative
    try {
      await connect('coinbase');
    } catch (secondError) {
      console.error('No wallets available');
    }
  }
};
```

### Graceful Fallbacks
```tsx
const { connect } = useWallet('ethereum');

const connectWithFallback = async () => {
  const walletTypes = ['metamask', 'coinbase'];
  
  for (const walletType of walletTypes) {
    try {
      await connect(walletType);
      console.log(`Connected with ${walletType}`);
      break;
    } catch (error) {
      console.log(`${walletType} not available, trying next...`);
    }
  }
};
```

---

## Best Practices

### 1. Use Defaults for Simple Cases
```tsx
// ✅ Good: Use defaults for simple wallet connections
const { connect } = useWallet('ethereum');
await connect();
```

### 2. Specify Wallet Type for Complex Apps
```tsx
// ✅ Good: Specify wallet type for apps with specific requirements
const { connect } = useWallet('ethereum', 'coinbase');
await connect();
```

### 3. Allow User Choice
```tsx
// ✅ Good: Let users choose their preferred wallet
const { connect } = useWallet('ethereum');

const connectUserWallet = async (userChoice: string) => {
  await connect(userChoice);
};
```

### 4. Handle Multiple Wallets
```tsx
// ✅ Good: Support multiple wallet types
const { connect } = useWallet('ethereum');

const walletOptions = [
  { name: 'MetaMask', type: 'metamask' },
  { name: 'Coinbase', type: 'coinbase' },

];

return (
  <div>
    {walletOptions.map(option => (
      <button key={option.type} onClick={() => connect(option.type)}>
        Connect {option.name}
      </button>
    ))}
  </div>
);
```

---

## Summary

The `walletType` parameter is **optional** and designed to make the SDK as user-friendly as possible:

- **No `walletType`**: Uses the most popular wallet for that chain
- **With `walletType`**: Uses your specified wallet
- **Flexible**: Can override at connection time
- **Chain-aware**: Each chain has appropriate defaults
- **Error-resistant**: Graceful handling of unavailable wallets

This design makes it easy for developers to get started quickly while still providing the flexibility needed for complex applications!