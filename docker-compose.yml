version: '3.8'

services:
  # Tokai Backend API
  tokai-backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: tokai-backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DATABASE_URL=file:/app/data/wallet_service.db
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      - WALLET_ENCRYPTION_KEY=${WALLET_ENCRYPTION_KEY:-your-wallet-encryption-key-change-this}
      - BASE_URL=${BASE_URL:-http://localhost:3001}
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:3000}
      
      # OAuth Configuration
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - DISCORD_CLIENT_ID=${DISCORD_CLIENT_ID}
      - DISCORD_CLIENT_SECRET=${DISCORD_CLIENT_SECRET}
      - TWITTER_CLIENT_ID=${TWITTER_CLIENT_ID}
      - TWITTER_CLIENT_SECRET=${TWITTER_CLIENT_SECRET}
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      
      
      
      # SMS Configuration
      - SMS_PROVIDER=${SMS_PROVIDER:-mock}
      
      # AWS SMS Configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      
      # Auto wallet creation
      - AUTO_CREATE_WALLET=${AUTO_CREATE_WALLET:-false}
      - DEFAULT_WALLET_NETWORK=${DEFAULT_WALLET_NETWORK:-ethereum}
    volumes:
      - tokai-data:/app/data
      - tokai-logs:/app/logs
    networks:
      - tokai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Tokai Frontend (optional - can be served separately)
  tokai-frontend:
    build:
      context: ./dashboard/frontend
      dockerfile: Dockerfile
    container_name: tokai-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:3001}
      - NEXT_PUBLIC_APP_NAME=${NEXT_PUBLIC_APP_NAME:-Tokai}
    depends_on:
      - tokai-backend
    networks:
      - tokai-network

  # Redis (for production caching and session storage)
  redis:
    image: redis:7-alpine
    container_name: tokai-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - tokai-network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-your-redis-password}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5

  # Nginx (reverse proxy and load balancer)
  nginx:
    image: nginx:alpine
    container_name: tokai-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    depends_on:
      - tokai-backend
      - tokai-frontend
    networks:
      - tokai-network

  # PostgreSQL (optional - for production database instead of SQLite)
  postgres:
    image: postgres:15-alpine
    container_name: tokai-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-tokai}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-your-postgres-password}
      - POSTGRES_DB=${POSTGRES_DB:-tokai}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - tokai-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-tokai}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: tokai-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - tokai-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana (optional - for monitoring dashboards)
  grafana:
    image: grafana/grafana:latest
    container_name: tokai-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"  # Note: Different port to avoid conflict
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
    networks:
      - tokai-network
    depends_on:
      - prometheus

volumes:
  tokai-data:
    driver: local
  tokai-logs:
    driver: local
  redis-data:
    driver: local
  postgres-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  nginx-logs:
    driver: local

networks:
  tokai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16 