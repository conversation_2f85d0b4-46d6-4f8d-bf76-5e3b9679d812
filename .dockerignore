# Node modules (will be installed in container)
node_modules/
*/node_modules/

# Build outputs
dist/
build/
.next/
*.tsbuildinfo

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# NYC test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment variables (for security)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database files
*.db
*.sqlite
*.sqlite3

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README.md
*.md
docs/

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Testing
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
jest.config.*
cypress/

# Temporary files
tmp/
temp/
.tmp/

# Package manager locks (except bun.lockb)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Development files
*.dev.*
dev/

# Backup files
*.backup
*.bak
*.orig

# Certificates and keys (for security)
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# Build tools
webpack.config.*
rollup.config.*
vite.config.*

# Linting
.eslintrc.*
.prettierrc.*
.stylelintrc.*

# Editor config
.editorconfig