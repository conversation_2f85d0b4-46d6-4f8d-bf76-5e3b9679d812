#!/bin/sh

# Tokai Self-Hosted Server Entrypoint
# Starts both backend (Bun) and frontend (Next.js) services

set -e

echo "🚀 Starting Tokai Self-Hosted Server..."

# Generate secure keys if not provided
if [ -z "$JWT_SECRET" ]; then
    echo "🔐 Generating JWT secret..."
    export JWT_SECRET=$(openssl rand -base64 32)
fi

if [ -z "$WALLET_ENCRYPTION_KEY" ]; then
    echo "🔐 Generating wallet encryption key..."
    export WALLET_ENCRYPTION_KEY=$(openssl rand -base64 32)
fi

# Set default environment variables
export NODE_ENV=${NODE_ENV:-production}
export DATABASE_PATH=${DATABASE_PATH:-/data/tokai.db}
export PORT_BACKEND=${PORT_BACKEND:-3001}
export PORT_FRONTEND=${PORT_FRONTEND:-3000}
export FRONTEND_URL=${FRONTEND_URL:-http://localhost:$PORT_FRONTEND}

echo "📊 Configuration:"
echo "  - Backend: http://localhost:$PORT_BACKEND"
echo "  - Frontend: http://localhost:$PORT_FRONTEND"
echo "  - Database: $DATABASE_PATH"

# Function to gracefully shutdown
shutdown() {
    echo "🛑 Shutting down services..."
    kill -TERM $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    wait $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    echo "✅ Services stopped"
    exit 0
}

# Trap signals for graceful shutdown
trap shutdown SIGTERM SIGINT

# Start backend service
echo "🔧 Starting backend service..."
cd /app/backend
PORT=$PORT_BACKEND bun run src/index.ts &
BACKEND_PID=$!

# Wait for backend to be ready
echo "⏳ Waiting for backend to start..."
timeout=30
while [ $timeout -gt 0 ]; do
    if curl -f http://localhost:$PORT_BACKEND/health >/dev/null 2>&1; then
        echo "✅ Backend is ready"
        break
    fi
    timeout=$((timeout - 1))
    sleep 1
done

if [ $timeout -eq 0 ]; then
    echo "❌ Backend failed to start"
    exit 1
fi

# Start frontend service
echo "🎨 Starting frontend service..."
cd /app/frontend
PORT=$PORT_FRONTEND npm start &
FRONTEND_PID=$!

# Wait for frontend to be ready
echo "⏳ Waiting for frontend to start..."
timeout=30
while [ $timeout -gt 0 ]; do
    if curl -f http://localhost:$PORT_FRONTEND >/dev/null 2>&1; then
        echo "✅ Frontend is ready"
        break
    fi
    timeout=$((timeout - 1))
    sleep 1
done

if [ $timeout -eq 0 ]; then
    echo "❌ Frontend failed to start"
    kill -TERM $BACKEND_PID
    exit 1
fi

echo ""
echo "🎉 Tokai Self-Hosted Server is running!"
echo ""
echo "📱 Services:"
echo "   Dashboard:    http://localhost:$PORT_FRONTEND"
echo "   API:          http://localhost:$PORT_BACKEND"
echo "   Health Check: http://localhost:$PORT_BACKEND/health"
echo ""
echo "🔧 Configuration:"
echo "   Database:     $DATABASE_PATH"
echo "   Environment:  $NODE_ENV"
echo ""
echo "📚 SDK Configuration:"
echo "   apiUrl: 'http://localhost:$PORT_BACKEND/api'"
echo "   appId: 'self-hosted' // Use this in your React app"
echo ""

# Wait for either service to exit
wait $BACKEND_PID $FRONTEND_PID