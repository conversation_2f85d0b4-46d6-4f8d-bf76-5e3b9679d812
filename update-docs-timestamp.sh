#!/bin/bash

# Update Documentation Timestamps
# Usage: ./update-docs-timestamp.sh [description] [pr-title]

TIMESTAMP=$(TZ='Asia/Kolkata' date '+%B %d, %Y at %I:%M %p IST')
DESCRIPTION=${1:-"Documentation update"}
PR_TITLE=${2:-"docs: Update documentation"}

echo "📚 Updating documentation timestamps to: $TIMESTAMP"
echo "📝 Description: $DESCRIPTION"
echo "🔗 PR Title: $PR_TITLE"

# List of documentation files to update
DOCS=(
    "AGENT_CONTEXT_INDEX.md"
    "TOKAI_DEVELOPMENT_CHECKLIST.md" 
    "NEXT_STEPS.md"
    "DEVELOPMENT_CONTEXT.md"
)

for doc in "${DOCS[@]}"; do
    if [ -f "$doc" ]; then
        # Update the "Last Updated" line with time
        sed -i "s/\*\*Last Updated\*\*:.*/\*\*Last Updated\*\*: $TIMESTAMP/" "$doc"
        
        # Add or update the "Last Change" line
        if grep -q "\*\*Last Change\*\*:" "$doc"; then
            sed -i "s/\*\*Last Change\*\*:.*/\*\*Last Change\*\*: $PR_TITLE/" "$doc"
        else
            sed -i "/\*\*Last Updated\*\*:.*/a\*\*Last Change\*\*: $PR_TITLE" "$doc"
        fi
        
        echo "✅ Updated: $doc"
    else
        echo "⚠️  Not found: $doc"
    fi
done

# Add changelog entry to AGENT_CONTEXT_INDEX.md if description provided
if [ "$1" != "" ]; then
    echo ""
    echo "📅 Adding changelog entry..."
    
    # Create changelog entry with PR info
    CHANGELOG_ENTRY="### **$TIMESTAMP**\n**PR**: \`$PR_TITLE\`\n- 📝 **$DESCRIPTION**\n"
    
    # Insert after the "## 📅 **DOCUMENTATION CHANGELOG**" line
    sed -i "/## 📅 \*\*DOCUMENTATION CHANGELOG\*\*/a\\
\\
$CHANGELOG_ENTRY" AGENT_CONTEXT_INDEX.md
    
    echo "✅ Added changelog entry to AGENT_CONTEXT_INDEX.md"
fi

echo ""
echo "🎉 Documentation timestamps updated successfully!"
echo "💡 Usage Examples:"
echo "   ./update-docs-timestamp.sh 'Feature completed' 'feat: Add new feature'"
echo "   ./update-docs-timestamp.sh 'Bug fixes' 'fix: Resolve authentication issues'"
echo "   ./update-docs-timestamp.sh 'Docs update' 'docs: Update API documentation'"
echo ""
echo "🕐 Timezone: IST (Indian Standard Time)"