# @tokai/wallet-connectors

A comprehensive wallet connector library for multiple blockchain networks with predefined chain configurations.

## Features

- Support for multiple blockchain networks (Ethereum, Solana, Polygon, BSC, Avalanche, Arbitrum, Optimism)
- Predefined chain configurations with icons, symbols, and wallet metadata
- 📱 **Mobile Deep Linking** - Native mobile wallet connections with automatic detection
- React hooks for easy integration
- TypeScript support
- Wallet detection and connection management

## Installation

```bash
pnpm add @tokai/wallet-connectors
```

## Quick Start

### Basic Usage

```tsx
import React from 'react';
import { 
  WalletProvider, 
  WalletConnectButton, 
  useWallet,
  chains,
  ethereum,
  solana,
  polygon,
  bsc,
  avalanche,
  arbitrum,
  optimism
} from '@tokai/wallet-connectors';

// Use predefined chain configurations
const customChains = [ethereum, solana, polygon];

// Or use all available chains
// const customChains = chains;

const walletConfig = {
  autoConnect: true,
  theme: 'dark' as const,
  chains: customChains,
  ethereum: {
    autoConnect: true,
    theme: 'dark' as const
  },
  solana: {
    autoConnect: true,
    theme: 'dark' as const
  }
};

function WalletDemo() {
  const { isConnected, account, network, chain, disconnect } = useWallet();

  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <div>
      {!isConnected ? (
        <WalletConnectButton variant="primary" size="lg">
          Connect Wallet
        </WalletConnectButton>
      ) : (
        <div>
          <h2>Wallet Connected!</h2>
          {account && (
            <p>Account: {account.substring(0, 6)}...{account.substring(account.length - 4)}</p>
          )}
          {chain && (
            <div>
              <img src={chain.icon} alt={`${chain.name} icon`} />
              <p>Chain: {chain.name} ({chain.symbol})</p>
              <p>Network: {network}</p>
            </div>
          )}
          <button onClick={handleDisconnect}>Disconnect Wallet</button>
        </div>
      )}
    </div>
  );
}

function App() {
  return (
    <WalletProvider config={walletConfig}>
      <WalletDemo />
    </WalletProvider>
  );
}

export default App;
```

## 📱 Mobile Deep Linking

The wallet connectors include comprehensive **cross-platform mobile deep linking** support for seamless mobile wallet connections on both iOS and Android:

### Basic Mobile Deep Linking

```tsx
import { MobileWalletDetector, useMobileDeepLinking } from '@tokai/wallet-connectors';

function MobileWalletDemo() {
  const { isMobile, platform, detectedWallet, connect } = useMobileDeepLinking();

  return (
    <div>
      {isMobile && (
        <div>
          <p>Platform: {platform}</p>
          <MobileWalletDetector
            onWalletSelect={(walletName) => {
              console.log('Selected wallet:', walletName);
            }}
            onConnect={async (uri, walletName) => {
              const success = await connect(uri, 'ethereum');
              return success;
            }}
          />
        </div>
      )}
    </div>
  );
}
```

### Advanced Cross-Platform Configuration

```tsx
const walletConfig = {
  chains: [1, 137, 56], // Ethereum, Polygon, BSC
  mobileDeepLinking: {
    enabled: true,
    timeout: 3000,
    retryAttempts: 2,
    platformSpecific: {
      ios: {
        appStoreFallback: true,    // Redirect to App Store
        universalLinks: false       // Enable Universal Links
      },
      android: {
        playStoreFallback: true,    // Redirect to Play Store
        intentUrls: true           // Use Android intent URLs
      }
    },
    customSchemes: {
      'mywallet': 'mywallet://wc?uri='
    }
  }
};
```

### Supported Mobile Wallets

**iOS & Android Support:**
- **Ethereum**: MetaMask, Trust Wallet, Rainbow, Argent, Coinbase Wallet
- **Solana**: Phantom, Solflare, Backpack
- **Multi-Chain**: imToken, TokenPocket, Bitget Wallet, OKX Wallet

**Platform Features:**
- ✅ **iOS**: App Store fallback, Safari integration, Universal Links support
- ✅ **Android**: Play Store fallback, Chrome integration, Intent URL support
- ✅ **Cross-Platform**: Automatic platform detection and appropriate handling

For detailed documentation, see:
- [MOBILE_DEEP_LINKING.md](./MOBILE_DEEP_LINKING.md) - Complete feature documentation
- [CROSS_PLATFORM_SUPPORT.md](./CROSS_PLATFORM_SUPPORT.md) - Platform-specific details
```

## Available Chain Configurations

### Individual Chain Imports

You can import individual chain configurations:

```tsx
import { 
  ethereum,        // Ethereum Mainnet
  solana,          // Solana Mainnet
  polygon,         // Polygon Mainnet
  bsc,             // BNB Smart Chain
  avalanche,       // Avalanche C-Chain
  arbitrum,        // Arbitrum One
  optimism         // Optimism
} from '@tokai/wallet-connectors';
```

### Alternative Names

For convenience, chains are also exported with alternative names:

```tsx
import { 
  mainnet,           // Alias for ethereum
  ethereumMainnet,   // Alias for ethereum
  solanaMainnet,     // Alias for solana
  polygonMainnet,    // Alias for polygon
  bscMainnet,        // Alias for bsc
  avalancheMainnet,  // Alias for avalanche
  arbitrumMainnet,   // Alias for arbitrum
  optimismMainnet    // Alias for optimism
} from '@tokai/wallet-connectors';
```

### All Chains Array

Import all available chains as an array:

```tsx
import { chains } from '@tokai/wallet-connectors';

// chains contains: [ethereum, solana, polygon, bsc, avalanche, arbitrum, optimism]
```

## Chain Configuration Structure

Each chain configuration includes:

```typescript
interface ChainConfig {
  id: string;                    // Unique chain identifier
  name: string;                  // Human-readable name
  symbol: string;                // Native token symbol
  icon: string;                  // Base64 encoded SVG icon
  nativeCurrency: {
    name: string;                // Currency name
    symbol: string;              // Currency symbol
    decimals: number;            // Decimal places
  };
  rpcUrls: string[];             // RPC endpoint URLs
  blockExplorerUrls: string[];   // Block explorer URLs
  autoConnect?: boolean;         // Auto-connect setting
  theme?: 'light' | 'dark';      // Theme preference
  wallets: {                     // Supported wallets
    [walletType: string]: {
      name: string;              // Wallet name
      icon: string;              // Wallet icon
      isInstalled?: boolean;     // Installation status
    };
  };
}
```

## Supported Networks

| Network | ID | Symbol | Native Token |
|---------|----|--------|--------------|
| Ethereum | `ethereum` | ETH | Ether |
| Solana | `solana` | SOL | Solana |
| Polygon | `polygon` | MATIC | MATIC |
| BNB Smart Chain | `bsc` | BNB | BNB |
| Avalanche | `avalanche` | AVAX | AVAX |
| Arbitrum One | `arbitrum` | ARB | Ether |
| Optimism | `optimism` | OP | Ether |

## Supported Wallets

### Ethereum Networks
- MetaMask
- Coinbase Wallet
- WalletConnect (planned)

### Solana Networks
- Phantom
- Backpack
- Solflare

## Custom Chain Configuration

You can also create custom chain configurations:

```tsx
import { ChainConfig } from '@tokai/wallet-connectors';

const customChain: ChainConfig = {
  id: 'custom-chain',
  name: 'Custom Chain',
  symbol: 'CUSTOM',
  icon: 'data:image/svg+xml;base64,...', // Your chain icon
  nativeCurrency: {
    name: 'Custom Token',
    symbol: 'CUSTOM',
    decimals: 18,
  },
  rpcUrls: ['https://your-rpc-url.com'],
  blockExplorerUrls: ['https://your-explorer.com'],
  autoConnect: true,
  theme: 'dark',
  wallets: {
    metamask: {
      name: 'MetaMask',
      icon: 'data:image/svg+xml;base64,...',
      isInstalled: true
    }
  }
};

const walletConfig = {
  chains: [customChain, ethereum, solana], // Mix custom and predefined
  // ... other config
};
```

## API Reference

### Components

- `WalletProvider` - Main provider component
- `WalletConnectButton` - Connect wallet button
- `WalletModal` - Wallet selection modal

### Hooks

- `useWallet()` - Main wallet hook
- `useEthereum()` - Ethereum-specific hook
- `useSolana()` - Solana-specific hook

### useWallet Hook

The `useWallet` hook provides access to wallet state and actions:

```typescript
const { 
  isConnected, 
  isConnecting, 
  account, 
  chain, 
  network, 
  error, 
  connect, 
  disconnect 
} = useWallet();
```

**Returns:**
- `isConnected`: Boolean indicating if wallet is connected
- `isConnecting`: Boolean indicating if connection is in progress
- `account`: Connected account address (string or null)
- `chain`: Connected chain configuration object (ChainConfig or null) - **directly accessible properties:**
  - `chain.id`: Chain identifier
  - `chain.name`: Chain display name
  - `chain.symbol`: Chain symbol (e.g., "ETH", "SOL")
  - `chain.icon`: Chain icon (Base64 data URL)
  - `chain.nativeCurrency`: Native currency details
  - `chain.rpcUrls`: RPC endpoint URLs
  - `chain.blockExplorerUrls`: Block explorer URLs
- `network`: Network information (string or null)
- `error`: Error message (string or null)
- `connect`: Function to connect wallet
- `disconnect`: Function to disconnect wallet

### Types

- `ChainConfig` - Chain configuration interface
- `WalletProviderConfig` - Provider configuration interface
- `WalletState` - Wallet state interface

## Examples

See the `apps/web` directory for a complete working example.

## License

MIT 