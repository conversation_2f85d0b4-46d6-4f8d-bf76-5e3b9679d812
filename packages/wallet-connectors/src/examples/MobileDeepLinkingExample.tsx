import React, { useState } from 'react';
import { 
  WalletProvider, 
  MobileWalletDetector, 
  useMobileDeepLinking,
  WalletConnectButton 
} from '../index';

// Example configuration
const walletConfig = {
  chains: [
    {
      id: 'ethereum',
      name: 'Ethereum',
      symbol: 'ETH',
      icon: '🔷',
      nativeCurrency: {
        name: 'Ether',
        symbol: 'ETH',
        decimals: 18,
      },
      rpcUrls: ['https://mainnet.infura.io/v3/'],
      blockExplorerUrls: ['https://etherscan.io'],
    },
    {
      id: 'polygon',
      name: 'Polygon',
      symbol: 'MATIC',
      icon: '⬡',
      nativeCurrency: {
        name: 'Polygon',
        symbol: 'MATIC',
        decimals: 18,
      },
      rpcUrls: ['https://polygon-rpc.com'],
      blockExplorerUrls: ['https://polygonscan.com'],
    },
    {
      id: 'bsc',
      name: '<PERSON><PERSON>',
      symbol: '<PERSON><PERSON><PERSON>',
      icon: '⛓️',
      nativeCurrency: {
        name: '<PERSON><PERSON><PERSON>',
        symbol: 'BNB',
        decimals: 18,
      },
      rpcUrls: ['https://bsc-dataseed.binance.org'],
      blockExplorerUrls: ['https://bscscan.com'],
    },
    {
      id: 'avalanche',
      name: 'Avalanche',
      symbol: 'AVAX',
      icon: '🔺',
      nativeCurrency: {
        name: 'Avalanche',
        symbol: 'AVAX',
        decimals: 18,
      },
      rpcUrls: ['https://api.avax.network/ext/bc/C/rpc'],
      blockExplorerUrls: ['https://snowtrace.io'],
    }
  ],
  mobileDeepLinking: {
    enabled: true,
    timeout: 3000,
    retryAttempts: 2,
    customSchemes: {
      'mywallet': 'mywallet://wc?uri=',
      'customapp': 'customapp://connect?uri='
    }
  }
};

// Example component using the hook
function MobileDeepLinkingDemo() {
  const {
    isMobile,
    isConnecting,
    detectedWallet,
    supportedWallets,
    error,
    connect,
    detectWallet
  } = useMobileDeepLinking({
    enabled: true,
    timeout: 3000,
    retryAttempts: 2
  });

  const [connectionStatus, setConnectionStatus] = useState<string>('');

  const handleConnect = async () => {
    try {
      setConnectionStatus('Connecting...');
      
      // Simulate WalletConnect URI
      const mockUri = 'wc:1234567890abcdef@2?relay-protocol=irn&symKey=abcdef1234567890';
      const success = await connect(mockUri, 'ethereum');
      
      if (success) {
        setConnectionStatus('Connected successfully!');
      } else {
        setConnectionStatus('Connection failed');
      }
    } catch (error) {
      setConnectionStatus(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  if (!isMobile) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h3>Desktop Mode</h3>
        <p>This demo is optimized for mobile devices. Try viewing on a mobile device or using browser dev tools mobile simulation.</p>
        <WalletConnectButton variant="primary" size="lg">
          Connect Wallet (Desktop)
        </WalletConnectButton>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', maxWidth: '400px', margin: '0 auto' }}>
      <h2>Mobile Wallet Deep Linking Demo</h2>
      
      {/* Status Information */}
      <div style={{ 
        background: '#f8fafc', 
        padding: '16px', 
        borderRadius: '8px', 
        marginBottom: '20px' 
      }}>
        <h3>Device Information</h3>
        <p><strong>Mobile Detected:</strong> {isMobile ? 'Yes' : 'No'}</p>
        {detectedWallet && (
          <p><strong>Detected Wallet:</strong> {detectedWallet}</p>
        )}
        <p><strong>Supported Wallets:</strong> {supportedWallets.length}</p>
        {error && (
          <p style={{ color: 'red' }}><strong>Error:</strong> {error}</p>
        )}
      </div>

      {/* Connection Status */}
      {connectionStatus && (
        <div style={{ 
          background: connectionStatus.includes('success') ? '#ecfdf5' : '#fef2f2',
          border: `1px solid ${connectionStatus.includes('success') ? '#10b981' : '#f87171'}`,
          padding: '12px',
          borderRadius: '8px',
          marginBottom: '20px',
          color: connectionStatus.includes('success') ? '#065f46' : '#dc2626'
        }}>
          <strong>Status:</strong> {connectionStatus}
        </div>
      )}

      {/* Mobile Wallet Detector */}
      <MobileWalletDetector
        onWalletSelect={(walletName) => {
          console.log('Selected wallet:', walletName);
          setConnectionStatus(`Selected: ${walletName}`);
        }}
        onConnect={async (uri, walletName) => {
          try {
            const success = await connect(uri, 'ethereum');
            if (success) {
              setConnectionStatus(`Connected to ${walletName} successfully!`);
            } else {
              setConnectionStatus(`Failed to connect to ${walletName}`);
            }
            return success;
          } catch (error) {
            setConnectionStatus(`Error connecting to ${walletName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return false;
          }
        }}
        chainId="ethereum"
      />

      {/* Manual Connect Button */}
      <div style={{ marginTop: '20px', textAlign: 'center' }}>
        <button
          onClick={handleConnect}
          disabled={isConnecting}
          style={{
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '8px',
            fontSize: '16px',
            cursor: isConnecting ? 'not-allowed' : 'pointer',
            opacity: isConnecting ? 0.6 : 1
          }}
        >
          {isConnecting ? 'Connecting...' : 'Manual Connect'}
        </button>
      </div>

      {/* Debug Information */}
      <details style={{ marginTop: '20px' }}>
        <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
          Debug Information
        </summary>
        <div style={{ 
          background: '#f1f5f9', 
          padding: '12px', 
          borderRadius: '8px', 
          marginTop: '8px',
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          <p><strong>Is Mobile:</strong> {isMobile.toString()}</p>
          <p><strong>Is Connecting:</strong> {isConnecting.toString()}</p>
          <p><strong>Detected Wallet:</strong> {detectedWallet || 'None'}</p>
          <p><strong>Supported Wallets:</strong> {supportedWallets.join(', ')}</p>
          <p><strong>Error:</strong> {error || 'None'}</p>
          <p><strong>User Agent:</strong> {navigator.userAgent}</p>
          <p><strong>Screen Size:</strong> {window.innerWidth} x {window.innerHeight}</p>
        </div>
      </details>
    </div>
  );
}

// Main example component
export function MobileDeepLinkingExample() {
  return (
    <WalletProvider config={walletConfig}>
      <div style={{ 
        minHeight: '100vh', 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '20px'
      }}>
        <div style={{ 
          background: 'white', 
          borderRadius: '16px', 
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden'
        }}>
          <div style={{ 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            padding: '24px',
            textAlign: 'center'
          }}>
            <h1 style={{ margin: '0 0 8px 0', fontSize: '24px' }}>
              📱 Mobile Wallet Deep Linking
            </h1>
            <p style={{ margin: 0, opacity: 0.9 }}>
              Test mobile wallet connections with deep linking
            </p>
          </div>
          
          <MobileDeepLinkingDemo />
        </div>
      </div>
    </WalletProvider>
  );
}

export default MobileDeepLinkingExample;