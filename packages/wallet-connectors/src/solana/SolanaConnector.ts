import { walletIcons } from '../assets/icons';

// Add window types for wallet providers
declare global {
  interface Window {
    phantom?: {
      solana?: any;
    };
    backpack?: {
      solana?: any;
    };
    solflare?: any;
  }
}

export interface SolanaWallet {
  type: 'phantom' | 'backpack' | 'solflare';
  name: string;
  icon: string;
  isInstalled: boolean;
}

export interface SolanaConnection {
  account: string;
  network: string;
  provider: any;
}

export interface SolanaConnectorConfig {
  autoConnect?: boolean;
  theme?: 'light' | 'dark';
}

export class SolanaConnector {
  private isConnected = false;
  private currentAccount: string | null = null;
  private currentProvider: any = null;
  private network: string = 'mainnet-beta';
  private error: string | null = null;
  private isDisconnecting = false;
  
  private listeners = new Map<string, Function[]>();
  private config: SolanaConnectorConfig;

  constructor(config: SolanaConnectorConfig = {}) {
    this.config = {
      autoConnect: true,
      theme: 'dark',
      ...config
    };
    
    // Don't auto-connect in constructor to avoid race conditions
    // Auto-connect will be handled by the React provider
  }

  // Public API
  async connect(walletType: 'phantom' | 'backpack' | 'solflare' = 'phantom'): Promise<SolanaConnection> {
    try {
      this.setError(null);
      
      let provider = null;

      if (walletType === 'phantom') {
        provider = window.phantom?.solana;
      } else if (walletType === 'backpack') {
        provider = window.backpack?.solana;
      } else if (walletType === 'solflare') {
        provider = window.solflare;
      }

      if (!provider) {
        throw new Error(`No Solana wallet detected for type: ${walletType}`);
      }

      await provider.connect();
      
      this.isConnected = true;
      this.currentAccount = provider.publicKey?.toString() || 'Unknown';
      this.currentProvider = provider;
      this.network = 'mainnet-beta';
      
      this.setupListeners();
      
      const connection: SolanaConnection = {
        account: this.currentAccount || '',
        network: this.network,
        provider: this.currentProvider
      };
      
      this.emit('connected', connection);
      return connection;
      
    } catch (error: any) {
      const errorMessage = `Failed to connect Solana wallet: ${error.message}`;
      this.setError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async disconnect(): Promise<void> {
    if (this.isDisconnecting) {
      return;
    }
    
    this.isDisconnecting = true;
    
    try {
      if (this.currentProvider && this.currentProvider.disconnect) {
        if (this.currentProvider.removeAllListeners) {
          this.currentProvider.removeAllListeners();
        }
        await this.currentProvider.disconnect();
      }
      
      this.clearConnection();
      this.emit('disconnected');
      
    } catch (error: any) {
      this.setError(error.message);
    } finally {
      this.isDisconnecting = false;
    }
  }

  getAccount(): string | null {
    return this.currentAccount;
  }

  getNetwork(): string {
    return this.network;
  }

  getProvider(): any {
    return this.currentProvider;
  }

  getError(): string | null {
    return this.error;
  }

  isWalletConnected(): boolean {
    return this.isConnected;
  }

  getAvailableWallets() {
    const wallets: any[] = [];

    // Check for Phantom
    if (window.phantom?.solana) {
      wallets.push({
        type: 'phantom',
        name: 'Phantom',
        icon: walletIcons.phantom,
        isInstalled: true
      });
    }

    // Check for Backpack
    if (window.backpack?.solana) {
      wallets.push({
        type: 'backpack',
        name: 'Backpack',
        icon: walletIcons.backpack,
        isInstalled: true
      });
    }

    // Check for Solflare
    if (window.solflare) {
      wallets.push({
        type: 'solflare',
        name: 'Solflare',
        icon: walletIcons.solflare,
        isInstalled: true
      });
    }

    return wallets;
  }

  // Event system
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)!;
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  removeAllListeners(): void {
    this.listeners.clear();
  }

  // Private methods
  private emit(event: string, data?: any): void {
    if (this.listeners.has(event)) {
      this.listeners.get(event)!.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Solana connector event error:', error);
        }
      });
    }
  }

  private setError(error: string | null): void {
    this.error = error;
    this.emit('error', error);
  }

  private clearConnection(): void {
    this.isConnected = false;
    this.currentAccount = null;
    this.currentProvider = null;
    this.network = 'mainnet-beta';
  }

  // Method to check for existing connection without auto-connecting
  async checkExistingConnection(): Promise<void> {
    try {
      if (window.phantom?.solana?.isConnected) {
        // Just check if already connected, don't trigger new connection
        this.isConnected = true;
        this.currentAccount = window.phantom.solana.publicKey?.toString() || 'Unknown';
        this.currentProvider = window.phantom.solana;
        this.network = 'mainnet-beta';
        this.setupListeners();
        
        const connection: SolanaConnection = {
          account: this.currentAccount || '',
          network: this.network,
          provider: this.currentProvider
        };
        
        this.emit('connected', connection);
      } else if (window.solflare?.isConnected) {
        // Just check if already connected, don't trigger new connection
        this.isConnected = true;
        this.currentAccount = window.solflare.publicKey?.toString() || 'Unknown';
        this.currentProvider = window.solflare;
        this.network = 'mainnet-beta';
        this.setupListeners();
        
        const connection: SolanaConnection = {
          account: this.currentAccount || '',
          network: this.network,
          provider: this.currentProvider
        };
        
        this.emit('connected', connection);
      }
    } catch (error) {
      // Silent fail for auto-connect
    }
  }

  private setupListeners(): void {
    if (this.currentProvider) {
      if (this.currentProvider.on) {
        this.currentProvider.on('accountChanged', (publicKey: any) => {
          if (publicKey) {
            this.currentAccount = publicKey.toString();
            this.emit('accountChanged', this.currentAccount);
          } else {
            this.clearConnection();
          }
        });
      }
      
      if (this.currentProvider.on) {
        this.currentProvider.on('disconnect', () => {
          this.clearConnection();
        });
      }
    }
  }
} 