export interface MobileDeepLinkingConfig {
  enabled: boolean;
  fallbackUrl?: string;
  customSchemes?: {
    [walletName: string]: string;
  };
  timeout?: number;
  retryAttempts?: number;
  // Platform-specific configurations
  platformSpecific?: {
    ios?: {
      appStoreFallback?: boolean;
      universalLinks?: boolean;
    };
    android?: {
      playStoreFallback?: boolean;
      intentUrls?: boolean;
    };
  };
}

export interface WalletScheme {
  name: string;
  scheme: string;
  priority: number;
  chains: string[];
  // Platform-specific schemes
  iosScheme?: string;
  androidScheme?: string;
  appStoreUrl?: string;
  playStoreUrl?: string;
}

export class MobileDeepLinkingService {
  private config: MobileDeepLinkingConfig;
  private isMobile: boolean = false;
  private platform: 'ios' | 'android' | 'unknown' = 'unknown';
  private walletSchemes: WalletScheme[] = [];

  constructor(config: MobileDeepLinkingConfig) {
    this.config = config;
    this.detectMobile();
    this.detectPlatform();
    this.initializeWalletSchemes();
  }

  private detectMobile(): void {
    // Enhanced mobile detection
    const userAgent = navigator.userAgent.toLowerCase();
    const mobileKeywords = [
      'android', 'webos', 'iphone', 'ipad', 'ipod', 
      'blackberry', 'iemobile', 'opera mini', 'mobile'
    ];
    
    this.isMobile = mobileKeywords.some(keyword => userAgent.includes(keyword)) ||
                   (window.innerWidth <= 768 && window.innerHeight <= 1024);
  }

  private detectPlatform(): void {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (userAgent.includes('iphone') || userAgent.includes('ipad') || userAgent.includes('ipod')) {
      this.platform = 'ios';
    } else if (userAgent.includes('android')) {
      this.platform = 'android';
    } else {
      this.platform = 'unknown';
    }
  }

  private initializeWalletSchemes(): void {
    // Initialize with platform-aware wallet schemes
    this.walletSchemes = [
      // Ethereum Wallets
      {
        name: 'metamask',
        scheme: 'metamask://wc?uri=',
        priority: 1,
        chains: ['ethereum', 'polygon', 'bsc', 'avalanche', 'arbitrum', 'optimism'],
        appStoreUrl: 'https://apps.apple.com/app/metamask/id1438144202',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=io.metamask'
      },
      {
        name: 'trust',
        scheme: 'trust://wc?uri=',
        priority: 2,
        chains: ['ethereum', 'polygon', 'bsc', 'avalanche'],
        appStoreUrl: 'https://apps.apple.com/app/trust-crypto-bitcoin-wallet/id1288339409',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=com.wallet.crypto.trustapp'
      },
      {
        name: 'rainbow',
        scheme: 'rainbow://wc?uri=',
        priority: 3,
        chains: ['ethereum', 'polygon', 'arbitrum', 'optimism'],
        appStoreUrl: 'https://apps.apple.com/app/rainbow-ethereum-wallet/id1457119021',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=me.rainbow'
      },
      {
        name: 'argent',
        scheme: 'argent://wc?uri=',
        priority: 4,
        chains: ['ethereum', 'polygon', 'arbitrum'],
        appStoreUrl: 'https://apps.apple.com/app/argent-wallet/id1358741926',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=im.argent.contractwalletclient'
      },
      {
        name: 'coinbase',
        scheme: 'coinbase-wallet://wc?uri=',
        priority: 5,
        chains: ['ethereum', 'polygon', 'bsc', 'avalanche'],
        appStoreUrl: 'https://apps.apple.com/app/coinbase-wallet/id1278383455',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=org.toshi'
      },
      {
        name: 'imtoken',
        scheme: 'imtokenv2://navigate/DappView?url=',
        priority: 6,
        chains: ['ethereum', 'polygon', 'bsc', 'avalanche'],
        appStoreUrl: 'https://apps.apple.com/app/imtoken/id1384792220',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=im.token.app'
      },
      {
        name: 'tokenpocket',
        scheme: 'tpdapp://wc?uri=',
        priority: 7,
        chains: ['ethereum', 'polygon', 'bsc', 'avalanche'],
        appStoreUrl: 'https://apps.apple.com/app/tokenpocket/id1436591839',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=vip.mytokenpocket'
      },
      {
        name: 'bitget',
        scheme: 'bitget://wc?uri=',
        priority: 8,
        chains: ['ethereum', 'polygon', 'bsc', 'avalanche'],
        appStoreUrl: 'https://apps.apple.com/app/bitget/id1395301115',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=com.bitget.wallet'
      },
      {
        name: 'okx',
        scheme: 'okx://wc?uri=',
        priority: 9,
        chains: ['ethereum', 'polygon', 'bsc', 'avalanche'],
        appStoreUrl: 'https://apps.apple.com/app/okx-buy-bitcoin-eth/id1327268470',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=com.okinc.okex.gp'
      },
      // Solana Wallets
      {
        name: 'phantom',
        scheme: 'phantom://wc?uri=',
        priority: 10,
        chains: ['solana'],
        appStoreUrl: 'https://apps.apple.com/app/phantom-crypto-wallet/id1598432977',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=app.phantom'
      },
      {
        name: 'solflare',
        scheme: 'solflare://wc?uri=',
        priority: 11,
        chains: ['solana'],
        appStoreUrl: 'https://apps.apple.com/app/solflare-wallet/id1580902717',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=com.solflare.mobile'
      },
      {
        name: 'backpack',
        scheme: 'backpack://wc?uri=',
        priority: 12,
        chains: ['solana'],
        appStoreUrl: 'https://apps.apple.com/app/backpack-wallet/id6446591340',
        playStoreUrl: 'https://play.google.com/store/apps/details?id=com.backpack.app'
      },
      // Add custom schemes from config
      ...Object.entries(this.config.customSchemes || {}).map(([name, scheme], index) => ({
        name,
        scheme,
        priority: 100 + index,
        chains: ['ethereum']
      }))
    ];

    // Sort by priority
    this.walletSchemes.sort((a, b) => a.priority - b.priority);
  }

  public async handleDeepLink(uri: string, chainId?: string): Promise<boolean> {
    if (!this.isMobile || !this.config.enabled) {
      return false;
    }

    try {
      // First try to detect installed wallets
      const detectedWallet = await this.detectInstalledWallet();
      if (detectedWallet) {
        const success = await this.attemptDeepLink(uri, detectedWallet);
        if (success) return true;
      }

      // Then try common wallets based on chain
      const chainWallets = this.getWalletsForChain(chainId);
      for (const wallet of chainWallets) {
        const success = await this.attemptDeepLink(uri, wallet);
        if (success) return true;
      }

      // If no deep link worked, try store fallback
      if (this.config.platformSpecific) {
        return await this.handleStoreFallback(chainWallets[0]);
      }

      return false;
    } catch (error) {
      console.warn('Mobile deep linking failed:', error);
      return false;
    }
  }

  private async handleStoreFallback(walletName?: string): Promise<boolean> {
    if (!walletName) return false;

    const wallet = this.walletSchemes.find(w => w.name === walletName);
    if (!wallet) return false;

    try {
      if (this.platform === 'ios' && wallet.appStoreUrl) {
        window.location.href = wallet.appStoreUrl;
        return true;
      } else if (this.platform === 'android' && wallet.playStoreUrl) {
        window.location.href = wallet.playStoreUrl;
        return true;
      }
    } catch (error) {
      console.warn('Store fallback failed:', error);
    }

    return false;
  }

  private async detectInstalledWallet(): Promise<string | null> {
    if (typeof window === 'undefined') return null;

    // Check for browser extensions (desktop)
    if (window.ethereum) {
      if (window.ethereum.isMetaMask) return 'metamask';
      if (window.ethereum.isCoinbaseWallet) return 'coinbase';
      if (window.ethereum.isTrust) return 'trust';
      if (window.ethereum.isRainbow) return 'rainbow';
      if (window.ethereum.isArgent) return 'argent';
      if (window.ethereum.isImToken) return 'imtoken';
      if (window.ethereum.isTokenPocket) return 'tokenpocket';
      if (window.ethereum.isBitget) return 'bitget';
      if (window.ethereum.isOkx) return 'okx';
    }

    // Check for Solana wallets
    if (window.phantom?.solana) return 'phantom';
    if (window.solflare?.isSolflare) return 'solflare';
    if (window.backpack?.solana) return 'backpack';

    // Platform-specific detection
    if (this.platform === 'ios') {
      return this.detectIOSWallet();
    } else if (this.platform === 'android') {
      return this.detectAndroidWallet();
    }

    return null;
  }

  private async detectIOSWallet(): Promise<string | null> {
    // iOS-specific detection logic
    // This could include checking for specific iOS behaviors
    // For now, we'll return null and let the user choose
    return null;
  }

  private async detectAndroidWallet(): Promise<string | null> {
    // Android-specific detection logic
    // This could include checking for installed packages
    // For now, we'll return null and let the user choose
    return null;
  }

  private getWalletsForChain(chainId?: string): string[] {
    if (!chainId) {
      // Return top wallets for ethereum by default
      return ['metamask', 'trust', 'rainbow', 'coinbase'];
    }

    const chain = chainId.toLowerCase();
    return this.walletSchemes
      .filter(wallet => wallet.chains.includes(chain))
      .map(wallet => wallet.name);
  }

  private async attemptDeepLink(uri: string, walletName: string): Promise<boolean> {
    const walletScheme = this.walletSchemes.find(w => w.name === walletName);
    if (!walletScheme) return false;

    const deepLink = walletScheme.scheme + encodeURIComponent(uri);
    const timeout = this.config.timeout || 2000;
    const retryAttempts = this.config.retryAttempts || 1;

    for (let attempt = 0; attempt <= retryAttempts; attempt++) {
      try {
        const success = await this.executeDeepLink(deepLink, timeout);
        if (success) return true;
      } catch (error) {
        console.warn(`Deep link attempt ${attempt + 1} failed for ${walletName}:`, error);
      }
    }

    return false;
  }

  private async executeDeepLink(deepLink: string, timeout: number): Promise<boolean> {
    return new Promise((resolve) => {
      let hasResponded = false;
      let timeoutId: NodeJS.Timeout;

      const cleanup = () => {
        if (hasResponded) return;
        hasResponded = true;
        clearTimeout(timeoutId);
        if (iframe && iframe.parentNode) {
          iframe.parentNode.removeChild(iframe);
        }
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };

      const handleVisibilityChange = () => {
        if (document.hidden) {
          cleanup();
          resolve(true);
        }
      };

      // Create hidden iframe
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = deepLink;
      document.body.appendChild(iframe);

      // Listen for visibility change
      document.addEventListener('visibilitychange', handleVisibilityChange);

      // Set timeout
      timeoutId = setTimeout(() => {
        cleanup();
        resolve(false);
      }, timeout);

      // Also try window.open as fallback
      try {
        const popup = window.open(deepLink, '_blank');
        if (popup) {
          setTimeout(() => {
            if (popup.closed) {
              cleanup();
              resolve(true);
            }
          }, timeout / 2);
        }
      } catch (error) {
        console.warn('Window.open fallback failed:', error);
      }
    });
  }

  public getIsMobile(): boolean {
    return this.isMobile;
  }

  public getSupportedWallets(): WalletScheme[] {
    return [...this.walletSchemes];
  }

  public addCustomScheme(name: string, scheme: string, chains: string[] = ['ethereum']): void {
    this.walletSchemes.push({
      name,
      scheme,
      priority: 100 + this.walletSchemes.length,
      chains
    });
  }

  public getPlatform(): 'ios' | 'android' | 'unknown' {
    return this.platform;
  }

  public isIOS(): boolean {
    return this.platform === 'ios';
  }

  public isAndroid(): boolean {
    return this.platform === 'android';
  }

  public getWalletStoreUrl(walletName: string): string | null {
    const wallet = this.walletSchemes.find(w => w.name === walletName);
    if (!wallet) return null;

    if (this.platform === 'ios' && wallet.appStoreUrl) {
      return wallet.appStoreUrl;
    } else if (this.platform === 'android' && wallet.playStoreUrl) {
      return wallet.playStoreUrl;
    }

    return null;
  }

  public async openWalletStore(walletName: string): Promise<boolean> {
    const storeUrl = this.getWalletStoreUrl(walletName);
    if (!storeUrl) return false;

    try {
      window.location.href = storeUrl;
      return true;
    } catch (error) {
      console.warn('Failed to open store:', error);
      return false;
    }
  }
}