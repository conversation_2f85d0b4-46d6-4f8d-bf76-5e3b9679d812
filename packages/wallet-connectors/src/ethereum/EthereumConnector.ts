import { walletIcons } from '../assets/icons';

// Add window types for wallet providers
declare global {
  interface Window {
    ethereum?: any;
  }
}

export interface EthereumWallet {
  type: 'metamask' | 'coinbase';
  name: string;
  icon: string;
  isInstalled: boolean;
}

export interface EthereumConnection {
  account: string;
  network: string;
  provider: any;
}

export interface EthereumConnectorConfig {
  autoConnect?: boolean;
  theme?: 'light' | 'dark';
}

export class EthereumConnector {
  private isConnected = false;
  private currentAccount: string | null = null;
  private currentProvider: any = null;
  private network: string = 'mainnet';
  private error: string | null = null;
  private isDisconnecting = false;
  
  private listeners = new Map<string, Function[]>();
  private config: EthereumConnectorConfig;

  constructor(config: EthereumConnectorConfig = {}) {
    this.config = {
      autoConnect: true,
      theme: 'dark',
      ...config
    };
    
    // Don't auto-connect in constructor to avoid race conditions
    // Auto-connect will be handled by the React provider
  }

  // Public API
  async connect(walletType: 'metamask' | 'coinbase' = 'metamask'): Promise<EthereumConnection> {
    try {
      this.setError(null);
      
      let provider = null;
      
      if (walletType === 'metamask') {
        provider = window.ethereum;
      } else if (walletType === 'coinbase') {
        provider = window.ethereum;
      }

      if (!provider) {
        throw new Error(`No Ethereum wallet detected for type: ${walletType}`);
      }

      const accounts = await provider.request({ method: 'eth_requestAccounts' });
      const account = accounts[0];
      
      this.isConnected = true;
      this.currentAccount = account;
      this.currentProvider = provider;
      this.network = this.getNetworkFromChainId(provider.chainId);
      
      this.setupListeners();
      
      const connection: EthereumConnection = {
        account: this.currentAccount || '',
        network: this.network,
        provider: this.currentProvider
      };
      
      this.emit('connected', connection);
      return connection;
      
    } catch (error: any) {
      const errorMessage = `Failed to connect Ethereum wallet: ${error.message}`;
      this.setError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  // Method to check for existing connection without auto-connecting
  async checkExistingConnection(): Promise<void> {
    try {
      if (window.ethereum && window.ethereum.selectedAddress) {
        // Just check if already connected, don't trigger new connection
        this.isConnected = true;
        this.currentAccount = window.ethereum.selectedAddress;
        this.currentProvider = window.ethereum;
        this.network = this.getNetworkFromChainId(window.ethereum.chainId);
        this.setupListeners();
        
        const connection: EthereumConnection = {
          account: this.currentAccount || '',
          network: this.network,
          provider: this.currentProvider
        };
        
        this.emit('connected', connection);
      }
    } catch (error) {
      // Silent fail for auto-connect
    }
  }

  async disconnect(): Promise<void> {
    if (this.isDisconnecting) {
      return;
    }
    
    this.isDisconnecting = true;
    
    try {
      if (this.currentProvider && this.currentProvider.removeAllListeners) {
        this.currentProvider.removeAllListeners();
      }
      
      this.clearConnection();
      this.emit('disconnected');
      
    } catch (error: any) {
      this.setError(error.message);
    } finally {
      this.isDisconnecting = false;
    }
  }

  getAccount(): string | null {
    return this.currentAccount;
  }

  getNetwork(): string {
    return this.network;
  }

  getProvider(): any {
    return this.currentProvider;
  }

  getError(): string | null {
    return this.error;
  }

  isWalletConnected(): boolean {
    return this.isConnected;
  }

  getAvailableWallets() {
    const wallets: any[] = [];

    // Check for MetaMask
    if (window.ethereum?.isMetaMask) {
      wallets.push({
        type: 'metamask',
        name: 'MetaMask',
        icon: walletIcons.metamask,
        isInstalled: true
      });
    }

    // Check for Coinbase Wallet
    if (window.ethereum?.isCoinbaseWallet) {
      wallets.push({
        type: 'coinbase',
        name: 'Coinbase Wallet',
        icon: walletIcons.coinbase,
        isInstalled: true
      });
    }

    // Generic Ethereum wallet (for other providers)
    if (window.ethereum && !window.ethereum.isMetaMask && !window.ethereum.isCoinbaseWallet) {
      wallets.push({
        type: 'ethereum',
        name: 'Ethereum Wallet',
        icon: walletIcons.default,
        isInstalled: true
      });
    }

    return wallets;
  }

  // Event system
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)!;
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  removeAllListeners(): void {
    this.listeners.clear();
  }

  // Private methods
  private emit(event: string, data?: any): void {
    if (this.listeners.has(event)) {
      this.listeners.get(event)!.forEach(callback => {
        try {
          callback(data);
                    } catch (error) {
          console.error('Ethereum connector event error:', error);
        }
      });
    }
  }

  private setError(error: string | null): void {
    this.error = error;
    this.emit('error', error);
  }

  private clearConnection(): void {
    this.isConnected = false;
    this.currentAccount = null;
    this.currentProvider = null;
    this.network = 'mainnet';
  }

  private setupListeners(): void {
    if (this.currentProvider) {
      this.currentProvider.on('accountsChanged', (accounts: string[]) => {
        if (accounts.length === 0) {
          this.clearConnection();
          this.emit('disconnected');
        } else {
          this.currentAccount = accounts[0];
          this.emit('accountChanged', this.currentAccount);
        }
      });

      this.currentProvider.on('chainChanged', (chainId: string) => {
        this.network = this.getNetworkFromChainId(chainId);
        this.emit('networkChanged', this.network);
      });

      this.currentProvider.on('disconnect', () => {
        this.clearConnection();
        this.emit('disconnected');
      });
    }
  }

  private getNetworkFromChainId(chainId: string): string {
    const networks: Record<string, string> = {
      '0x1': 'mainnet',
      '0x3': 'ropsten',
      '0x4': 'rinkeby',
      '0x5': 'goerli',
      '0x2a': 'kovan',
      '0x89': 'polygon',
      '0xa': 'optimism',
      '0xa4b1': 'arbitrum'
    };
    return networks[chainId] || 'unknown';
  }
} 