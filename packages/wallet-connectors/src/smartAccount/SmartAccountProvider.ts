import { ethers } from 'ethers';

export interface SmartAccountConfig {
  entryPointAddress: string;
  factoryAddress: string;
  ownerAddress: string;
  salt?: string;
}

export interface UserOperation {
  sender: string;
  nonce: string;
  initCode: string;
  callData: string;
  callGasLimit: string;
  verificationGasLimit: string;
  preVerificationGas: string;
  maxFeePerGas: string;
  maxPriorityFeePerGas: string;
  paymasterAndData: string;
  signature: string;
}

export class SmartAccountProvider {
  private provider: ethers.Provider;
  private signer: ethers.Signer;
  private entryPoint: ethers.Contract;
  private factory: ethers.Contract;
  private config: SmartAccountConfig;

  constructor(
    provider: ethers.Provider,
    signer: ethers.Signer,
    config: SmartAccountConfig
  ) {
    this.provider = provider;
    this.signer = signer;
    this.config = config;

    const entryPointABI = [
      'function getNonce(address sender, uint192 key) view returns (uint256 nonce)',
      'function simulateValidation(UserOperation calldata userOp) external',
      'function handleOps(UserOperation[] calldata ops, address payable beneficiary) external payable',
    ];

    const factoryABI = [
      'function getAddress(address owner, uint256 salt) view returns (address)',
      'function createAccount(address owner, uint256 salt) external returns (address)',
    ];

    this.entryPoint = new ethers.Contract(
      config.entryPointAddress,
      entryPointABI,
      provider
    );

    this.factory = new ethers.Contract(
      config.factoryAddress,
      factoryABI,
      provider
    );
  }

  async getAccountAddress(): Promise<string> {
    const salt = this.config.salt || ethers.ZeroHash;
    const factoryInterface = new ethers.Interface([
      'function getAddress(address owner, uint256 salt) view returns (address)',
    ]);
    const data = factoryInterface.encodeFunctionData('getAddress', [this.config.ownerAddress, salt]);
    const result = await this.provider.call({
      to: this.config.factoryAddress,
      data: data,
    });
    return factoryInterface.decodeFunctionResult('getAddress', result)[0];
  }

  async deployAccount(): Promise<string> {
    const salt = this.config.salt || ethers.ZeroHash;
    const tx = await this.factory.createAccount(this.config.ownerAddress, salt);
    const receipt = await tx.wait();
    return receipt.contractAddress;
  }

  async createUserOperation(
    target: string,
    value: string,
    data: string
  ): Promise<UserOperation> {
    const accountAddress = await this.getAccountAddress();
    const nonce = await this.entryPoint.getNonce(accountAddress, 0);

    // Create the call data for the account contract
    const accountABI = [
      'function execute(address target, uint256 value, bytes calldata data) external',
    ];
    const accountInterface = new ethers.Interface(accountABI);
    const callData = accountInterface.encodeFunctionData('execute', [
      target,
      value,
      data,
    ]);

    // Estimate gas
    const gasEstimate = await this.provider.estimateGas({
      from: accountAddress,
      to: target,
      value: ethers.parseEther(value),
      data: data,
    });

    const userOp: UserOperation = {
      sender: accountAddress,
      nonce: ethers.toBeHex(nonce),
      initCode: '0x',
      callData: callData,
      callGasLimit: ethers.toBeHex(gasEstimate),
      verificationGasLimit: ethers.toBeHex(100000),
      preVerificationGas: ethers.toBeHex(21000),
      maxFeePerGas: ethers.toBeHex(await this.getMaxFeePerGas()),
      maxPriorityFeePerGas: ethers.toBeHex(await this.getMaxPriorityFeePerGas()),
      paymasterAndData: '0x',
      signature: '0x',
    };

    return userOp;
  }

  async signUserOperation(userOp: UserOperation): Promise<UserOperation> {
    const userOpHash = await this.getUserOperationHash(userOp);
    const signature = await this.signer.signMessage(ethers.getBytes(userOpHash));
    
    return {
      ...userOp,
      signature: signature,
    };
  }

  async submitUserOperation(userOp: UserOperation): Promise<string> {
    const tx = await this.entryPoint.handleOps([userOp], await this.signer.getAddress());
    const receipt = await tx.wait();
    return receipt.hash;
  }

  async executeTransaction(
    target: string,
    value: string,
    data: string
  ): Promise<string> {
    const userOp = await this.createUserOperation(target, value, data);
    const signedUserOp = await this.signUserOperation(userOp);
    return await this.submitUserOperation(signedUserOp);
  }

  private async getUserOperationHash(userOp: UserOperation): Promise<string> {
    const userOpHash = ethers.keccak256(
      ethers.AbiCoder.defaultAbiCoder().encode(
        [
          'address',
          'uint256',
          'bytes32',
          'bytes',
          'uint256',
          'uint256',
          'uint256',
          'uint256',
          'uint256',
          'bytes32',
        ],
        [
          userOp.sender,
          userOp.nonce,
          ethers.keccak256(userOp.initCode),
          ethers.keccak256(userOp.callData),
          userOp.callGasLimit,
          userOp.verificationGasLimit,
          userOp.preVerificationGas,
          userOp.maxFeePerGas,
          userOp.maxPriorityFeePerGas,
          ethers.keccak256(userOp.paymasterAndData),
        ]
      )
    );

    const domainSeparator = ethers.keccak256(
      ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes32', 'uint256', 'address'],
        [
          ethers.keccak256(ethers.toUtf8Bytes('EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)')),
          1, // version
          this.config.entryPointAddress,
        ]
      )
    );

    return ethers.keccak256(
      ethers.concat([
        ethers.toUtf8Bytes('\x19\x01'),
        domainSeparator,
        userOpHash,
      ])
    );
  }

  private async getMaxFeePerGas(): Promise<bigint> {
    const feeData = await this.provider.getFeeData();
    return feeData.maxFeePerGas || ethers.parseUnits('20', 'gwei');
  }

  private async getMaxPriorityFeePerGas(): Promise<bigint> {
    const feeData = await this.provider.getFeeData();
    return feeData.maxPriorityFeePerGas || ethers.parseUnits('2', 'gwei');
  }

  async getAccountBalance(accountAddress: string): Promise<bigint> {
    return await this.provider.getBalance(accountAddress);
  }
}