export interface UserOperationStruct {
  sender: string;
  nonce: string;
  initCode: string;
  callData: string;
  callGasLimit: string;
  verificationGasLimit: string;
  preVerificationGas: string;
  maxFeePerGas: string;
  maxPriorityFeePerGas: string;
  paymasterAndData: string;
  signature: string;
}

export interface UserOperation {
  sender: string;
  nonce: bigint;
  initCode: Uint8Array;
  callData: Uint8Array;
  callGasLimit: bigint;
  verificationGasLimit: bigint;
  preVerificationGas: bigint;
  maxFeePerGas: bigint;
  maxPriorityFeePerGas: bigint;
  paymasterAndData: Uint8Array;
  signature: Uint8Array;
}

export interface UserOperationReceipt {
  userOpHash: string;
  entryPoint: string;
  sender: string;
  nonce: string;
  paymaster?: string;
  actualGasCost: string;
  actualGasUsed: string;
  success: boolean;
  reason?: string;
  logs: Array<{
    address: string;
    topics: string[];
    data: string;
  }>;
  receipt: {
    transactionHash: string;
    transactionIndex: string;
    blockHash: string;
    blockNumber: string;
    from: string;
    to: string;
    cumulativeGasUsed: string;
    gasUsed: string;
    contractAddress?: string;
    logs: Array<{
      address: string;
      topics: string[];
      data: string;
      blockNumber: string;
      transactionHash: string;
      transactionIndex: string;
      blockHash: string;
      logIndex: string;
      removed: boolean;
    }>;
    logsBloom: string;
    status: string;
    effectiveGasPrice: string;
  };
}

export interface SmartAccountInfo {
  address: string;
  owner: string;
  isDeployed: boolean;
  balance: string;
  nonce: number;
  implementation?: string;
}

export interface TransactionRequest {
  to: string;
  value?: string;
  data?: string;
  gasLimit?: string;
}

export interface BatchTransactionRequest {
  transactions: TransactionRequest[];
  gasLimit?: string;
}

export interface SmartAccountConfig {
  entryPointAddress: string;
  factoryAddress: string;
  bundlerUrl: string;
  paymasterUrl?: string;
  chainId: number;
}

export interface PaymasterConfig {
  url: string;
  context?: Record<string, any>;
}

export interface BundlerConfig {
  url: string;
  authKey?: string;
}

// Events
export interface UserOperationEvent {
  userOpHash: string;
  sender: string;
  paymaster?: string;
  nonce: string;
  success: boolean;
  actualGasCost: string;
  actualGasUsed: string;
}

export interface AccountDeployedEvent {
  account: string;
  owner: string;
  factory: string;
  salt: string;
}

// Error types
export class SmartAccountError extends Error {
  constructor(
    message: string,
    public code: string,
    public data?: any
  ) {
    super(message);
    this.name = 'SmartAccountError';
  }
}

export class UserOperationError extends SmartAccountError {
  constructor(message: string, public userOpHash?: string) {
    super(message, 'USER_OPERATION_ERROR');
  }
}

export class PaymasterError extends SmartAccountError {
  constructor(message: string) {
    super(message, 'PAYMASTER_ERROR');
  }
}

export class BundlerError extends SmartAccountError {
  constructor(message: string) {
    super(message, 'BUNDLER_ERROR');
  }
}

// Gas estimation types
export interface GasEstimate {
  callGasLimit: string;
  verificationGasLimit: string;
  preVerificationGas: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
}

export interface PaymasterData {
  paymaster?: string;
  paymasterVerificationGasLimit?: string;
  paymasterPostOpGasLimit?: string;
  paymasterData?: string;
}

// Smart contract interfaces
export interface IEntryPoint {
  getUserOpHash(userOp: UserOperationStruct): Promise<string>;
  simulateValidation(userOp: UserOperationStruct): Promise<any>;
  handleOps(ops: UserOperationStruct[], beneficiary: string): Promise<any>;
  getNonce(sender: string, key: string): Promise<bigint>;
}

export interface IAccountFactory {
  createAccount(owner: string, salt: bigint): Promise<string>;
  getAddress(owner: string, salt: bigint): Promise<string>;
}

export interface IAccount {
  validateUserOp(
    userOp: UserOperationStruct,
    userOpHash: string,
    missingAccountFunds: bigint
  ): Promise<bigint>;
  execute(dest: string, value: bigint, func: Uint8Array): Promise<void>;
  executeBatch(
    dest: string[],
    value: bigint[],
    func: Uint8Array[]
  ): Promise<void>;
}

export interface IPaymaster {
  validatePaymasterUserOp(
    userOp: UserOperationStruct,
    userOpHash: string,
    maxCost: bigint
  ): Promise<{ context: Uint8Array; validAfter: bigint; validUntil: bigint }>;
  postOp(
    mode: number,
    context: Uint8Array,
    actualGasCost: bigint
  ): Promise<void>;
}

// Chain-specific configurations
export interface ChainConfig {
  chainId: number;
  name: string;
  entryPointAddress: string;
  factoryAddress: string;
  bundlerUrl: string;
  paymasterUrl?: string;
  explorerUrl: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
}

// Popular testnet/mainnet configurations
export const CHAIN_CONFIGS: Record<number, ChainConfig> = {
  // Ethereum Mainnet
  1: {
    chainId: 1,
    name: 'Ethereum Mainnet',
    entryPointAddress: '******************************************',
    factoryAddress: '******************************************',
    bundlerUrl: 'https://bundler.biconomy.io/api/v2/1/nJPK7B3ru.dd7f7861-190d-41bd-af80-6877f74b8f44',
    explorerUrl: 'https://etherscan.io',
    nativeCurrency: {
      name: 'Ether',
      symbol: 'ETH',
      decimals: 18,
    },
  },
  // Polygon Mainnet
  137: {
    chainId: 137,
    name: 'Polygon Mainnet',
    entryPointAddress: '******************************************',
    factoryAddress: '******************************************',
    bundlerUrl: 'https://bundler.biconomy.io/api/v2/137/nJPK7B3ru.dd7f7861-190d-41bd-af80-6877f74b8f44',
    explorerUrl: 'https://polygonscan.com',
    nativeCurrency: {
      name: 'MATIC',
      symbol: 'MATIC',
      decimals: 18,
    },
  },
  // Goerli Testnet
  5: {
    chainId: 5,
    name: 'Goerli Testnet',
    entryPointAddress: '******************************************',
    factoryAddress: '******************************************',
    bundlerUrl: 'https://bundler.biconomy.io/api/v2/5/nJPK7B3ru.dd7f7861-190d-41bd-af80-6877f74b8f44',
    explorerUrl: 'https://goerli.etherscan.io',
    nativeCurrency: {
      name: 'Goerli Ether',
      symbol: 'ETH',
      decimals: 18,
    },
  },
  // Mumbai Testnet
  80001: {
    chainId: 80001,
    name: 'Mumbai Testnet',
    entryPointAddress: '******************************************',
    factoryAddress: '******************************************',
    bundlerUrl: 'https://bundler.biconomy.io/api/v2/80001/nJPK7B3ru.dd7f7861-190d-41bd-af80-6877f74b8f44',
    explorerUrl: 'https://mumbai.polygonscan.com',
    nativeCurrency: {
      name: 'MATIC',
      symbol: 'MATIC',
      decimals: 18,
    },
  },
};