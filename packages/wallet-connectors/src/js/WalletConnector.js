/**
 * Framework-agnostic Wallet Connector
 * Pure JavaScript implementation that can be used with any framework
 */
import walletIcons from '../assets/icons/index.js';

class WalletConnector {
  constructor(options = {}) {
    this.options = {
      theme: 'dark', // 'light' | 'dark'
      position: 'bottom-right', // 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
      autoConnect: true,
      ...options
    };
    
    this.isConnected = false;
    this.currentAccount = null;
    this.currentProvider = null;
    this.walletType = null;
    this.network = null;
    this.error = null;
    this.isDisconnecting = false;
    
    this.listeners = new Map();
    this.container = null;
    this.modal = null;
    
    this.init();
  }

  init() {
    this.createContainer();
    this.setupEventListeners();
    if (this.options.autoConnect) {
      this.checkExistingConnection();
    }
  }

  // High-level API methods
  async connect(chain = 'ethereum', walletType = 'auto') {
    try {
      this.setError(null);
      
      if (chain === 'ethereum') {
        return await this.connectEthereum(walletType);
      } else if (chain === 'solana') {
        return await this.connectSolana(walletType);
      }
      
      throw new Error(`Unsupported chain: ${chain}`);
    } catch (error) {
      this.setError(error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.isDisconnecting) {
      return;
    }
    
    this.isDisconnecting = true;
    
    try {
      if (this.walletType === 'ethereum') {
        await this.disconnectEthereum();
      } else if (this.walletType === 'solana') {
        await this.disconnectSolana();
      }
      
      this.clearConnection();
      this.emit('disconnected');
    } catch (error) {
      this.setError(error.message);
    } finally {
      this.isDisconnecting = false;
    }
  }

  getAccount() {
    return this.currentAccount;
  }

  getNetwork() {
    return this.network;
  }

  getProvider() {
    return this.currentProvider;
  }

  getError() {
    return this.error;
  }

  // Event system
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  removeAllListeners() {
    this.listeners.clear();
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Wallet connector event error:', error);
        }
      });
    }
  }

  // UI Methods
  show() {
    if (this.modal) {
      this.modal.style.display = 'flex';
    }
  }

  hide() {
    if (this.modal) {
      this.modal.style.display = 'none';
    }
  }

  // Theme management
  setTheme(theme) {
    this.options.theme = theme;
    this.updateTheme();
  }

  // Private methods
  setError(error) {
    this.error = error;
    this.emit('error', error);
  }

  clearConnection() {
    this.isConnected = false;
    this.currentAccount = null;
    this.currentProvider = null;
    this.walletType = null;
    this.network = null;
    this.isDisconnecting = false;
  }

  async checkExistingConnection() {
    try {
      // Check for existing Ethereum connection
      if (window.ethereum && window.ethereum.selectedAddress) {
        await this.connectEthereum('auto');
        return;
      }
      
      // Check for existing Solana connection
      if (window.phantom?.solana?.isConnected) {
        await this.connectSolana('phantom');
        return;
      }
      
      if (window.solflare?.isConnected) {
        await this.connectSolana('solflare');
        return;
      }
    } catch (error) {
      // Silent fail for auto-connect
    }
  }

  async connectEthereum(walletType = 'auto') {
    let provider = null;

    if (walletType === 'auto' || walletType === 'metamask') {
      provider = window.ethereum;
    }

    if (!provider) {
      throw new Error('No Ethereum wallet detected');
    }

    try {
      const accounts = await provider.request({ method: 'eth_requestAccounts' });
      const account = accounts[0];
      
      this.isConnected = true;
      this.currentAccount = account;
      this.currentProvider = provider;
      this.walletType = 'ethereum';
      this.network = this.getEthereumNetwork(provider.chainId);
      
      this.setupEthereumListeners();
      this.emit('connected', {
        account: this.currentAccount,
        network: this.network,
        provider: this.currentProvider
      });
      
      return this.currentAccount;
    } catch (error) {
      throw new Error(`Failed to connect Ethereum wallet: ${error.message}`);
    }
  }

  async disconnectEthereum() {
    if (this.currentProvider && this.currentProvider.removeAllListeners) {
      this.currentProvider.removeAllListeners();
    }
  }

  setupEthereumListeners() {
    if (this.currentProvider) {
      this.currentProvider.on('accountsChanged', (accounts) => {
        if (accounts.length === 0) {
          this.clearConnection();
          this.emit('disconnected');
        } else {
          this.currentAccount = accounts[0];
          this.emit('accountChanged', this.currentAccount);
        }
      });

      this.currentProvider.on('chainChanged', (chainId) => {
        this.network = this.getEthereumNetwork(chainId);
        this.emit('networkChanged', this.network);
      });

      this.currentProvider.on('disconnect', () => {
        this.clearConnection();
        this.emit('disconnected');
      });
    }
  }

  getEthereumNetwork(chainId) {
    const networks = {
      '0x1': 'mainnet',
      '0x3': 'ropsten',
      '0x4': 'rinkeby',
      '0x5': 'goerli',
      '0x2a': 'kovan',
      '0x89': 'polygon',
      '0xa': 'optimism',
      '0xa4b1': 'arbitrum'
    };
    return networks[chainId] || 'unknown';
  }

  async connectSolana(walletType = 'auto') {
    let provider = null;

    if (walletType === 'auto' || walletType === 'phantom') {
      provider = window.phantom?.solana;
    } else if (walletType === 'backpack') {
      provider = window.backpack?.solana;
    } else if (walletType === 'solflare') {
      provider = window.solflare;
    }

    if (!provider) {
      throw new Error(`No Solana wallet detected for type: ${walletType}`);
    }

    try {
      if (provider.isConnected) {
        await provider.connect();
      } else {
        await provider.connect();
      }
      
      this.isConnected = true;
      this.currentAccount = provider.publicKey?.toString() || 'Unknown';
      this.currentProvider = provider;
      this.walletType = 'solana';
      this.network = 'mainnet-beta';
      
      this.setupSolanaListeners();
      this.emit('connected', {
        account: this.currentAccount,
        network: this.network,
        provider: this.currentProvider
      });
      
      return this.currentAccount;
    } catch (error) {
      throw new Error(`Failed to connect Solana wallet: ${error.message}`);
    }
  }

  async disconnectSolana() {
    if (this.currentProvider && this.currentProvider.disconnect) {
      if (this.currentProvider.removeAllListeners) {
        this.currentProvider.removeAllListeners();
      }
      await this.currentProvider.disconnect();
    }
    this.clearConnection();
  }

  setupSolanaListeners() {
    if (this.currentProvider) {
      if (this.currentProvider.on) {
        this.currentProvider.on('accountChanged', (publicKey) => {
          if (publicKey) {
            this.currentAccount = publicKey.toString();
            this.emit('accountChanged', this.currentAccount);
          } else {
            this.clearConnection();
          }
        });
      }
      
      if (this.currentProvider.on) {
        this.currentProvider.on('disconnect', () => {
          this.clearConnection();
        });
      }
    }
  }

  // UI creation methods
  createContainer() {
    this.modal = document.createElement('div');
    this.modal.className = 'wallet-connector-modal';
    this.modal.style.display = 'none';
    
    const modalContent = document.createElement('div');
    modalContent.className = 'wallet-connector-content';
    
    const header = document.createElement('div');
    header.className = 'wallet-connector-header';
    header.innerHTML = '<h3>Connect Wallet</h3><button class="wallet-connector-close">&times;</button>';
    
    const body = document.createElement('div');
    body.className = 'wallet-connector-body';
    body.innerHTML = this.getWalletOptionsHTML();
    
    modalContent.appendChild(header);
    modalContent.appendChild(body);
    this.modal.appendChild(modalContent);
    
    document.body.appendChild(this.modal);
  }

  getWalletOptionsHTML() {
    return `
      <div class="wallet-options">
        <div class="wallet-option" data-chain="ethereum" data-wallet="metamask">
          <img src="${walletIcons.metamask}" alt="MetaMask">
          <div>
            <div class="wallet-name">MetaMask</div>
            <div class="wallet-description">Connect to MetaMask</div>
          </div>
        </div>
        <div class="wallet-option" data-chain="solana" data-wallet="phantom">
          <img src="${walletIcons.phantom}" alt="Phantom">
          <div>
            <div class="wallet-name">Phantom</div>
            <div class="wallet-description">Connect to Phantom</div>
          </div>
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    this.modal.addEventListener('click', (e) => {
      if (e.target === this.modal) {
        this.hide();
      }
    });

    const closeBtn = this.modal.querySelector('.wallet-connector-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hide());
    }

    const walletOptions = this.modal.querySelectorAll('.wallet-option');
    walletOptions.forEach(option => {
      option.addEventListener('click', async () => {
        const chain = option.dataset.chain;
        const wallet = option.dataset.wallet;
        
        try {
          await this.connect(chain, wallet);
          this.hide();
        } catch (error) {
          console.error('Connection failed:', error);
        }
      });
    });
  }

  updateTheme() {
    if (this.modal) {
      this.modal.className = `wallet-connector-modal wallet-connector-${this.options.theme}`;
    }
  }
}

export default WalletConnector;
export { walletIcons }; 