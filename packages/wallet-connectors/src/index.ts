export { WalletProvider } from './react/provider/WalletProvider';
export { WalletConnectButton } from './react/components/WalletConnectButton';
export { WalletModal } from './react/components/WalletModal';
export { MobileWalletDetector } from './react/components/MobileWalletDetector';

export { EmbeddedWalletButton, EmbeddedWalletConnector } from './embedded';
export type { EmbeddedUser, EmbeddedConnection, EmbeddedWalletConfig } from './embedded';

export { useWallet } from './react/provider/WalletProvider';
export { useMobileDeepLinking } from './react/hooks/useMobileDeepLinking';

export { EthereumConnector } from './ethereum/EthereumConnector';
export { SolanaConnector } from './solana/SolanaConnector';

export type { WalletProviderConfig, WalletState, ChainConfig } from './react/provider/WalletProvider';
export type { WalletModalProps } from './react/components/WalletModal';
export type { 
  UseMobileDeepLinkingConfig, 
  MobileDeepLinkingState, 
  MobileDeepLinkingActions 
} from './react/hooks/useMobileDeepLinking';
export type { MobileWalletDetectorProps } from './react/components/MobileWalletDetector';

export { walletIcons } from './assets/icons';

export { 
  chains,
  ethereum,
  solana,
  polygon,
  bsc,
  avalanche,
  arbitrum,
  optimism,
  mainnet,
  ethereumMainnet,
  solanaMainnet,
  polygonMainnet,
  bscMainnet,
  avalancheMainnet,
  arbitrumMainnet,
  optimismMainnet
} from './chains'; 