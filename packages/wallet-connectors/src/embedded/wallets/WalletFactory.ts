import { BaseWallet, WalletConfig } from './BaseWallet';
import { EthereumWallet, EthereumWalletConfig } from './EthereumWallet';
import { SolanaWallet, SolanaWalletConfig } from './SolanaWallet';

export type SupportedNetwork = 'ethereum' | 'solana';
export type ExternalNetwork = 'ethereum' | 'polygon' | 'bsc' | 'avalanche' | 'arbitrum' | 'optimism' | 'solana' | 'fivire' | 'astar';

export interface WalletFactoryConfig {
  network: SupportedNetwork;
  chainId?: number;
  rpcUrl?: string;
  commitment?: 'processed' | 'confirmed' | 'finalized';
}

export class WalletFactory {
  private static readonly DEFAULT_RPC_URLS: Record<SupportedNetwork, string> = {
    ethereum: 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID',
    solana: 'https://api.mainnet-beta.solana.com'
  };

  private static readonly DEFAULT_CHAIN_IDS: Record<SupportedNetwork, number> = {
    ethereum: 1,
    solana: 0 // Solana doesn't use chainId in the same way
  };

  public static createWallet(config: WalletFactoryConfig): BaseWallet {
    const rpcUrl = config.rpcUrl || this.DEFAULT_RPC_URLS[config.network];
    const chainId = config.chainId || this.DEFAULT_CHAIN_IDS[config.network];

    switch (config.network) {
      case 'ethereum':
        return new EthereumWallet({
          network: config.network,
          chainId,
          rpcUrl
        });

      case 'solana':
        return new SolanaWallet({
          network: config.network,
          rpcUrl,
          commitment: config.commitment
        });

      default:
        throw new Error(`Unsupported network: ${config.network}. Only Ethereum and Solana are supported for embedded wallets.`);
    }
  }

  public static getSupportedNetworks(): SupportedNetwork[] {
    return Object.keys(this.DEFAULT_RPC_URLS) as SupportedNetwork[];
  }

  public static getDefaultConfig(network: SupportedNetwork): WalletFactoryConfig {
    return {
      network,
      chainId: this.DEFAULT_CHAIN_IDS[network],
      rpcUrl: this.DEFAULT_RPC_URLS[network]
    };
  }

  public static validateNetwork(network: string): network is SupportedNetwork {
    return Object.keys(this.DEFAULT_RPC_URLS).includes(network);
  }
} 