import { ethers } from 'ethers'
import { Keypair, Connection, PublicKey } from '@solana/web3.js'
import * as bip39 from 'bip39'

export interface GeneratedWallet {
  network: string
  seedPhrase: string
  privateKey: string
  publicKey: string
  address: string
  derivationPath?: string
}

export interface WalletGenerationOptions {
  network: 'ethereum' | 'polygon' | 'bsc' | 'avalanche' | 'arbitrum' | 'optimism' | 'solana'
  derivationPath?: string
  existingSeedPhrase?: string
}

export class WalletGenerator {
  private static readonly DERIVATION_PATHS = {
    ethereum: "m/44'/60'/0'/0/0",
    polygon: "m/44'/60'/0'/0/0",
    bsc: "m/44'/60'/0'/0/0",
    avalanche: "m/44'/60'/0'/0/0",
    arbitrum: "m/44'/60'/0'/0/0",
    optimism: "m/44'/60'/0'/0/0",
    solana: "m/44'/501'/0'/0'"
  }

  /**
   * Generate a new wallet for the specified network
   */
  static async generateWallet(options: WalletGenerationOptions): Promise<GeneratedWallet> {
    const { network, derivationPath, existingSeedPhrase } = options

    // Generate or use existing seed phrase
    const seedPhrase = existingSeedPhrase || bip39.generateMnemonic(256) // 24 words for better security

    if (network === 'solana') {
      return this.generateSolanaWallet(seedPhrase, derivationPath)
    } else {
      return this.generateEthereumWallet(seedPhrase, network, derivationPath)
    }
  }

  /**
   * Generate Ethereum-compatible wallet (ETH, Polygon, BSC, etc.)
   */
  private static async generateEthereumWallet(
    seedPhrase: string,
    network: string,
    derivationPath?: string
  ): Promise<GeneratedWallet> {
    const path = derivationPath || this.DERIVATION_PATHS[network as keyof typeof this.DERIVATION_PATHS]
    
    // Generate HD wallet from seed phrase
    const hdNode = ethers.HDNodeWallet.fromPhrase(seedPhrase, undefined, path)
    
    return {
      network,
      seedPhrase,
      privateKey: hdNode.privateKey,
      publicKey: hdNode.publicKey,
      address: hdNode.address,
      derivationPath: path
    }
  }

  /**
   * Generate Solana wallet
   */
  private static async generateSolanaWallet(
    seedPhrase: string,
    derivationPath?: string
  ): Promise<GeneratedWallet> {
    const path = derivationPath || this.DERIVATION_PATHS.solana
    
    // Generate seed from mnemonic
    const seed = await bip39.mnemonicToSeed(seedPhrase)
    
    // Create keypair from seed
    const keypair = Keypair.fromSeed(seed.slice(0, 32))
    
    return {
      network: 'solana',
      seedPhrase,
      privateKey: Buffer.from(keypair.secretKey).toString('hex'),
      publicKey: keypair.publicKey.toString(),
      address: keypair.publicKey.toString(),
      derivationPath: path
    }
  }

  /**
   * Import wallet from existing seed phrase
   */
  static async importWallet(
    seedPhrase: string,
    network: string,
    derivationPath?: string
  ): Promise<GeneratedWallet> {
    // Validate seed phrase
    if (!bip39.validateMnemonic(seedPhrase)) {
      throw new Error('Invalid seed phrase')
    }

    return this.generateWallet({
      network: network as any,
      derivationPath,
      existingSeedPhrase: seedPhrase
    })
  }

  /**
   * Generate multiple wallets from the same seed phrase
   */
  static async generateMultipleWallets(
    seedPhrase: string,
    networks: string[],
    derivationPaths?: Record<string, string>
  ): Promise<GeneratedWallet[]> {
    const wallets: GeneratedWallet[] = []

    for (const network of networks) {
      const wallet = await this.generateWallet({
        network: network as any,
        derivationPath: derivationPaths?.[network],
        existingSeedPhrase: seedPhrase
      })
      wallets.push(wallet)
    }

    return wallets
  }

  /**
   * Validate a seed phrase
   */
  static validateSeedPhrase(seedPhrase: string): boolean {
    return bip39.validateMnemonic(seedPhrase)
  }

  /**
   * Get supported networks
   */
  static getSupportedNetworks(): string[] {
    return Object.keys(this.DERIVATION_PATHS)
  }

  /**
   * Get default derivation path for a network
   */
  static getDefaultDerivationPath(network: string): string {
    return this.DERIVATION_PATHS[network as keyof typeof this.DERIVATION_PATHS] || this.DERIVATION_PATHS.ethereum
  }
} 