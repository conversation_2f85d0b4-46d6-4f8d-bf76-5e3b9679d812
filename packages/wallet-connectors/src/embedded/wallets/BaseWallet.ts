export interface WalletConfig {
  network: string;
  chainId?: string | number;
  rpcUrl?: string;
}

export interface WalletAddress {
  address: string;
  network: string;
  chainId?: string | number;
}

export interface WalletBalance {
  balance: string;
  symbol: string;
  decimals: number;
  network: string;
}

export interface Transaction {
  to: string;
  value: string;
  data?: string;
  gasLimit?: string;
  gasPrice?: string;
}

export interface SignedTransaction {
  hash: string;
  rawTransaction: string;
  network: string;
}

export abstract class BaseWallet {
  protected config: WalletConfig;

  constructor(config: WalletConfig) {
    this.config = config;
  }

  abstract generateAddress(userId: string): Promise<WalletAddress>;
  abstract getBalance(address: string): Promise<WalletBalance>;
  abstract signMessage(message: string, userId: string): Promise<string>;
  abstract signTransaction(transaction: Transaction, userId: string): Promise<SignedTransaction>;
  abstract sendTransaction(signedTx: SignedTransaction): Promise<string>;
  abstract validateAddress(address: string): boolean;

  protected async generateDeterministicId(userId: string, salt?: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(userId + (salt || ''));
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  public getNetwork(): string {
    return this.config.network;
  }

  public getChainId(): string | number | undefined {
    return this.config.chainId;
  }
} 