import React, { useState, useEffect } from 'react';
import { EmbeddedWalletConnector, EmbeddedConnection, EmbeddedWalletConfig } from './EmbeddedWalletConnector';

interface EmbeddedWalletButtonProps {
  config: EmbeddedWalletConfig;
  onConnect?: (connection: EmbeddedConnection) => void;
  onDisconnect?: () => void;
  onError?: (error: string) => void;
  className?: string;
  children?: React.ReactNode;
  disabled?: boolean;
}

export function EmbeddedWalletButton({
  config,
  onConnect,
  onDisconnect,
  onError,
  className = '',
  children,
  disabled = false
}: EmbeddedWalletButtonProps) {
  const [connector, setConnector] = useState<EmbeddedWalletConnector | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [user, setUser] = useState<EmbeddedConnection['user'] | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showAuthOptions, setShowAuthOptions] = useState(false);
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [otpSent, setOtpSent] = useState(false);

  useEffect(() => {
    const embeddedConnector = new EmbeddedWalletConnector(config);
    setConnector(embeddedConnector);

    // Set up event listeners
    embeddedConnector.on('connected', (connection: EmbeddedConnection) => {
      setIsConnected(true);
      setIsConnecting(false);
      setUser(connection.user);
      setError(null);
      setShowAuthOptions(false);
      setOtpSent(false);
      setEmail('');
      setOtp('');
      onConnect?.(connection);
    });

    embeddedConnector.on('disconnected', () => {
      setIsConnected(false);
      setIsConnecting(false);
      setUser(null);
      setError(null);
      setShowAuthOptions(false);
      setOtpSent(false);
      setEmail('');
      setOtp('');
      onDisconnect?.();
    });

    embeddedConnector.on('error', (errorMessage: string) => {
      setIsConnecting(false);
      setError(errorMessage);
      onError?.(errorMessage);
    });

    // Check if already connected
    const connection = embeddedConnector.getConnection();
    if (connection) {
      setIsConnected(true);
      setUser(connection.user);
    }

    return () => {
      embeddedConnector.destroy();
    };
  }, [config, onConnect, onDisconnect, onError]);

  const handleConnectWithGoogle = async () => {
    if (!connector || disabled) return;

    try {
      setIsConnecting(true);
      setError(null);
      await connector.connectWithGoogle();
    } catch (err) {
      setIsConnecting(false);
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect with Google';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  };

  const handleConnectWithEmail = async () => {
    if (!connector || disabled || !email.trim()) return;

    try {
      setIsConnecting(true);
      setError(null);
      await connector.connectWithEmail(email);
      setOtpSent(true);
      setIsConnecting(false);
    } catch (err) {
      setIsConnecting(false);
      const errorMessage = err instanceof Error ? err.message : 'Failed to send OTP';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  };

  const handleVerifyOTP = async () => {
    if (!connector || disabled || !otp.trim()) return;

    try {
      setIsConnecting(true);
      setError(null);
      await connector.verifyOTP(email, otp);
    } catch (err) {
      setIsConnecting(false);
      const errorMessage = err instanceof Error ? err.message : 'Failed to verify OTP';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  };

  const handleDisconnect = async () => {
    if (!connector) return;

    try {
      await connector.disconnect();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to disconnect';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  };

  const handleBackToOptions = () => {
    setShowAuthOptions(false);
    setOtpSent(false);
    setEmail('');
    setOtp('');
    setError(null);
  };

  if (isConnected && user) {
    return (
      <div className={`embedded-wallet-connected ${className}`}>
        <div className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          {user.picture && (
            <img 
              src={user.picture} 
              alt={user.name || user.email}
              className="w-8 h-8 rounded-full"
            />
          )}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-green-800 dark:text-green-200 truncate">
              {user.name || user.email}
            </p>
            <p className="text-xs text-green-600 dark:text-green-300">
              {user.authMethod === 'google' ? 'Google' : 'Email'} • Connected
            </p>
          </div>
          <button
            onClick={handleDisconnect}
            className="px-3 py-1 text-xs bg-red-500 hover:bg-red-600 text-white rounded transition-colors"
          >
            Disconnect
          </button>
        </div>
      </div>
    );
  }

  if (showAuthOptions) {
    return (
      <div className={`embedded-wallet-auth-options ${className}`}>
        <div className="space-y-4 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Choose Authentication
            </h3>
            <button
              onClick={handleBackToOptions}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕
            </button>
          </div>

          {error && (
            <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">
              {error}
            </div>
          )}

          {!otpSent ? (
            <div className="space-y-4">
              {/* Google OAuth Option */}
              {config.googleAuth?.clientId && (
                <button
                  onClick={handleConnectWithGoogle}
                  disabled={isConnecting || disabled}
                  className="w-full px-4 py-3 bg-white hover:bg-gray-50 disabled:bg-gray-400 text-gray-700 border border-gray-300 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2"
                >
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  <span>Continue with Google</span>
                </button>
              )}

              {/* Email OTP Option */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Or continue with email
                </label>
                <div className="flex space-x-2">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                  />
                  <button
                    onClick={handleConnectWithEmail}
                    disabled={isConnecting || disabled || !email.trim()}
                    className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-lg transition-colors text-sm"
                  >
                    Send OTP
                  </button>
                </div>
              </div>
            </div>
          ) : (
            /* OTP Verification */
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                  We've sent a verification code to:
                </p>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {email}
                </p>
              </div>
              
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Enter verification code
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                    placeholder="Enter OTP"
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                    maxLength={6}
                  />
                  <button
                    onClick={handleVerifyOTP}
                    disabled={isConnecting || disabled || !otp.trim()}
                    className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-lg transition-colors text-sm"
                  >
                    Verify
                  </button>
                </div>
              </div>

              <div className="text-center">
                <button
                  onClick={handleBackToOptions}
                  className="text-sm text-blue-500 hover:text-blue-600"
                >
                  Use different email
                </button>
              </div>
            </div>
          )}

          {isConnecting && (
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
              <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
              <span>Connecting...</span>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <button
      onClick={() => setShowAuthOptions(true)}
      disabled={disabled}
      className={`embedded-wallet-button px-6 py-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors flex items-center justify-center space-x-2 ${className}`}
    >
      {children || (
        <>
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <span>Connect Embedded Wallet</span>
        </>
      )}
    </button>
  );
} 