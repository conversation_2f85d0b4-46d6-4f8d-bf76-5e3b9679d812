import { createCipheriv, createDecipheriv, randomBytes, scryptSync } from 'crypto'

export interface EncryptedData {
  encryptedData: string
  iv: string
  salt: string
  tag: string
}

export class EncryptionService {
  private static readonly ALGORITHM = 'aes-256-gcm'
  private static readonly KEY_LENGTH = 32
  private static readonly IV_LENGTH = 16
  private static readonly SALT_LENGTH = 64
  private static readonly TAG_LENGTH = 16

  /**
   * Encrypt sensitive data (private key, seed phrase)
   */
  static encrypt(data: string, encryptionKey: string): EncryptedData {
    if (!encryptionKey || encryptionKey.length < 32) {
      throw new Error('Encryption key must be at least 32 characters long')
    }

    // Generate salt and derive key
    const salt = randomBytes(this.SALT_LENGTH)
    const key = scryptSync(encryptionKey, salt, this.KEY_LENGTH)

    // Generate IV
    const iv = randomBytes(this.IV_LENGTH)

    // Create cipher
    const cipher = createCipheriv(this.ALGORITHM, key, iv)

    // Encrypt data
    let encrypted = cipher.update(data, 'utf8', 'hex')
    encrypted += cipher.final('hex')

    // Get auth tag
    const tag = cipher.getAuthTag()

    return {
      encryptedData: encrypted,
      iv: iv.toString('hex'),
      salt: salt.toString('hex'),
      tag: tag.toString('hex')
    }
  }

  /**
   * Decrypt sensitive data
   */
  static decrypt(encryptedData: EncryptedData, encryptionKey: string): string {
    if (!encryptionKey || encryptionKey.length < 32) {
      throw new Error('Encryption key must be at least 32 characters long')
    }

    try {
      // Reconstruct key from salt
      const salt = Buffer.from(encryptedData.salt, 'hex')
      const key = scryptSync(encryptionKey, salt, this.KEY_LENGTH)

      // Reconstruct IV and tag
      const iv = Buffer.from(encryptedData.iv, 'hex')
      const tag = Buffer.from(encryptedData.tag, 'hex')

      // Create decipher
      const decipher = createDecipheriv(this.ALGORITHM, key, iv)
      decipher.setAuthTag(tag)

      // Decrypt data
      let decrypted = decipher.update(encryptedData.encryptedData, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      return decrypted
    } catch (error) {
      throw new Error('Failed to decrypt data. Invalid encryption key or corrupted data.')
    }
  }

  /**
   * Generate a secure encryption key
   */
  static generateEncryptionKey(): string {
    return randomBytes(32).toString('hex')
  }

  /**
   * Validate encryption key format
   */
  static validateEncryptionKey(key: string): boolean {
    return Boolean(key && key.length >= 32)
  }

  /**
   * Encrypt wallet data for storage
   */
  static encryptWalletData(
    walletData: {
      privateKey: string
      seedPhrase: string
      [key: string]: any
    },
    encryptionKey: string
  ): {
    encryptedPrivateKey: EncryptedData
    encryptedSeedPhrase: EncryptedData
    publicData: Omit<typeof walletData, 'privateKey' | 'seedPhrase'>
  } {
    const { privateKey, seedPhrase, ...publicData } = walletData

    return {
      encryptedPrivateKey: this.encrypt(privateKey, encryptionKey),
      encryptedSeedPhrase: this.encrypt(seedPhrase, encryptionKey),
      publicData
    }
  }

  /**
   * Decrypt wallet data from storage
   */
  static decryptWalletData(
    encryptedData: {
      encryptedPrivateKey: EncryptedData
      encryptedSeedPhrase: EncryptedData
      publicData: any
    },
    encryptionKey: string
  ): {
    privateKey: string
    seedPhrase: string
    [key: string]: any
  } {
    const privateKey = this.decrypt(encryptedData.encryptedPrivateKey, encryptionKey)
    const seedPhrase = this.decrypt(encryptedData.encryptedSeedPhrase, encryptionKey)

    return {
      privateKey,
      seedPhrase,
      ...encryptedData.publicData
    }
  }
} 