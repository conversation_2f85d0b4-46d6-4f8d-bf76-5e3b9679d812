export interface EmailUser {
  email: string;
  verified_email: boolean;
}

export interface EmailAuthConfig {
  apiBaseUrl: string;
  otpLength?: number;
  otpExpiryMinutes?: number;
}

export class EmailAuthProvider {
  private config: EmailAuthConfig;

  constructor(config: EmailAuthConfig) {
    this.config = {
      otpLength: config.otpLength || 6,
      otpExpiryMinutes: config.otpExpiryMinutes || 10,
      ...config
    };
  }

  public async sendOTP(email: string): Promise<void> {
    const response = await fetch(`${this.config.apiBaseUrl}/auth/send-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        email,
        otpLength: this.config.otpLength,
        expiryMinutes: this.config.otpExpiryMinutes
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send OTP');
    }

    // For demo purposes, we'll simulate OTP sending
    console.log(`OTP sent to ${email} (demo mode)`);
  }

  public async verifyOTP(email: string, otp: string): Promise<{ user: EmailUser; accessToken: string }> {
    const response = await fetch(`${this.config.apiBaseUrl}/auth/verify-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, otp }),
    });

    if (!response.ok) {
      throw new Error('Invalid OTP');
    }

    const { user, accessToken } = await response.json();

    return {
      user: {
        email: user.email,
        verified_email: true
      },
      accessToken
    };
  }

  public async resendOTP(email: string): Promise<void> {
    return this.sendOTP(email);
  }

  public destroy(): void {
    // Cleanup if needed
  }
} 