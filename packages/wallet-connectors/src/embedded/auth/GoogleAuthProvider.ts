export interface GoogleUser {
  id: string;
  email: string;
  name: string;
  picture: string;
  verified_email: boolean;
}

export interface GoogleAuthConfig {
  clientId: string;
  scopes?: string[];
}

export class GoogleAuthProvider {
  private googleAuth: any;
  private config: GoogleAuthConfig;

  constructor(config: GoogleAuthConfig) {
    this.config = {
      scopes: config.scopes || ['openid', 'profile', 'email'],
      ...config
    };
    this.initializeGoogleAuth();
  }

  private initializeGoogleAuth() {
    if (typeof window === 'undefined') return;

    // Load Google Identity Services
    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client';
    script.async = true;
    script.defer = true;
    script.onload = () => {
      this.setupGoogleAuth();
    };
    document.head.appendChild(script);
  }

  private setupGoogleAuth() {
    if (typeof window === 'undefined' || !window.google) return;

    this.googleAuth = window.google.accounts.oauth2.initTokenClient({
      client_id: this.config.clientId,
      scope: this.config.scopes?.join(' '),
      callback: (response: any) => {
        // This will be handled by the calling code
        console.log('Google auth callback received');
      },
    });
  }

  public async authenticate(): Promise<{ user: GoogleUser; accessToken: string }> {
    if (!this.googleAuth) {
      throw new Error('Google Auth not initialized. Please provide googleClientId in config.');
    }

    return new Promise((resolve, reject) => {
      const originalCallback = this.googleAuth.callback;
      
      this.googleAuth.callback = async (response: any) => {
        if (response.error) {
          reject(new Error(`Google authentication failed: ${response.error}`));
          return;
        }

        try {
          const userInfo = await this.getGoogleUserInfo(response.access_token);
          resolve({
            user: userInfo,
            accessToken: response.access_token
          });
        } catch (error) {
          reject(error);
        } finally {
          // Restore original callback
          this.googleAuth.callback = originalCallback;
        }
      };

      this.googleAuth.requestAccessToken();
    });
  }

  private async getGoogleUserInfo(accessToken: string): Promise<GoogleUser> {
    const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch Google user info');
    }

    return response.json();
  }

  public revoke(): void {
    if (this.googleAuth) {
      this.googleAuth.revoke();
    }
  }

  public destroy(): void {
    this.revoke();
  }
}

// Extend window interface for Google Identity Services
declare global {
  interface Window {
    google?: {
      accounts: {
        oauth2: {
          initTokenClient: (config: any) => any;
        };
      };
    };
  }
} 