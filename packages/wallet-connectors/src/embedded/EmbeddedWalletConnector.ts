import { <PERSON><PERSON>uth<PERSON><PERSON>ider, <PERSON><PERSON>ser, GoogleAuthConfig } from './auth/GoogleAuthProvider';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ail<PERSON><PERSON>, EmailAuthConfig } from './auth/EmailAuthProvider';
import { WalletFactory, WalletFactoryConfig, BaseWallet, WalletAddress, Transaction } from './wallets';
import { SmartAccountProvider, SmartAccountConfig } from '../smartAccount/SmartAccountProvider';
import { CHAIN_CONFIGS } from '../smartAccount/types';

// Simple event emitter for browser environment
class EventEmitter {
  private events: { [key: string]: Function[] } = {};

  on(event: string, listener: Function) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
  }

  once(event: string, listener: Function) {
    const onceWrapper = (...args: any[]) => {
      listener(...args);
      this.removeListener(event, onceWrapper);
    };
    this.on(event, onceWrapper);
  }

  emit(event: string, ...args: any[]) {
    if (this.events[event]) {
      this.events[event].forEach(listener => listener(...args));
    }
  }

  removeListener(event: string, listener: Function) {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter(l => l !== listener);
    }
  }

  removeAllListeners() {
    this.events = {};
  }
}

export interface EmbeddedUser {
  id: string;
  email: string;
  name?: string;
  picture?: string;
  verified_email: boolean;
  authMethod: 'google' | 'email';
  smartAccount?: {
    address: string;
    isDeployed: boolean;
    chainId: number;
  };
}

export interface EmbeddedConnection {
  user: EmbeddedUser;
  accessToken?: string | null;
  walletAddress: string;
  network: string;
  chainId?: string | number | null;
}

export interface EmbeddedWalletConfig {
  // Authentication configs
  googleAuth?: GoogleAuthConfig;
  emailAuth?: EmailAuthConfig;
  
  // Wallet configs
  walletConfig: WalletFactoryConfig;
  
  // General configs
  apiBaseUrl?: string;
}

export class EmbeddedWalletConnector extends EventEmitter {
  private isConnected: boolean = false;
  private currentUser: EmbeddedUser | null = null;
  private currentAccessToken: string | null = null;
  private currentWalletAddress: string | null = null;
  private currentNetwork: string | null = null;
  private currentChainId: string | number | null | undefined = null;
  private error: string | null = null;
  
  // Authentication providers
  private googleAuthProvider: GoogleAuthProvider | null = null;
  private emailAuthProvider: EmailAuthProvider | null = null;
  
  // Wallet instance
  private wallet: BaseWallet | null = null;
  
  private config: EmbeddedWalletConfig;

  constructor(config: EmbeddedWalletConfig) {
    super();
    this.config = {
      apiBaseUrl: config.apiBaseUrl || 'https://api.yourdomain.com',
      ...config
    };
    
    this.initializeProviders();
    this.initializeWallet();
  }

  private initializeProviders() {
    // Initialize Google Auth Provider if config provided
    if (this.config.googleAuth) {
      this.googleAuthProvider = new GoogleAuthProvider(this.config.googleAuth);
    }

    // Initialize Email Auth Provider if config provided
    if (this.config.emailAuth) {
      this.emailAuthProvider = new EmailAuthProvider(this.config.emailAuth);
    }
  }

  private initializeWallet() {
    try {
      this.wallet = WalletFactory.createWallet(this.config.walletConfig);
    } catch (error) {
      console.error('Failed to initialize wallet:', error);
    }
  }

  private async createOrGetEmbeddedUser(userData: {
    email: string;
    name?: string;
    picture?: string;
    verified_email: boolean;
    authMethod: 'google' | 'email';
    googleId?: string;
  }): Promise<EmbeddedUser> {
    // In a real implementation, you would make an API call to your backend
    // to create or retrieve the user from your database
    
    // For demo purposes, we'll create a deterministic user ID
    const userId = userData.googleId || userData.email;
    const encoder = new TextEncoder();
    const data = encoder.encode(userId);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    
    return {
      id: hashHex.slice(0, 16),
      email: userData.email,
      name: userData.name,
      picture: userData.picture,
      verified_email: userData.verified_email,
      authMethod: userData.authMethod
    };
  }

  public async connectWithGoogle(): Promise<EmbeddedConnection> {
    if (!this.googleAuthProvider) {
      throw new Error('Google Auth not initialized. Please provide googleAuth config.');
    }

    if (!this.wallet) {
      throw new Error('Wallet not initialized. Please check walletConfig.');
    }

    try {
      this.setError(null);

      // Authenticate with Google
      const { user: googleUser, accessToken } = await this.googleAuthProvider.authenticate();
      
      // Create or get embedded user
      const embeddedUser = await this.createOrGetEmbeddedUser({
        email: googleUser.email,
        name: googleUser.name,
        picture: googleUser.picture,
        verified_email: googleUser.verified_email,
        authMethod: 'google' as const,
        googleId: googleUser.id
      });

      // Generate or retrieve wallet address
      const walletAddress = await this.wallet.generateAddress(embeddedUser.id);

      this.isConnected = true;
      this.currentUser = embeddedUser;
      this.currentAccessToken = accessToken;
      this.currentWalletAddress = walletAddress.address;
      this.currentNetwork = walletAddress.network;
      this.currentChainId = walletAddress.chainId;

      const connection: EmbeddedConnection = {
        user: embeddedUser,
        accessToken,
        walletAddress: walletAddress.address,
        network: walletAddress.network,
        chainId: walletAddress.chainId
      };

      this.emit('connected', connection);
      return connection;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to complete Google authentication';
      this.setError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  public async connectWithEmail(email: string): Promise<EmbeddedConnection> {
    if (!this.emailAuthProvider) {
      throw new Error('Email Auth not initialized. Please provide emailAuth config.');
    }

    if (!this.wallet) {
      throw new Error('Wallet not initialized. Please check walletConfig.');
    }

    try {
      this.setError(null);

      // Send OTP to email
      await this.emailAuthProvider.sendOTP(email);

      // Return a promise that resolves when OTP is verified
      return new Promise((resolve, reject) => {
        const onConnected = (connection: EmbeddedConnection) => {
          this.removeListener('connected', onConnected);
          this.removeListener('error', onError);
          resolve(connection);
        };

        const onError = (error: string) => {
          this.removeListener('connected', onConnected);
          this.removeListener('error', onError);
          reject(new Error(error));
        };

        this.once('connected', onConnected);
        this.once('error', onError);
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start email authentication';
      this.setError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  public async verifyOTP(email: string, otp: string): Promise<void> {
    if (!this.emailAuthProvider) {
      throw new Error('Email Auth not initialized.');
    }

    if (!this.wallet) {
      throw new Error('Wallet not initialized.');
    }

    try {
      // Verify OTP
      const { user: emailUser, accessToken } = await this.emailAuthProvider.verifyOTP(email, otp);

      // Create or get embedded user
      const embeddedUser = await this.createOrGetEmbeddedUser({
        email: emailUser.email,
        verified_email: emailUser.verified_email,
        authMethod: 'email' as const
      });

      // Generate or retrieve wallet address
      const walletAddress = await this.wallet.generateAddress(embeddedUser.id);

      this.isConnected = true;
      this.currentUser = embeddedUser;
      this.currentAccessToken = accessToken;
      this.currentWalletAddress = walletAddress.address;
      this.currentNetwork = walletAddress.network;
      this.currentChainId = walletAddress.chainId;

      const connection: EmbeddedConnection = {
        user: embeddedUser,
        accessToken,
        walletAddress: walletAddress.address,
        network: walletAddress.network,
        chainId: walletAddress.chainId
      };

      this.emit('connected', connection);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to verify OTP';
      this.setError(errorMessage);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    if (this.googleAuthProvider) {
      this.googleAuthProvider.revoke();
    }

    this.isConnected = false;
    this.currentUser = null;
    this.currentAccessToken = null;
    this.currentWalletAddress = null;
    this.currentNetwork = null;
    this.currentChainId = null;
    this.error = null;

    this.emit('disconnected');
  }

  public async signMessage(message: string): Promise<string> {
    if (!this.isConnected || !this.currentUser || !this.wallet) {
      throw new Error('Not connected to embedded wallet');
    }

    return this.wallet.signMessage(message, this.currentUser.id);
  }

  public async sendTransaction(transaction: Transaction): Promise<string> {
    if (!this.isConnected || !this.currentUser || !this.wallet) {
      throw new Error('Not connected to embedded wallet');
    }

    // Sign the transaction
    const signedTx = await this.wallet.signTransaction(transaction, this.currentUser.id);
    
    // Send the signed transaction
    return this.wallet.sendTransaction(signedTx);
  }

  public async getBalance(): Promise<string> {
    if (!this.currentWalletAddress || !this.wallet) {
      return '0.0';
    }

    const balance = await this.wallet.getBalance(this.currentWalletAddress);
    return balance.balance;
  }

  public getConnection(): EmbeddedConnection | null {
    if (!this.isConnected || !this.currentUser || !this.currentWalletAddress) {
      return null;
    }

    return {
      user: this.currentUser,
      accessToken: this.currentAccessToken,
      walletAddress: this.currentWalletAddress,
      network: this.currentNetwork || '',
      chainId: this.currentChainId
    };
  }

  public isWalletConnected(): boolean {
    return this.isConnected;
  }

  public getUser(): EmbeddedUser | null {
    return this.currentUser;
  }

  public getAccessToken(): string | null {
    return this.currentAccessToken;
  }

  public getWalletAddress(): string | null {
    return this.currentWalletAddress;
  }

  public getNetwork(): string | null {
    return this.currentNetwork;
  }

  public getChainId(): string | number | null {
    return this.currentChainId || null;
  }

  public getError(): string | null {
    return this.error;
  }

  private setError(error: string | null) {
    this.error = error;
    if (error) {
      this.emit('error', error);
    }
  }

  public destroy() {
    this.removeAllListeners();
    if (this.googleAuthProvider) {
      this.googleAuthProvider.destroy();
    }
    if (this.emailAuthProvider) {
      this.emailAuthProvider.destroy();
    }
  }
} 