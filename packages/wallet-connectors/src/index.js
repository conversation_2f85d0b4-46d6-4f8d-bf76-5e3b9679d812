// Main entry point for the wallet connector package
import WalletConnector from './js/WalletConnector.js';
import './css/wallet-connector.css';

// Import and export assets
import walletIcons, { getWalletIcon } from './assets/icons/index.js';

// Export the main class
export default WalletConnector;

// Named exports for convenience
export { WalletConnector };

// Export assets
export { walletIcons, getWalletIcon };

// Export types (if needed)
export const VERSION = '1.0.0';

// Auto-initialize for browser environments
if (typeof window !== 'undefined') {
  // Make WalletConnector globally available
  window.WalletConnector = WalletConnector;
  
  // Load CSS if not already loaded
  if (!document.querySelector('link[href*="wallet-connector.css"]')) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '/packages/wallet-connectors/dist/wallet-connector.css';
    document.head.appendChild(link);
  }
} 