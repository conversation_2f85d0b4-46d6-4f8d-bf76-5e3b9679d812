// Import all SVG icons as data URLs
import metamaskIcon from './metamask.svg';
import coinbaseIcon from './coinbase.svg';
import phantomIcon from './phantom.svg';
import backpackIcon from './backpack.svg';
import solflareIcon from './solflare.svg';
import defaultIcon from './default.svg';

// Export individual icons
export { metamaskIcon, coinbaseIcon, phantomIcon, backpackIcon, solflareIcon, defaultIcon };

// Icon mapping for easy access
export const walletIcons = {
  metamask: metamaskIcon,
  coinbase: coinbaseIcon,
  phantom: phantomIcon,
  backpack: backpackIcon,
  solflare: solflareIcon,
  default: defaultIcon
};

// Function to get icon
export function getWalletIcon(walletType) {
  return walletIcons[walletType] || walletIcons.default;
}

// Export all icons as default
export default walletIcons; 