import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * React Hook for Wallet Connector
 * Provides a React-friendly interface to the vanilla JS WalletConnector
 */
export function useWalletConnector(options = {}) {
  const [isConnected, setIsConnected] = useState(false);
  const [account, setAccount] = useState(null);
  const [network, setNetwork] = useState(null);
  const [error, setError] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  
  const connectorRef = useRef(null);
  const mountedRef = useRef(true);

  // Initialize connector
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Dynamically import the WalletConnector class
      import('../js/WalletConnector.js').then(({ default: WalletConnector }) => {
        if (mountedRef.current) {
          connectorRef.current = new WalletConnector(options);
          
          // Set up event listeners
          connectorRef.current.on('connected', (data) => {
            if (mountedRef.current) {
              setIsConnected(true);
              setAccount(data.account);
              setNetwork(data.network);
              setError(null);
              setIsConnecting(false);
            }
          });

          connectorRef.current.on('disconnected', () => {
            if (mountedRef.current) {
              setIsConnected(false);
              setAccount(null);
              setNetwork(null);
              setIsConnecting(false);
            }
          });

          connectorRef.current.on('accountChanged', (newAccount) => {
            if (mountedRef.current) {
              setAccount(newAccount);
            }
          });

          connectorRef.current.on('networkChanged', (newNetwork) => {
            if (mountedRef.current) {
              setNetwork(newNetwork);
            }
          });

          connectorRef.current.on('error', (error) => {
            if (mountedRef.current) {
              setError(error);
              setIsConnecting(false);
            }
          });

          // Check initial state
          const currentAccount = connectorRef.current.getAccount();
          if (currentAccount) {
            setIsConnected(true);
            setAccount(currentAccount);
            setNetwork(connectorRef.current.getNetwork());
          }
        }
      });
    }

    return () => {
      mountedRef.current = false;
      if (connectorRef.current) {
        // Clean up event listeners
        connectorRef.current.off('connected');
        connectorRef.current.off('disconnected');
        connectorRef.current.off('accountChanged');
        connectorRef.current.off('networkChanged');
        connectorRef.current.off('error');
      }
    };
  }, [options]);

  // Connect method
  const connect = useCallback(async (chain = 'ethereum', walletType = 'auto') => {
    if (!connectorRef.current) {
      throw new Error('Wallet connector not initialized');
    }

    setIsConnecting(true);
    setError(null);

    try {
      const account = await connectorRef.current.connect(chain, walletType);
      return account;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsConnecting(false);
    }
  }, []);

  // Disconnect method
  const disconnect = useCallback(async () => {
    if (!connectorRef.current) {
      return;
    }

    try {
      await connectorRef.current.disconnect();
    } catch (error) {
      setError(error.message);
    }
  }, []);

  // Show modal method
  const showModal = useCallback(() => {
    if (connectorRef.current) {
      connectorRef.current.show();
    }
  }, []);

  // Hide modal method
  const hideModal = useCallback(() => {
    if (connectorRef.current) {
      connectorRef.current.hide();
    }
  }, []);

  // Set theme method
  const setTheme = useCallback((theme) => {
    if (connectorRef.current) {
      connectorRef.current.setTheme(theme);
    }
  }, []);

  return {
    // State
    isConnected,
    account,
    network,
    error,
    isConnecting,
    
    // Methods
    connect,
    disconnect,
    showModal,
    hideModal,
    setTheme,
    
    // Direct access to connector instance
    connector: connectorRef.current,
  };
}

/**
 * React Component for Wallet Connect Button
 */
export function WalletConnectButton({ 
  children = 'Connect Wallet', 
  className = '', 
  chain = 'ethereum',
  walletType = 'auto',
  onConnect,
  onError,
  ...props 
}) {
  const { connect, isConnected, isConnecting, error } = useWalletConnector();

  const handleClick = async () => {
    try {
      const account = await connect(chain, walletType);
      if (onConnect) {
        onConnect(account);
      }
    } catch (error) {
      if (onError) {
        onError(error);
      }
    }
  };

  return (
    <button
      className={`wallet-connector-button ${className}`}
      onClick={handleClick}
      disabled={isConnecting}
      {...props}
    >
      {isConnecting ? 'Connecting...' : children}
    </button>
  );
}

/**
 * React Component for Wallet Status Display
 */
export function WalletStatus({ className = '', ...props }) {
  const { isConnected, account, network, disconnect } = useWalletConnector();

  if (!isConnected) {
    return (
      <div className={`wallet-connector-status disconnected ${className}`} {...props}>
        <span>Not Connected</span>
      </div>
    );
  }

  return (
    <div className={`wallet-connector-status connected ${className}`} {...props}>
      <span>Connected</span>
      {account && (
        <div className="wallet-connector-account">
          <span>{account.slice(0, 6)}...{account.slice(-4)}</span>
          {network && <span className="wallet-connector-account-address">({network})</span>}
        </div>
      )}
      <button onClick={disconnect} className="wallet-connector-button">
        Disconnect
      </button>
    </div>
  );
} 