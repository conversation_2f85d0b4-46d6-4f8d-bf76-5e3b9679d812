import { ChainConfig } from '../react/provider/WalletProvider';
import { walletIcons } from '../assets/icons';

// Ethereum Chain Configuration
export const ethereum: ChainConfig = {
  id: 'ethereum',
  name: 'Ethereum',
  symbol: 'ETH',
  icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHJ4PSI4IiBmaWxsPSIjNjI3NEVBIi8+CiAgPHBhdGggZD0iTTE2IDdDMjEuNSA3IDI2IDExLjUgMjYgMTdDMjYgMjIuNSAyMS41IDI3IDE2IDI3QzEwLjUgMjcgNiAyMi41IDYgMTdDNiAxMS41IDEwLjUgNyAxNiA3Wk0xNiA5QzExLjYgOSA4IDEyLjYgOCAxN0M4IDIxLjQgMTEuNiAyNSAxNiAyNUMyMC40IDI1IDI0IDIxLjQgMjQgMTdDMjQgMTIuNiAyMC40IDkgMTYgOVpNMTYgMTFDMTkuMyAxMSAyMiAxMy43IDIyIDE3QzIyIDIwLjMgMTkuMyAyMyAxNiAyM0MxMi43IDIzIDEwIDIwLjMgMTAgMTdDMTAgMTMuNyAxMi43IDExIDE2IDExWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+',
  nativeCurrency: {
    name: 'Ether',
    symbol: 'ETH',
    decimals: 18,
  },
  rpcUrls: ['https://mainnet.infura.io/v3/'],
  blockExplorerUrls: ['https://etherscan.io'],
  autoConnect: true,
  theme: 'dark',
  wallets: {
    metamask: {
      name: 'MetaMask',
      icon: walletIcons.metamask,
      isInstalled: true
    },
    coinbase: {
      name: 'Coinbase Wallet',
      icon: walletIcons.coinbase,
      isInstalled: true
    }
  }
};

// Solana Chain Configuration
export const solana: ChainConfig = {
  id: 'solana',
  name: '<PERSON><PERSON>',
  symbol: 'S<PERSON>',
  icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHJ4PSI4IiBmaWxsPSIjMDAwMDAwIi8+CiAgPHBhdGggZD0iTTE2IDdDMjEuNSA3IDI2IDExLjUgMjYgMTdDMjYgMjIuNSAyMS41IDI3IDE2IDI3QzEwLjUgMjcgNiAyMi41IDYgMTdDNiAxMS41IDEwLjUgNyAxNiA3Wk0xNiA5QzExLjYgOSA4IDEyLjYgOCAxN0M4IDIxLjQgMTEuNiAyNSAxNiAyNUMyMC40IDI1IDI0IDIxLjQgMjQgMTdDMjQgMTIuNiAyMC40IDkgMTYgOVpNMTYgMTFDMTkuMyAxMSAyMiAxMy43IDIyIDE3QzIyIDIwLjMgMTkuMyAyMyAxNiAyM0MxMi43IDIzIDEwIDIwLjMgMTAgMTdDMTAgMTMuNyAxMi43IDExIDE2IDExWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+',
  nativeCurrency: {
    name: '<PERSON><PERSON>',
    symbol: 'SOL',
    decimals: 9,
  },
  rpcUrls: ['https://api.mainnet-beta.solana.com'],
  blockExplorerUrls: ['https://explorer.solana.com'],
  autoConnect: true,
  theme: 'dark',
  wallets: {
    phantom: {
      name: 'Phantom',
      icon: walletIcons.phantom,
      isInstalled: true
    },
    backpack: {
      name: 'Backpack',
      icon: walletIcons.backpack,
      isInstalled: true
    },
    solflare: {
      name: 'Solflare',
      icon: walletIcons.solflare,
      isInstalled: true
    }
  }
};

// Polygon Chain Configuration
export const polygon: ChainConfig = {
  id: 'polygon',
  name: 'Polygon',
  symbol: 'MATIC',
  icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHJ4PSI4IiBmaWxsPSIjODI0N0U1Ii8+CiAgPHBhdGggZD0iTTE2IDdDMjEuNSA3IDI2IDExLjUgMjYgMTdDMjYgMjIuNSAyMS41IDI3IDE2IDI3QzEwLjUgMjcgNiAyMi41IDYgMTdDNiAxMS41IDEwLjUgNyAxNiA3Wk0xNiA5QzExLjYgOSA4IDEyLjYgOCAxN0M4IDIxLjQgMTEuNiAyNSAxNiAyNUMyMC40IDI1IDI0IDIxLjQgMjQgMTdDMjQgMTIuNiAyMC40IDkgMTYgOVpNMTYgMTFDMTkuMyAxMSAyMiAxMy43IDIyIDE3QzIyIDIwLjMgMTkuMyAyMyAxNiAyM0MxMi43IDIzIDEwIDIwLjMgMTAgMTdDMTAgMTMuNyAxMi43IDExIDE2IDExWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+',
  nativeCurrency: {
    name: 'MATIC',
    symbol: 'MATIC',
    decimals: 18,
  },
  rpcUrls: ['https://polygon-rpc.com'],
  blockExplorerUrls: ['https://polygonscan.com'],
  autoConnect: true,
  theme: 'dark',
  wallets: {
    metamask: {
      name: 'MetaMask',
      icon: walletIcons.metamask,
      isInstalled: true
    },
    coinbase: {
      name: 'Coinbase Wallet',
      icon: walletIcons.coinbase,
      isInstalled: true
    }
  }
};

// BSC Chain Configuration
export const bsc: ChainConfig = {
  id: 'bsc',
  name: 'BNB Smart Chain',
  symbol: 'BNB',
  icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHJ4PSI4IiBmaWxsPSIjRjNCQTA1Ii8+CiAgPHBhdGggZD0iTTE2IDdDMjEuNSA3IDI2IDExLjUgMjYgMTdDMjYgMjIuNSAyMS41IDI3IDE2IDI3QzEwLjUgMjcgNiAyMi41IDYgMTdDNiAxMS41IDEwLjUgNyAxNiA3Wk0xNiA5QzExLjYgOSA4IDEyLjYgOCAxN0M4IDIxLjQgMTEuNiAyNSAxNiAyNUMyMC40IDI1IDI0IDIxLjQgMjQgMTdDMjQgMTIuNiAyMC40IDkgMTYgOVpNMTYgMTFDMTkuMyAxMSAyMiAxMy43IDIyIDE3QzIyIDIwLjMgMTkuMyAyMyAxNiAyM0MxMi43IDIzIDEwIDIwLjMgMTAgMTdDMTAgMTMuNyAxMi43IDExIDE2IDExWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+',
  nativeCurrency: {
    name: 'BNB',
    symbol: 'BNB',
    decimals: 18,
  },
  rpcUrls: ['https://bsc-dataseed.binance.org'],
  blockExplorerUrls: ['https://bscscan.com'],
  autoConnect: true,
  theme: 'dark',
  wallets: {
    metamask: {
      name: 'MetaMask',
      icon: walletIcons.metamask,
      isInstalled: true
    },
    coinbase: {
      name: 'Coinbase Wallet',
      icon: walletIcons.coinbase,
      isInstalled: true
    }
  }
};

// Avalanche Chain Configuration
export const avalanche: ChainConfig = {
  id: 'avalanche',
  name: 'Avalanche',
  symbol: 'AVAX',
  icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHJ4PSI4IiBmaWxsPSIjRTA3MjM5Ii8+CiAgPHBhdGggZD0iTTE2IDdDMjEuNSA3IDI2IDExLjUgMjYgMTdDMjYgMjIuNSAyMS41IDI3IDE2IDI3QzEwLjUgMjcgNiAyMi41IDYgMTdDNiAxMS41IDEwLjUgNyAxNiA3Wk0xNiA5QzExLjYgOSA4IDEyLjYgOCAxN0M4IDIxLjQgMTEuNiAyNSAxNiAyNUMyMC40IDI1IDI0IDIxLjQgMjQgMTdDMjQgMTIuNiAyMC40IDkgMTYgOVpNMTYgMTFDMTkuMyAxMSAyMiAxMy43IDIyIDE3QzIyIDIwLjMgMTkuMyAyMyAxNiAyM0MxMi43IDIzIDEwIDIwLjMgMTAgMTdDMTAgMTMuNyAxMi43IDExIDE2IDExWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+',
  nativeCurrency: {
    name: 'AVAX',
    symbol: 'AVAX',
    decimals: 18,
  },
  rpcUrls: ['https://api.avax.network/ext/bc/C/rpc'],
  blockExplorerUrls: ['https://snowtrace.io'],
  autoConnect: true,
  theme: 'dark',
  wallets: {
    metamask: {
      name: 'MetaMask',
      icon: walletIcons.metamask,
      isInstalled: true
    },
    coinbase: {
      name: 'Coinbase Wallet',
      icon: walletIcons.coinbase,
      isInstalled: true
    }
  }
};

// Arbitrum Chain Configuration
export const arbitrum: ChainConfig = {
  id: 'arbitrum',
  name: 'Arbitrum One',
  symbol: 'ARB',
  icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHJ4PSI4IiBmaWxsPSIjMjhBM0Y5Ii8+CiAgPHBhdGggZD0iTTE2IDdDMjEuNSA3IDI2IDExLjUgMjYgMTdDMjYgMjIuNSAyMS41IDI3IDE2IDI3QzEwLjUgMjcgNiAyMi41IDYgMTdDNiAxMS41IDEwLjUgNyAxNiA3Wk0xNiA5QzExLjYgOSA4IDEyLjYgOCAxN0M4IDIxLjQgMTEuNiAyNSAxNiAyNUMyMC40IDI1IDI0IDIxLjQgMjQgMTdDMjQgMTIuNiAyMC40IDkgMTYgOVpNMTYgMTFDMTkuMyAxMSAyMiAxMy43IDIyIDE3QzIyIDIwLjMgMTkuMyAyMyAxNiAyM0MxMi43IDIzIDEwIDIwLjMgMTAgMTdDMTAgMTMuNyAxMi43IDExIDE2IDExWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+',
  nativeCurrency: {
    name: 'Ether',
    symbol: 'ETH',
    decimals: 18,
  },
  rpcUrls: ['https://arb1.arbitrum.io/rpc'],
  blockExplorerUrls: ['https://arbiscan.io'],
  autoConnect: true,
  theme: 'dark',
  wallets: {
    metamask: {
      name: 'MetaMask',
      icon: walletIcons.metamask,
      isInstalled: true
    },
    coinbase: {
      name: 'Coinbase Wallet',
      icon: walletIcons.coinbase,
      isInstalled: true
    }
  }
};

// Optimism Chain Configuration
export const optimism: ChainConfig = {
  id: 'optimism',
  name: 'Optimism',
  symbol: 'OP',
  icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHJ4PSI4IiBmaWxsPSIjRkYwNDIxIi8+CiAgPHBhdGggZD0iTTE2IDdDMjEuNSA3IDI2IDExLjUgMjYgMTdDMjYgMjIuNSAyMS41IDI3IDE2IDI3QzEwLjUgMjcgNiAyMi41IDYgMTdDNiAxMS41IDEwLjUgNyAxNiA3Wk0xNiA5QzExLjYgOSA4IDEyLjYgOCAxN0M4IDIxLjQgMTEuNiAyNSAxNiAyNUMyMC40IDI1IDI0IDIxLjQgMjQgMTdDMjQgMTIuNiAyMC40IDkgMTYgOVpNMTYgMTFDMTkuMyAxMSAyMiAxMy43IDIyIDE3QzIyIDIwLjMgMTkuMyAyMyAxNiAyM0MxMi43IDIzIDEwIDIwLjMgMTAgMTdDMTAgMTMuNyAxMi43IDExIDE2IDExWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+',
  nativeCurrency: {
    name: 'Ether',
    symbol: 'ETH',
    decimals: 18,
  },
  rpcUrls: ['https://mainnet.optimism.io'],
  blockExplorerUrls: ['https://optimistic.etherscan.io'],
  autoConnect: true,
  theme: 'dark',
  wallets: {
    metamask: {
      name: 'MetaMask',
      icon: walletIcons.metamask,
      isInstalled: true
    },
    coinbase: {
      name: 'Coinbase Wallet',
      icon: walletIcons.coinbase,
      isInstalled: true
    }
  }
};

export const chains = [
  ethereum,
  solana,
  polygon,
  bsc,
  avalanche,
  arbitrum,
  optimism
];

export {
  ethereum as mainnet,
  ethereum as ethereumMainnet,
  solana as solanaMainnet,
  polygon as polygonMainnet,
  bsc as bscMainnet,
  avalanche as avalancheMainnet,
  arbitrum as arbitrumMainnet,
  optimism as optimismMainnet
}; 