/* Wallet Connector Styles */
.wallet-connector-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.wallet-connector-content {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: wallet-connector-slide-in 0.3s ease-out;
}

.wallet-connector-dark .wallet-connector-content {
  background: #1f2937;
  color: #ffffff;
}

.wallet-connector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.wallet-connector-dark .wallet-connector-header {
  border-bottom-color: #374151;
}

.wallet-connector-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.wallet-connector-dark .wallet-connector-header h3 {
  color: #ffffff;
}

.wallet-connector-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s;
}

.wallet-connector-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.wallet-connector-dark .wallet-connector-close:hover {
  background: #374151;
  color: #d1d5db;
}

.wallet-connector-body {
  padding: 24px;
}

.wallet-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.wallet-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: #ffffff;
}

.wallet-connector-dark .wallet-option {
  border-color: #374151;
  background: #374151;
}

.wallet-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.wallet-connector-dark .wallet-option:hover {
  border-color: #60a5fa;
  background: #1f2937;
}

.wallet-option img {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  margin-right: 12px;
  flex-shrink: 0;
}

.wallet-option .wallet-name {
  font-weight: 600;
  font-size: 16px;
  color: #111827;
  margin-bottom: 2px;
}

.wallet-connector-dark .wallet-option .wallet-name {
  color: #ffffff;
}

.wallet-option .wallet-description {
  font-size: 14px;
  color: #6b7280;
}

.wallet-connector-dark .wallet-option .wallet-description {
  color: #9ca3af;
}

/* Animation */
@keyframes wallet-connector-slide-in {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive */
@media (max-width: 480px) {
  .wallet-connector-content {
    width: 95%;
    margin: 20px;
  }
  
  .wallet-connector-header {
    padding: 16px 20px;
  }
  
  .wallet-connector-body {
    padding: 20px;
  }
  
  .wallet-option {
    padding: 12px;
  }
}

/* Utility classes for framework integration */
.wallet-connector-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.wallet-connector-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.wallet-connector-button:active {
  transform: translateY(0);
}

.wallet-connector-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.wallet-connector-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.wallet-connector-status.connected {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.wallet-connector-status.disconnected {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.wallet-connector-dark .wallet-connector-status.connected {
  background: #064e3b;
  color: #6ee7b7;
  border-color: #059669;
}

.wallet-connector-dark .wallet-connector-status.disconnected {
  background: #450a0a;
  color: #fca5a5;
  border-color: #dc2626;
}

/* Account display */
.wallet-connector-account {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.wallet-connector-dark .wallet-connector-account {
  background: #374151;
  border-color: #4b5563;
  color: #d1d5db;
}

.wallet-connector-account-address {
  color: #6b7280;
}

.wallet-connector-dark .wallet-connector-account-address {
  color: #9ca3af;
} 