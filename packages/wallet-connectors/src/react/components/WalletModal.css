/* RainbowKit-inspired Modal Styles */

/* Native Dialog Modal */
.wallet-modal {
  /* Reset dialog defaults */
  border: none;
  border-radius: 20px;
  padding: 0;
  margin: auto;

  /* Modal styling */
  background: white;
  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05);
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;

  /* Animation */
  animation: modalSlideIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Dialog backdrop (native) */
.wallet-modal::backdrop {
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(12px);
}

/* Modal Header */
.wallet-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.wallet-modal-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: white;
  letter-spacing: -0.025em;
}

.wallet-modal-close {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 18px;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.wallet-modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.wallet-modal-close:active {
  transform: scale(0.95);
}

/* Modal Content */
.wallet-modal-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

/* Wallet List */
.wallet-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 0;
  padding: 0;
  list-style: none;
}

/* Wallet Button */
.wallet-button {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  padding: 16px 20px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
  font-family: inherit;
  font-size: 16px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.wallet-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 0;
}

.wallet-button:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -8px rgba(102, 126, 234, 0.4);
}

.wallet-button:hover::before {
  opacity: 0.05;
}

.wallet-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px -4px rgba(102, 126, 234, 0.3);
}

/* Wallet Icon */
.wallet-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  object-fit: cover;
  z-index: 1;
  position: relative;
  flex-shrink: 0;
}

/* Wallet Info */
.wallet-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  z-index: 1;
  position: relative;
  flex: 1;
}

.wallet-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.025em;
}

.wallet-chain {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

/* Arrow Icon */
.wallet-arrow {
  width: 20px;
  height: 20px;
  color: #94a3b8;
  transition: all 0.2s ease;
  z-index: 1;
  position: relative;
}

.wallet-button:hover .wallet-arrow {
  color: #667eea;
  transform: translateX(4px);
}

/* Error State */
.wallet-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.wallet-error-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* Empty State */
.wallet-empty {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

.wallet-empty-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 16px;
  opacity: 0.5;
}

.wallet-empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #475569;
  margin: 0 0 8px 0;
}

.wallet-empty-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

/* Loading State */
.wallet-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #64748b;
}

.wallet-loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

/* Animations */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .wallet-modal {
    background: #1e293b;
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .wallet-modal-header {
    border-bottom-color: #334155;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  }

  .wallet-button {
    background: #334155;
    border-color: #475569;
    color: #f1f5f9;
  }

  .wallet-button:hover {
    border-color: #6366f1;
    background: #3b4a5c;
  }

  .wallet-name {
    color: #f1f5f9;
  }

  .wallet-chain {
    color: #94a3b8;
  }

  .wallet-arrow {
    color: #64748b;
  }

  .wallet-button:hover .wallet-arrow {
    color: #6366f1;
  }

  .wallet-error {
    background: #450a0a;
    border-color: #dc2626;
    color: #fca5a5;
  }

  .wallet-empty-title {
    color: #cbd5e1;
  }

  .wallet-empty-description {
    color: #94a3b8;
  }

  .wallet-loading {
    color: #94a3b8;
  }

  .wallet-loading-spinner {
    border-color: #475569;
    border-top-color: #6366f1;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .wallet-modal {
    width: 95%;
    max-width: none;
    border-radius: 16px;
  }

  .wallet-modal-header {
    padding: 20px 20px 12px 20px;
  }

  .wallet-modal-content {
    padding: 20px;
  }

  .wallet-button {
    padding: 14px 16px;
  }

  .wallet-icon {
    width: 36px;
    height: 36px;
  }

  .wallet-name {
    font-size: 15px;
  }

  .wallet-chain {
    font-size: 13px;
  }
}

/* Focus States for Accessibility */
.wallet-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.wallet-modal-close:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

/* Smooth scrolling for content */
.wallet-modal-content {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.wallet-modal-content::-webkit-scrollbar {
  width: 6px;
}

.wallet-modal-content::-webkit-scrollbar-track {
  background: transparent;
}

.wallet-modal-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.wallet-modal-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
} 