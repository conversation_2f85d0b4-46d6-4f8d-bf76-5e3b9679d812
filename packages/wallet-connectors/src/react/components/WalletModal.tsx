import React, { useEffect, useRef } from 'react';
import { useWallet } from '../provider/WalletProvider';
import { MobileWalletDetector } from './MobileWalletDetector';
import './WalletModal.css';

// CSS-in-JS fallback styles
const modalStyles = `
.wallet-modal {
  border: none;
  border-radius: 20px;
  padding: 0;
  margin: auto;
  background: white;
  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05);
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.wallet-modal::backdrop {
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(12px);
}

.wallet-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.wallet-modal-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: white;
  letter-spacing: -0.025em;
}

.wallet-modal-close {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 18px;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.wallet-modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.wallet-modal-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.wallet-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 0;
  padding: 0;
  list-style: none;
}

.wallet-button {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  padding: 16px 20px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
  font-family: inherit;
  font-size: 16px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.wallet-button:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -8px rgba(102, 126, 234, 0.4);
}

.wallet-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  object-fit: cover;
  z-index: 1;
  position: relative;
  flex-shrink: 0;
}

.wallet-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  z-index: 1;
  position: relative;
  flex: 1;
}

.wallet-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.025em;
}

.wallet-chain {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.wallet-arrow {
  width: 20px;
  height: 20px;
  color: #94a3b8;
  transition: all 0.2s ease;
  z-index: 1;
  position: relative;
}

.wallet-button:hover .wallet-arrow {
  color: #667eea;
  transform: translateX(4px);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@media (prefers-color-scheme: dark) {
  .wallet-modal {
    background: #1e293b;
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .wallet-modal-header {
    border-bottom-color: #334155;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  }

  .wallet-button {
    background: #334155;
    border-color: #475569;
    color: #f1f5f9;
  }

  .wallet-button:hover {
    border-color: #6366f1;
    background: #3b4a5c;
  }

  .wallet-name {
    color: #f1f5f9;
  }

  .wallet-chain {
    color: #94a3b8;
  }

  .wallet-arrow {
    color: #64748b;
  }

  .wallet-button:hover .wallet-arrow {
    color: #6366f1;
  }
}
`;

export interface WalletModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function WalletModal({ isOpen, onClose }: WalletModalProps) {
  const { availableWallets, connect, isConnecting, error } = useWallet();
  const dialogRef = useRef<HTMLDialogElement>(null);

  // Inject styles if not already present
  useEffect(() => {
    if (!document.getElementById('wallet-modal-styles')) {
      const styleElement = document.createElement('style');
      styleElement.id = 'wallet-modal-styles';
      styleElement.textContent = modalStyles;
      document.head.appendChild(styleElement);
    }
  }, []);

  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    if (isOpen) {
      dialog.showModal();
      document.body.style.overflow = 'hidden';
    } else {
      dialog.close();
      document.body.style.overflow = 'unset';
    }
  }, [isOpen]);

  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    const handleEscape = () => { onClose(); };
    const handleClick = (e: Event) => { if (e.target === dialog) { onClose(); } };

    dialog.addEventListener('cancel', handleEscape);
    dialog.addEventListener('click', handleClick);

    return () => {
      dialog.removeEventListener('cancel', handleEscape);
      dialog.removeEventListener('click', handleClick);
    };
  }, [onClose]);

  const handleConnect = async (chainId: string, walletType: string) => {
    try {
      await connect(chainId, walletType);
      onClose();
    } catch (err) {
      console.error('Failed to connect wallet:', err);
    }
  };

  const allWallets = React.useMemo(() => {
    const wallets: Array<{
      chainId: string;
      chainName: string;
      type: string;
      name: string;
      icon: string;
      isInstalled: boolean;
    }> = [];
    
    if (availableWallets.ethereum && Array.isArray(availableWallets.ethereum)) {
      availableWallets.ethereum.forEach((wallet: any) => {
        wallets.push({ chainId: 'ethereum', chainName: 'Ethereum', ...wallet });
      });
    }
    
    if (availableWallets.solana && Array.isArray(availableWallets.solana)) {
      availableWallets.solana.forEach((wallet: any) => {
        wallets.push({ chainId: 'solana', chainName: 'Solana', ...wallet });
      });
    }
    
    return wallets;
  }, [availableWallets]);

  return (
    <dialog ref={dialogRef} className="wallet-modal">
      <div className="wallet-modal-header">
        <h2 className="wallet-modal-title">Connect Wallet</h2>
        <button 
          className="wallet-modal-close" 
          onClick={onClose}
          aria-label="Close modal"
        >
          ×
        </button>
      </div>
      
      <div className="wallet-modal-content">
        {isConnecting ? (
          <div className="wallet-loading">
            <div className="wallet-loading-spinner"></div>
            <span>Connecting...</span>
          </div>
        ) : error ? (
          <div className="wallet-error">
            <svg className="wallet-error-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span>{error}</span>
          </div>
        ) : allWallets.length === 0 ? (
          <div className="wallet-empty">
            <svg className="wallet-empty-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <h3 className="wallet-empty-title">No Wallets Found</h3>
            <p className="wallet-empty-description">
              Please install a supported wallet to connect to this application.
            </p>
          </div>
        ) : (
          <>
            {/* Mobile Wallet Detector */}
            <MobileWalletDetector
              onWalletSelect={(walletName) => {
                console.log('Selected mobile wallet:', walletName);
              }}
              onConnect={async (uri, walletName) => {
                try {
                  await handleConnect('ethereum', 'walletconnect');
                  return true;
                } catch (error) {
                  console.error('Mobile wallet connection failed:', error);
                  return false;
                }
              }}
            />
            
            {/* Desktop Wallets */}
            <ul className="wallet-list">
              {allWallets.map((wallet, index) => (
                <li key={`${wallet.chainId}-${wallet.type}-${index}`}>
                  <button
                    className="wallet-button"
                    onClick={() => handleConnect(wallet.chainId, wallet.type)}
                    disabled={!wallet.isInstalled}
                  >
                    <img 
                      src={wallet.icon} 
                      alt={`${wallet.name} icon`}
                      className="wallet-icon"
                    />
                    <div className="wallet-info">
                      <h3 className="wallet-name">{wallet.name}</h3>
                      <p className="wallet-chain">{wallet.chainName}</p>
                    </div>
                    <svg className="wallet-arrow" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </li>
              ))}
            </ul>
          </>
        )}
      </div>
    </dialog>
  );
} 