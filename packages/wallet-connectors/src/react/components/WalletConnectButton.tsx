import React, { useState } from 'react';
import { useWallet } from '../provider/WalletProvider';
import { WalletModal } from './WalletModal';
import { walletIcons } from '../../assets/icons';
import './WalletConnectButton.css';

interface WalletConnectButtonProps {
  className?: string;
  children?: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
}

export function WalletConnectButton({ 
  className = '', 
  children = 'Connect Wallet',
  variant = 'primary',
  size = 'md'
}: WalletConnectButtonProps) {
  const { isConnected, account } = useWallet();
  const [showModal, setShowModal] = useState(false);

  const handleClick = () => {
    setShowModal(true);
  };

  const buttonClass = `wallet-connect-button wallet-connect-button--${variant} wallet-connect-button--${size} ${className}`;

  return (
    <>
      <button className={buttonClass} onClick={handleClick}>
        {isConnected ? (
          <span className="wallet-connect-button__account">
            {account ? `${account.slice(0, 6)}...${account.slice(-4)}` : 'Connected'}
          </span>
        ) : (
          <>
            {/* <img 
              src={walletIcons.default} 
              alt="Wallet icon"
              className="wallet-connect-button__icon"
              style={{ width: '20px', height: '20px', marginRight: '8px' }}
            /> */}
            {children}
          </>
        )}
      </button>

      {showModal && (
        <WalletModal 
          isOpen={showModal} 
          onClose={() => setShowModal(false)} 
        />
      )}
    </>
  );
} 