/* Wallet Connect Button Base Styles */
.wallet-connect-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  outline: none;
  position: relative;
  overflow: hidden;
}

.wallet-connect-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.wallet-connect-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Size Variants */
.wallet-connect-button--sm {
  padding: 8px 16px;
  font-size: 14px;
  min-height: 36px;
}

.wallet-connect-button--md {
  padding: 12px 20px;
  font-size: 16px;
  min-height: 44px;
}

.wallet-connect-button--lg {
  padding: 16px 24px;
  font-size: 18px;
  min-height: 52px;
}

/* Style Variants */
.wallet-connect-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.wallet-connect-button--primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

.wallet-connect-button--primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.wallet-connect-button--secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.wallet-connect-button--secondary:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.wallet-connect-button--outline {
  background: transparent;
  color: #3b82f6;
  border: 2px solid #3b82f6;
}

.wallet-connect-button--outline:hover:not(:disabled) {
  background: #3b82f6;
  color: white;
}

/* Account Display */
.wallet-connect-button__account {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Wallet Icon */
.wallet-connect-button__icon {
  flex-shrink: 0;
  border-radius: 4px;
}

.wallet-connect-button__account::before {
  content: '';
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  display: inline-block;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .wallet-connect-button--secondary {
    background: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }

  .wallet-connect-button--secondary:hover:not(:disabled) {
    background: #4b5563;
    border-color: #6b7280;
  }

  .wallet-connect-button--outline {
    color: #60a5fa;
    border-color: #60a5fa;
  }

  .wallet-connect-button--outline:hover:not(:disabled) {
    background: #60a5fa;
    color: #1f2937;
  }
}

/* Loading State */
.wallet-connect-button--loading {
  position: relative;
  color: transparent;
}

.wallet-connect-button--loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: wallet-connect-button-spin 1s linear infinite;
}

@keyframes wallet-connect-button-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 