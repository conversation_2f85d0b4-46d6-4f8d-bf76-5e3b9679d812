import React, { useState, useEffect } from 'react';
import { useMobileDeepLinking } from '../hooks/useMobileDeepLinking';

export interface MobileWalletDetectorProps {
  onWalletSelect?: (walletName: string) => void;
  onConnect?: (uri: string, walletName: string) => Promise<boolean>;
  chainId?: string;
  className?: string;
  showDetectedOnly?: boolean;
}

export function MobileWalletDetector({
  onWalletSelect,
  onConnect,
  chainId,
  className = '',
  showDetectedOnly = false,
}: MobileWalletDetectorProps) {
  const {
    isMobile,
    isConnecting,
    detectedWallet,
    supportedWallets,
    error,
    platform,
    connect,
    detectWallet,
  } = useMobileDeepLinking({
    enabled: true,
    timeout: 3000,
    retryAttempts: 2,
  });

  const [localError, setLocalError] = useState<string | null>(null);

  useEffect(() => {
    if (isMobile) {
      detectWallet();
    }
  }, [isMobile, detectWallet]);

  const handleWalletClick = async (walletName: string) => {
    try {
      setLocalError(null);
      
      if (onWalletSelect) {
        onWalletSelect(walletName);
      }

      if (onConnect) {
        // This would be called with the actual URI from WalletConnect
        const success = await onConnect('mock-uri', walletName);
        if (!success) {
          setLocalError(`Failed to connect to ${walletName}`);
        }
      }
    } catch (error) {
      setLocalError(error instanceof Error ? error.message : 'Connection failed');
    }
  };

  if (!isMobile) {
    return null;
  }

  const walletsToShow = showDetectedOnly && detectedWallet 
    ? [detectedWallet]
    : supportedWallets;

  const displayError = error || localError;

  return (
    <div className={`mobile-wallet-detector ${className}`}>
      {detectedWallet && (
        <div className="mobile-wallet-detected">
          <div className="mobile-wallet-detected-icon">
            <svg fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="mobile-wallet-detected-content">
            <h4>Detected Wallet</h4>
            <p>{detectedWallet}</p>
          </div>
        </div>
      )}

      {displayError && (
        <div className="mobile-wallet-error">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span>{displayError}</span>
        </div>
      )}

      <div className="mobile-wallet-list">
        <h4 className="mobile-wallet-list-title">
          {detectedWallet ? 'Other Mobile Wallets' : 'Mobile Wallets'}
        </h4>
        
        {isConnecting && (
          <div className="mobile-wallet-loading">
            <div className="mobile-wallet-loading-spinner"></div>
            <span>Connecting...</span>
          </div>
        )}

        <div className="mobile-wallet-grid">
          {walletsToShow.map((walletName) => (
            <button
              key={walletName}
              className={`mobile-wallet-item ${detectedWallet === walletName ? 'mobile-wallet-detected' : ''}`}
              onClick={() => handleWalletClick(walletName)}
              disabled={isConnecting}
            >
              <div className="mobile-wallet-icon">
                {getWalletIcon(walletName)}
              </div>
              <div className="mobile-wallet-info">
                <span className="mobile-wallet-name">{getWalletDisplayName(walletName)}</span>
                {detectedWallet === walletName && (
                  <span className="mobile-wallet-detected-badge">Detected</span>
                )}
              </div>
            </button>
          ))}
        </div>
      </div>

      <style>{`
        .mobile-wallet-detector {
          padding: 16px;
          background: #f8fafc;
          border-radius: 12px;
          margin: 16px 0;
        }

        .mobile-wallet-detected {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          background: #ecfdf5;
          border: 1px solid #10b981;
          border-radius: 8px;
          margin-bottom: 16px;
        }

        .mobile-wallet-detected-icon {
          color: #10b981;
          width: 20px;
          height: 20px;
        }

        .mobile-wallet-detected-content h4 {
          margin: 0;
          font-size: 14px;
          font-weight: 600;
          color: #065f46;
        }

        .mobile-wallet-detected-content p {
          margin: 0;
          font-size: 12px;
          color: #047857;
        }

        .mobile-wallet-error {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px;
          background: #fef2f2;
          border: 1px solid #f87171;
          border-radius: 8px;
          margin-bottom: 16px;
          color: #dc2626;
          font-size: 14px;
        }

        .mobile-wallet-list-title {
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
        }

        .mobile-wallet-loading {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          padding: 16px;
          color: #64748b;
        }

        .mobile-wallet-loading-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid #e2e8f0;
          border-top: 2px solid #3b82f6;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .mobile-wallet-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
          gap: 8px;
        }

        .mobile-wallet-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          padding: 12px;
          background: white;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          text-decoration: none;
          color: inherit;
          font-family: inherit;
          font-size: 14px;
        }

        .mobile-wallet-item:hover {
          border-color: #3b82f6;
          background: #f8fafc;
        }

        .mobile-wallet-item:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .mobile-wallet-item.mobile-wallet-detected {
          border-color: #10b981;
          background: #f0fdf4;
        }

        .mobile-wallet-icon {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .mobile-wallet-info {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
        }

        .mobile-wallet-name {
          font-weight: 500;
          text-align: center;
        }

        .mobile-wallet-detected-badge {
          font-size: 10px;
          color: #10b981;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      `}</style>
    </div>
  );
}

function getWalletIcon(walletName: string): React.ReactNode {
  // Return wallet-specific icons
  const icons: { [key: string]: string } = {
    metamask: '🦊',
    trust: '🛡️',
    rainbow: '🌈',
    argent: '🛡️',
    coinbase: '🪙',
    phantom: '👻',
    solflare: '🔥',
    backpack: '🎒',
    imtoken: '📱',
    tokenpocket: '💼',
    bitget: '🪙',
    okx: '🪙',
  };

  return icons[walletName.toLowerCase()] || '💳';
}

function getWalletDisplayName(walletName: string): string {
  const names: { [key: string]: string } = {
    metamask: 'MetaMask',
    trust: 'Trust Wallet',
    rainbow: 'Rainbow',
    argent: 'Argent',
    coinbase: 'Coinbase Wallet',
    phantom: 'Phantom',
    solflare: 'Solflare',
    backpack: 'Backpack',
    imtoken: 'imToken',
    tokenpocket: 'TokenPocket',
    bitget: 'Bitget Wallet',
    okx: 'OKX Wallet',
  };

  return names[walletName.toLowerCase()] || walletName;
}