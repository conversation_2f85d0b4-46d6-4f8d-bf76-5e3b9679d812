import React from 'react';
import { EthereumConnector } from '../../ethereum/EthereumConnector';
import { SolanaConnector } from '../../solana/SolanaConnector';
import { walletIcons } from '../../assets/icons';

// Helper function to get wallet icon based on wallet type
const getWalletIcon = (walletType: string): string => {
  switch (walletType) {
    case 'metamask':
      return walletIcons.metamask;
    case 'coinbase':
      return walletIcons.coinbase;
    case 'phantom':
      return walletIcons.phantom;
    case 'backpack':
      return walletIcons.backpack;
    case 'solflare':
      return walletIcons.solflare;
    default:
      return walletIcons.default;
  }
};

// Chain configuration types
export interface ChainConfig {
  id: string;
  name: string;
  symbol: string;
  icon: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  rpcUrls: string[];
  blockExplorerUrls: string[];
  autoConnect?: boolean;
  theme?: 'light' | 'dark';
  wallets?: {
    [walletType: string]: {
      name: string;
      icon: string;
      isInstalled?: boolean;
    };
  };
}

export interface WalletConfig {
  type: string;
  name: string;
  icon: string;
  chainId: string;
}

export interface WalletProviderConfig {
  autoConnect?: boolean;
  theme?: 'light' | 'dark';
  chains: ChainConfig[];
  wallets?: WalletConfig[];
  ethereum?: {
    autoConnect?: boolean;
    theme?: 'light' | 'dark';
  };
  solana?: {
    autoConnect?: boolean;
    theme?: 'light' | 'dark';
  };
}

export interface WalletState {
  isConnected: boolean;
  isConnecting: boolean;
  account: string | null;
  chain: ChainConfig | null;
  network: string | null;
  error: string | null;
  connectedWalletType: string | null;
  availableWallets: {
    ethereum: any[];
    solana: any[];
  };
}

interface WalletContextValue extends WalletState {
  connect: (chain: string, walletType: string) => Promise<void>;
  disconnect: (chain?: string) => Promise<void>;
  config: WalletProviderConfig;
  connectors: {
    ethereum: EthereumConnector;
    solana: SolanaConnector;
  };
  connectedWalletIcon: string | null;
}

interface WalletProviderProps {
  children: React.ReactNode;
  config: WalletProviderConfig;
}

// Create context
const WalletContext = React.createContext<WalletContextValue | null>(null);

// Hook to use wallet context
export function useWallet() {
  const context = React.useContext(WalletContext);
  if (!context) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
}

// Hook for Ethereum-specific functionality
export function useEthereum() {
  const { connectors, ...state } = useWallet();
  
  return {
    ...state,
    availableWallets: state.availableWallets.ethereum,
    connect: (walletType: string) => state.connect('ethereum', walletType),
    disconnect: () => state.disconnect('ethereum'),
    connector: connectors.ethereum
  };
}

// Hook for Solana-specific functionality
export function useSolana() {
  const { connectors, ...state } = useWallet();
  
  return {
    ...state,
    availableWallets: state.availableWallets.solana,
    connect: (walletType: string) => state.connect('solana', walletType),
    disconnect: () => state.disconnect('solana'),
    connector: connectors.solana
  };
}

// Default chain configurations
const DEFAULT_CHAINS: ChainConfig[] = [
  {
    id: 'ethereum',
    name: 'Ethereum',
    symbol: 'ETH',
    icon: walletIcons.metamask, // Using default icon for Ethereum chain
    nativeCurrency: {
      name: 'Ether',
      symbol: 'ETH',
      decimals: 18,
    },
    rpcUrls: ['https://mainnet.infura.io/v3/'],
    blockExplorerUrls: ['https://etherscan.io'],
    autoConnect: true,
    theme: 'dark',
    wallets: {
      metamask: {
        name: 'MetaMask',
        icon: walletIcons.metamask,
        isInstalled: true
      },
      coinbase: {
        name: 'Coinbase Wallet',
        icon: walletIcons.coinbase,
        isInstalled: true
      }
    }
  },
  {
    id: 'solana',
    name: 'Solana',
    symbol: 'SOL',
    icon: walletIcons.default, // Using default icon for Solana chain
    nativeCurrency: {
      name: 'Solana',
      symbol: 'SOL',
      decimals: 9,
    },
    rpcUrls: ['https://api.mainnet-beta.solana.com'],
    blockExplorerUrls: ['https://explorer.solana.com'],
    autoConnect: true,
    theme: 'dark',
    wallets: {
      phantom: {
        name: 'Phantom',
        icon: walletIcons.phantom,
        isInstalled: true
      },
      backpack: {
        name: 'Backpack',
        icon: walletIcons.backpack,
        isInstalled: true
      },
      solflare: {
        name: 'Solflare',
        icon: walletIcons.solflare,
        isInstalled: true
      }
    }
  }
];

// WalletProvider Component
export function WalletProvider({ children, config }: WalletProviderProps) {
  const [state, setState] = React.useState<WalletState>({
    isConnected: false,
    isConnecting: false,
    account: null,
    chain: null,
    network: null,
    error: null,
    connectedWalletType: null,
    availableWallets: {
      ethereum: [],
      solana: []
    }
  });

  // Initialize connectors
  const connectors = React.useMemo(() => ({
    ethereum: new EthereumConnector(),
    solana: new SolanaConnector()
  }), []);

  // Detect available wallets on mount
  React.useEffect(() => {
    const detectWallets = () => {
      const ethereumWallets = connectors.ethereum.getAvailableWallets();
      const solanaWallets = connectors.solana.getAvailableWallets();
      
      setState(prev => ({
        ...prev,
        availableWallets: {
          ethereum: ethereumWallets,
          solana: solanaWallets
        }
      }));
    };

    // Detect wallets immediately
    detectWallets();

    // Also detect on window load in case wallets are injected later
    if (typeof window !== 'undefined') {
      window.addEventListener('load', detectWallets);
      return () => window.removeEventListener('load', detectWallets);
    }
  }, [connectors]);

  // Connect function
  const connect = React.useCallback(async (chainId: string, walletType: string) => {
    setState(prev => ({ ...prev, isConnecting: true, error: null }));
    
    try {
      // Find the chain configuration
      const chainConfig = config.chains.find(c => c.id === chainId);
      if (!chainConfig) {
        throw new Error(`Chain configuration not found for ${chainId}`);
      }

      if (chainId === 'ethereum') {
        await connectors.ethereum.connect(walletType as 'metamask' | 'coinbase');
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          chain: chainConfig,
          account: connectors.ethereum.getAccount(),
          network: connectors.ethereum.getNetwork(),
          connectedWalletType: walletType
        }));
      } else if (chainId === 'solana') {
        await connectors.solana.connect(walletType as 'phantom' | 'backpack' | 'solflare');
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          chain: chainConfig,
          account: connectors.solana.getAccount(),
          network: connectors.solana.getNetwork(),
          connectedWalletType: walletType
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isConnecting: false,
        error: error instanceof Error ? error.message : 'Connection failed'
      }));
    }
  }, [connectors, config.chains]);

  // Disconnect function
  const disconnect = React.useCallback(async (chain?: string) => {
    try {
      if (!chain || chain === 'ethereum') {
        await connectors.ethereum.disconnect();
      }
      if (!chain || chain === 'solana') {
        await connectors.solana.disconnect();
      }
      
      setState(prev => ({
        ...prev,
        isConnected: false,
        account: null,
        chain: null,
        network: null,
        error: null,
        connectedWalletType: null
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Disconnect failed'
      }));
    }
  }, [connectors]);

  // Context value
  const contextValue: WalletContextValue = {
    ...state,
    connect,
    disconnect,
    config,
    connectors,
    connectedWalletIcon: state.connectedWalletType ? getWalletIcon(state.connectedWalletType) : null
  };

  return (
    <WalletContext.Provider value={contextValue}>
      {children}
    </WalletContext.Provider>
  );
}