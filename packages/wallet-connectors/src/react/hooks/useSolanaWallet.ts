import { useState, useEffect, useCallback, useRef } from 'react';
import { SolanaConnector, SolanaConnection, SolanaWallet } from '../../solana/SolanaConnector';

export interface UseSolanaWalletReturn {
  // State
  isConnected: boolean;
  account: string | null;
  network: string | null;
  error: string | null;
  isConnecting: boolean;
  
  // Available wallets
  availableWallets: SolanaWallet[];
  
  // Actions
  connect: (walletType?: 'phantom' | 'backpack' | 'solflare') => Promise<void>;
  disconnect: () => Promise<void>;
  
  // Connector instance
  connector: SolanaConnector | null;
}

export function useSolanaWallet(config?: {
  autoConnect?: boolean;
  theme?: 'light' | 'dark';
}): UseSolanaWalletReturn {
  const [isConnected, setIsConnected] = useState(false);
  const [account, setAccount] = useState<string | null>(null);
  const [network, setNetwork] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [availableWallets, setAvailableWallets] = useState<SolanaWallet[]>([]);
  
  const connectorRef = useRef<SolanaConnector | null>(null);
  const mountedRef = useRef(true);

  // Initialize connector
  useEffect(() => {
    if (typeof window !== 'undefined') {
      connectorRef.current = new SolanaConnector(config);
      
      // Set up event listeners
      connectorRef.current.on('connected', (connection: SolanaConnection) => {
        if (mountedRef.current) {
          setIsConnected(true);
          setAccount(connection.account);
          setNetwork(connection.network);
          setError(null);
          setIsConnecting(false);
        }
      });

      connectorRef.current.on('disconnected', () => {
        if (mountedRef.current) {
          setIsConnected(false);
          setAccount(null);
          setNetwork(null);
          setIsConnecting(false);
        }
      });

      connectorRef.current.on('accountChanged', (newAccount: string) => {
        if (mountedRef.current) {
          setAccount(newAccount);
        }
      });

      connectorRef.current.on('error', (error: string) => {
        if (mountedRef.current) {
          setError(error);
          setIsConnecting(false);
        }
      });

      // Check initial state
      const currentAccount = connectorRef.current.getAccount();
      if (currentAccount) {
        setIsConnected(true);
        setAccount(currentAccount);
        setNetwork(connectorRef.current.getNetwork());
      }

      // Get available wallets
      setAvailableWallets(connectorRef.current.getAvailableWallets());
    }

    return () => {
      mountedRef.current = false;
      if (connectorRef.current) {
        connectorRef.current.removeAllListeners();
      }
    };
  }, [config]);

  const connect = useCallback(async (walletType: 'phantom' | 'backpack' | 'solflare' = 'phantom') => {
    if (!connectorRef.current) {
      return;
    }
    
    setIsConnecting(true);
    setError(null);
    
    try {
      await connectorRef.current.connect(walletType);
    } catch (err: any) {
      setError(err.message);
      setIsConnecting(false);
    }
  }, []);

  const disconnect = useCallback(async () => {
    if (!connectorRef.current) {
      return;
    }
    
    try {
      await connectorRef.current.disconnect();
    } catch (err: any) {
      setError(err.message);
    }
  }, []);

  return {
    isConnected,
    account,
    network,
    error,
    isConnecting,
    availableWallets,
    connect,
    disconnect,
    connector: connectorRef.current
  };
} 