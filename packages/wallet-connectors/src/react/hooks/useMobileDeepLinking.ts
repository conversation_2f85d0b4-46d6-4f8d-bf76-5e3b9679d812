import { useState, useEffect, useCallback } from 'react';
import { MobileDeepLinkingService, MobileDeepLinkingConfig } from '../../services/MobileDeepLinkingService';

export interface UseMobileDeepLinkingConfig {
  enabled?: boolean;
  fallbackUrl?: string;
  customSchemes?: {
    [walletName: string]: string;
  };
  timeout?: number;
  retryAttempts?: number;
}

export interface MobileDeepLinkingState {
  isMobile: boolean;
  isConnecting: boolean;
  detectedWallet: string | null;
  supportedWallets: string[];
  error: string | null;
  platform: 'ios' | 'android' | 'unknown';
}

export interface MobileDeepLinkingActions {
  connect: (uri: string, chainId?: string) => Promise<boolean>;
  detectWallet: () => Promise<string | null>;
  getSupportedWallets: () => string[];
  addCustomScheme: (name: string, scheme: string, chains?: string[]) => void;
}

export function useMobileDeepLinking(config: UseMobileDeepLinkingConfig = {}): MobileDeepLinkingState & MobileDeepLinkingActions {
  const [service, setService] = useState<MobileDeepLinkingService | null>(null);
  const [state, setState] = useState<MobileDeepLinkingState>({
    isMobile: false,
    isConnecting: false,
    detectedWallet: null,
    supportedWallets: [],
    error: null,
    platform: 'unknown',
  });

  // Initialize deep linking service
  useEffect(() => {
    if (config.enabled !== false) {
      const deepLinkingConfig: MobileDeepLinkingConfig = {
        enabled: true,
        fallbackUrl: config.fallbackUrl,
        customSchemes: config.customSchemes,
        timeout: config.timeout || 3000,
        retryAttempts: config.retryAttempts || 2,
      };

      const newService = new MobileDeepLinkingService(deepLinkingConfig);
      setService(newService);

      // Update initial state
      setState(prev => ({
        ...prev,
        isMobile: newService.getIsMobile(),
        supportedWallets: newService.getSupportedWallets().map(w => w.name),
        platform: newService.getPlatform(),
      }));
    }
  }, [config.enabled, config.fallbackUrl, config.customSchemes, config.timeout, config.retryAttempts]);

  // Auto-detect wallet on mount
  useEffect(() => {
    if (service && state.isMobile) {
      detectWallet();
    }
  }, [service, state.isMobile]);

  const detectWallet = useCallback(async (): Promise<string | null> => {
    if (!service) return null;

    try {
      setState(prev => ({ ...prev, error: null }));
      
      // This would need to be implemented in the service
      // For now, we'll simulate detection
      const detected = await service['detectInstalledWallet']();
      
      setState(prev => ({ 
        ...prev, 
        detectedWallet: detected 
      }));
      
      return detected;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to detect wallet';
      setState(prev => ({ 
        ...prev, 
        error: errorMessage 
      }));
      return null;
    }
  }, [service]);

  const connect = useCallback(async (uri: string, chainId?: string): Promise<boolean> => {
    if (!service) {
      setState(prev => ({ 
        ...prev, 
        error: 'Deep linking service not initialized' 
      }));
      return false;
    }

    try {
      setState(prev => ({ 
        ...prev, 
        isConnecting: true, 
        error: null 
      }));

      const success = await service.handleDeepLink(uri, chainId);
      
      setState(prev => ({ 
        ...prev, 
        isConnecting: false 
      }));

      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connection failed';
      setState(prev => ({ 
        ...prev, 
        isConnecting: false, 
        error: errorMessage 
      }));
      return false;
    }
  }, [service]);

  const getSupportedWallets = useCallback((): string[] => {
    return service?.getSupportedWallets().map(w => w.name) || [];
  }, [service]);

  const addCustomScheme = useCallback((name: string, scheme: string, chains: string[] = ['ethereum']): void => {
    service?.addCustomScheme(name, scheme, chains);
    setState(prev => ({
      ...prev,
      supportedWallets: service?.getSupportedWallets().map(w => w.name) || prev.supportedWallets,
    }));
  }, [service]);

  return {
    ...state,
    connect,
    detectWallet,
    getSupportedWallets,
    addCustomScheme,
  };
}