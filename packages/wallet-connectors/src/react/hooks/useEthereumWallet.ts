import { useState, useEffect, useCallback, useRef } from 'react';
import { EthereumConnector, EthereumConnection, EthereumWallet } from '../../ethereum/EthereumConnector';

export interface UseEthereumWalletReturn {
  // State
  isConnected: boolean;
  account: string | null;
  network: string | null;
  error: string | null;
  isConnecting: boolean;
  
  // Available wallets
  availableWallets: EthereumWallet[];
  
  // Actions
  connect: (walletType?: 'metamask' | 'coinbase' | 'walletconnect') => Promise<void>;
  disconnect: () => Promise<void>;
  
  // Connector instance
  connector: EthereumConnector | null;
}

export function useEthereumWallet(config?: {
  autoConnect?: boolean;
  theme?: 'light' | 'dark';
}): UseEthereumWalletReturn {
  const [isConnected, setIsConnected] = useState(false);
  const [account, setAccount] = useState<string | null>(null);
  const [network, setNetwork] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [availableWallets, setAvailableWallets] = useState<EthereumWallet[]>([]);
  
  const connectorRef = useRef<EthereumConnector | null>(null);
  const mountedRef = useRef(true);

  // Initialize connector
  useEffect(() => {
    if (typeof window !== 'undefined') {
      connectorRef.current = new EthereumConnector(config);
      
      // Set up event listeners
      connectorRef.current.on('connected', (connection: EthereumConnection) => {
        if (mountedRef.current) {
          setIsConnected(true);
          setAccount(connection.account);
          setNetwork(connection.network);
          setError(null);
          setIsConnecting(false);
        }
      });

      connectorRef.current.on('disconnected', () => {
        if (mountedRef.current) {
          setIsConnected(false);
          setAccount(null);
          setNetwork(null);
          setIsConnecting(false);
        }
      });

      connectorRef.current.on('accountChanged', (newAccount: string) => {
        if (mountedRef.current) {
          setAccount(newAccount);
        }
      });

      connectorRef.current.on('networkChanged', (newNetwork: string) => {
        if (mountedRef.current) {
          setNetwork(newNetwork);
        }
      });

      connectorRef.current.on('error', (error: string) => {
        if (mountedRef.current) {
          setError(error);
          setIsConnecting(false);
        }
      });

      // Check initial state
      const currentAccount = connectorRef.current.getAccount();
      if (currentAccount) {
        setIsConnected(true);
        setAccount(currentAccount);
        setNetwork(connectorRef.current.getNetwork());
      }

      // Get available wallets
      setAvailableWallets(connectorRef.current.getAvailableWallets());
    }

    return () => {
      mountedRef.current = false;
      if (connectorRef.current) {
        connectorRef.current.removeAllListeners();
      }
    };
  }, [config]);

  const connect = useCallback(async (walletType: 'metamask' | 'coinbase' | 'walletconnect' = 'metamask') => {
    if (!connectorRef.current) {
      return;
    }
    
    setIsConnecting(true);
    setError(null);
    
    try {
      await connectorRef.current.connect(walletType as 'metamask' | 'coinbase');
    } catch (err: any) {
      setError(err.message);
      setIsConnecting(false);
    }
  }, []);

  const disconnect = useCallback(async () => {
    if (!connectorRef.current) {
      return;
    }
    
    try {
      await connectorRef.current.disconnect();
    } catch (err: any) {
      setError(err.message);
    }
  }, []);

  return {
    isConnected,
    account,
    network,
    error,
    isConnecting,
    availableWallets,
    connect,
    disconnect,
    connector: connectorRef.current
  };
} 