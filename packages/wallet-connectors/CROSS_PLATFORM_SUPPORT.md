# 📱 Cross-Platform Mobile Deep Linking Support

This document explains how the mobile wallet deep linking feature works across iOS and Android platforms.

## 🎯 Platform Support Overview

The mobile deep linking implementation provides **native support for both iOS and Android** with platform-specific optimizations and fallback mechanisms.

## 📱 iOS Support

### **Deep Link Schemes**
iOS uses URL schemes for deep linking. The implementation supports:

```typescript
// iOS URL Schemes
'metamask://wc?uri='           // MetaMask
'trust://wc?uri='              // Trust Wallet
'rainbow://wc?uri='            // Rainbow
'argent://wc?uri='             // Argent
'coinbase-wallet://wc?uri='    // Coinbase Wallet
'phantom://wc?uri='            // Phantom (Solana)
```

### **iOS-Specific Features**
- **Universal Links** - Can be extended to support Apple's Universal Links
- **App Store Fallback** - Redirects to App Store if wallet app isn't installed
- **Safari Integration** - Works seamlessly with Safari's deep linking
- **iOS Detection** - Automatically detects iOS devices via user agent

### **iOS Configuration**
```typescript
const config = {
  mobileDeepLinking: {
    enabled: true,
    platformSpecific: {
      ios: {
        appStoreFallback: true,    // Redirect to App Store
        universalLinks: false       // Enable Universal Links
      }
    }
  }
};
```

## 🤖 Android Support

### **Deep Link Schemes**
Android uses intent URLs for deep linking. The implementation supports:

```typescript
// Android Intent URLs (same schemes work on Android)
'metamask://wc?uri='           // MetaMask
'trust://wc?uri='              // Trust Wallet
'rainbow://wc?uri='            // Rainbow
'argent://wc?uri='             // Argent
'coinbase-wallet://wc?uri='    // Coinbase Wallet
'phantom://wc?uri='            // Phantom (Solana)
```

### **Android-Specific Features**
- **Intent URLs** - Uses Android's intent system for deep linking
- **Package Detection** - Can detect installed Android apps
- **Play Store Fallback** - Redirects to Google Play Store if app isn't installed
- **Chrome Integration** - Works with Chrome's deep linking on Android

### **Android Configuration**
```typescript
const config = {
  mobileDeepLinking: {
    enabled: true,
    platformSpecific: {
      android: {
        playStoreFallback: true,   // Redirect to Play Store
        intentUrls: true           // Use Android intent URLs
      }
    }
  }
};
```

## 🔧 Platform Detection

The service automatically detects the platform:

```typescript
// Platform detection logic
private detectPlatform(): void {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (userAgent.includes('iphone') || userAgent.includes('ipad') || userAgent.includes('ipod')) {
    this.platform = 'ios';
  } else if (userAgent.includes('android')) {
    this.platform = 'android';
  } else {
    this.platform = 'unknown';
  }
}
```

## 📱 Platform-Specific Behavior

### **iOS Behavior**
1. **Deep Link Attempt** - Tries to open wallet app via URL scheme
2. **App Store Fallback** - If app not installed, redirects to App Store
3. **Safari Handling** - Works with Safari's deep link handling
4. **Universal Links** - Can be configured for Universal Links

### **Android Behavior**
1. **Deep Link Attempt** - Tries to open wallet app via intent URL
2. **Play Store Fallback** - If app not installed, redirects to Play Store
3. **Chrome Handling** - Works with Chrome's deep link handling
4. **Package Detection** - Can detect installed Android packages

## 🛠️ Implementation Details

### **Cross-Platform Deep Linking**
```typescript
// Same deep link works on both platforms
const deepLink = 'metamask://wc?uri=' + encodeURIComponent(uri);

// Platform-specific handling
if (platform === 'ios') {
  // iOS-specific logic
  window.location.href = deepLink;
} else if (platform === 'android') {
  // Android-specific logic
  window.location.href = deepLink;
}
```

### **Store Fallback URLs**
```typescript
// iOS App Store URLs
const appStoreUrls = {
  metamask: 'https://apps.apple.com/app/metamask/id1438144202',
  trust: 'https://apps.apple.com/app/trust-crypto-bitcoin-wallet/id1288339409',
  rainbow: 'https://apps.apple.com/app/rainbow-ethereum-wallet/id1457119021'
};

// Android Play Store URLs
const playStoreUrls = {
  metamask: 'https://play.google.com/store/apps/details?id=io.metamask',
  trust: 'https://play.google.com/store/apps/details?id=com.wallet.crypto.trustapp',
  rainbow: 'https://play.google.com/store/apps/details?id=me.rainbow'
};
```

## 🎣 React Hook Usage

### **Platform Information**
```tsx
import { useMobileDeepLinking } from '@tokai/wallet-connectors';

function MyComponent() {
  const { 
    isMobile, 
    platform, 
    isIOS, 
    isAndroid 
  } = useMobileDeepLinking();

  return (
    <div>
      {isMobile && (
        <div>
          <p>Platform: {platform}</p>
          {isIOS && <p>iOS-specific features available</p>}
          {isAndroid && <p>Android-specific features available</p>}
        </div>
      )}
    </div>
  );
}
```

### **Platform-Specific Actions**
```tsx
const { openWalletStore } = useMobileDeepLinking();

// Open store for specific wallet
const handleInstallWallet = async (walletName: string) => {
  const success = await openWalletStore(walletName);
  if (success) {
    console.log(`Redirected to ${platform} store for ${walletName}`);
  }
};
```

## 📊 Testing on Both Platforms

### **iOS Testing**
```bash
# Test on iOS Simulator
npx ios-simulator

# Test deep links on iOS
open "metamask://wc?uri=wc%3A..."

# Test App Store fallback
open "https://apps.apple.com/app/metamask/id1438144202"
```

### **Android Testing**
```bash
# Test on Android Emulator
npx android-emulator

# Test deep links on Android
adb shell am start -W -a android.intent.action.VIEW -d "metamask://wc?uri=wc%3A..."

# Test Play Store fallback
open "https://play.google.com/store/apps/details?id=io.metamask"
```

### **Browser Dev Tools**
```javascript
// Simulate iOS
navigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15';

// Simulate Android
navigator.userAgent = 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36';
```

## 🔍 Platform-Specific Debugging

### **iOS Debugging**
```typescript
// Check iOS-specific features
const isIOS = platform === 'ios';
const hasAppStoreFallback = config.platformSpecific?.ios?.appStoreFallback;

console.log('iOS Features:', {
  isIOS,
  hasAppStoreFallback,
  userAgent: navigator.userAgent
});
```

### **Android Debugging**
```typescript
// Check Android-specific features
const isAndroid = platform === 'android';
const hasPlayStoreFallback = config.platformSpecific?.android?.playStoreFallback;

console.log('Android Features:', {
  isAndroid,
  hasPlayStoreFallback,
  userAgent: navigator.userAgent
});
```

## 🚀 Production Considerations

### **iOS Production**
- **App Store URLs** - All wallet apps have valid App Store URLs
- **Universal Links** - Can be configured for better UX
- **Safari Compatibility** - Tested with Safari's deep link handling
- **iOS Version Support** - Works with iOS 12+ (deep linking support)

### **Android Production**
- **Play Store URLs** - All wallet apps have valid Play Store URLs
- **Chrome Compatibility** - Tested with Chrome's deep link handling
- **Intent System** - Uses Android's native intent system
- **Android Version Support** - Works with Android 6+ (API 23+)

## 📱 Supported Wallets by Platform

### **iOS Supported Wallets**
- ✅ MetaMask
- ✅ Trust Wallet
- ✅ Rainbow
- ✅ Argent
- ✅ Coinbase Wallet
- ✅ imToken
- ✅ TokenPocket
- ✅ Bitget Wallet
- ✅ OKX Wallet
- ✅ Phantom (Solana)
- ✅ Solflare (Solana)
- ✅ Backpack (Solana)

### **Android Supported Wallets**
- ✅ MetaMask
- ✅ Trust Wallet
- ✅ Rainbow
- ✅ Argent
- ✅ Coinbase Wallet
- ✅ imToken
- ✅ TokenPocket
- ✅ Bitget Wallet
- ✅ OKX Wallet
- ✅ Phantom (Solana)
- ✅ Solflare (Solana)
- ✅ Backpack (Solana)

## 🔧 Configuration Examples

### **Basic Cross-Platform Config**
```typescript
const config = {
  mobileDeepLinking: {
    enabled: true,
    timeout: 3000,
    retryAttempts: 2
  }
};
```

### **Advanced Platform-Specific Config**
```typescript
const config = {
  mobileDeepLinking: {
    enabled: true,
    timeout: 3000,
    retryAttempts: 2,
    platformSpecific: {
      ios: {
        appStoreFallback: true,
        universalLinks: false
      },
      android: {
        playStoreFallback: true,
        intentUrls: true
      }
    }
  }
};
```

### **Custom Wallet Schemes**
```typescript
const config = {
  mobileDeepLinking: {
    enabled: true,
    customSchemes: {
      'mywallet': 'mywallet://wc?uri=',
      'customapp': 'customapp://connect?uri='
    }
  }
};
```

## 🎯 Summary

The mobile deep linking feature provides **comprehensive cross-platform support** with:

- ✅ **iOS Support** - Native iOS deep linking with App Store fallback
- ✅ **Android Support** - Native Android deep linking with Play Store fallback
- ✅ **Platform Detection** - Automatic iOS/Android detection
- ✅ **Unified API** - Same API works on both platforms
- ✅ **Store Fallbacks** - Automatic redirect to appropriate app store
- ✅ **12+ Wallets** - Support for popular wallets on both platforms
- ✅ **Custom Schemes** - Support for custom wallet deep link schemes

The implementation ensures a **seamless mobile experience** across both iOS and Android platforms, with appropriate fallbacks and platform-specific optimizations.