# 📱 Mobile Wallet Deep Linking

This document provides comprehensive information about the mobile wallet deep linking feature in Tokai's wallet connectors.

## 🎯 Overview

The mobile deep linking feature enables seamless connection to mobile wallet apps through deep links, providing a native mobile experience for users. It automatically detects mobile devices and attempts to open the appropriate wallet app when connecting.

## ✨ Features

- **Automatic Mobile Detection** - Detects mobile devices and adjusts behavior accordingly
- **Wallet Detection** - Identifies installed wallet apps on the device
- **Multi-Wallet Support** - Supports 12+ popular mobile wallets
- **Fallback Handling** - Graceful fallback to Web3Modal if deep linking fails
- **Custom Schemes** - Support for custom wallet deep link schemes
- **Chain-Specific Wallets** - Different wallet options based on blockchain
- **Retry Logic** - Multiple attempts with configurable timeouts

## 🚀 Quick Start

### Basic Usage

```tsx
import { WalletProvider, MobileWalletDetector } from '@tokai/wallet-connectors';

function App() {
  return (
    <WalletProvider config={{
      chains: [1, 137, 56], // Ethereum, Polygon, BSC
      mobileDeepLinking: {
        enabled: true,
        timeout: 3000,
        retryAttempts: 2
      }
    }}>
      <MobileWalletDetector
        onWalletSelect={(walletName) => {
          console.log('Selected wallet:', walletName);
        }}
        onConnect={async (uri, walletName) => {
          // Handle connection logic
          return true;
        }}
      />
    </WalletProvider>
  );
}
```

### Advanced Configuration

```tsx
import { WalletProvider } from '@tokai/wallet-connectors';

const config = {
  projectId: 'your-walletconnect-project-id',
  chains: [1, 137, 56, 43114], // Ethereum, Polygon, BSC, Avalanche
  mobileDeepLinking: {
    enabled: true,
    fallbackUrl: 'https://your-app.com/fallback',
    customSchemes: {
      'mywallet': 'mywallet://wc?uri=',
      'customapp': 'customapp://connect?uri='
    },
    timeout: 5000,
    retryAttempts: 3
  }
};

<WalletProvider config={config}>
  {/* Your app */}
</WalletProvider>
```

## 📱 Supported Wallets

### Ethereum Wallets
- **MetaMask** - `metamask://wc?uri=`
- **Trust Wallet** - `trust://wc?uri=`
- **Rainbow** - `rainbow://wc?uri=`
- **Argent** - `argent://wc?uri=`
- **Coinbase Wallet** - `coinbase-wallet://wc?uri=`
- **imToken** - `imtokenv2://navigate/DappView?url=`
- **TokenPocket** - `tpdapp://wc?uri=`
- **Bitget Wallet** - `bitget://wc?uri=`
- **OKX Wallet** - `okx://wc?uri=`

### Solana Wallets
- **Phantom** - `phantom://wc?uri=`
- **Solflare** - `solflare://wc?uri=`
- **Backpack** - `backpack://wc?uri=`

## 🔧 Configuration Options

### WalletConnectConfig.mobileDeepLinking

```typescript
interface MobileDeepLinkingConfig {
  enabled: boolean;                    // Enable/disable deep linking
  fallbackUrl?: string;                // Fallback URL if deep linking fails
  customSchemes?: {                    // Custom wallet schemes
    [walletName: string]: string;
  };
  timeout?: number;                    // Timeout for deep link attempts (ms)
  retryAttempts?: number;              // Number of retry attempts
}
```

### MobileWalletDetector Props

```typescript
interface MobileWalletDetectorProps {
  onWalletSelect?: (walletName: string) => void;
  onConnect?: (uri: string, walletName: string) => Promise<boolean>;
  chainId?: string;
  className?: string;
  showDetectedOnly?: boolean;
}
```

## 🎣 React Hooks

### useMobileDeepLinking

```tsx
import { useMobileDeepLinking } from '@tokai/wallet-connectors';

function MyComponent() {
  const {
    isMobile,
    isConnecting,
    detectedWallet,
    supportedWallets,
    error,
    connect,
    detectWallet,
    getSupportedWallets,
    addCustomScheme
  } = useMobileDeepLinking({
    enabled: true,
    timeout: 3000,
    retryAttempts: 2
  });

  const handleConnect = async () => {
    const success = await connect('wc:uri...', 'ethereum');
    if (success) {
      console.log('Connected via deep link!');
    }
  };

  return (
    <div>
      {isMobile && (
        <div>
          <p>Mobile device detected</p>
          {detectedWallet && <p>Detected wallet: {detectedWallet}</p>}
          <button onClick={handleConnect}>Connect</button>
        </div>
      )}
    </div>
  );
}
```

## 🔄 Integration with WalletConnect

The mobile deep linking feature integrates seamlessly with WalletConnect v2:

```tsx
import { WalletConnectProvider } from '@tokai/wallet-connectors';

const wcProvider = new WalletConnectProvider({
  projectId: 'your-project-id',
  chains: [1, 137, 56],
  mobileDeepLinking: {
    enabled: true,
    timeout: 3000
  }
});

// The provider automatically handles deep linking when connecting
const { uri, approval } = await wcProvider.connect();
// Deep linking is handled automatically in handleMobileDeepLink()
```

## 🎨 UI Components

### MobileWalletDetector

A pre-built component that displays mobile wallet options:

```tsx
<MobileWalletDetector
  onWalletSelect={(walletName) => {
    console.log('User selected:', walletName);
  }}
  onConnect={async (uri, walletName) => {
    // Handle the connection
    const success = await connectWallet(uri, walletName);
    return success;
  }}
  chainId="ethereum"
  showDetectedOnly={false}
/>
```

Features:
- **Automatic Detection** - Shows detected wallet prominently
- **Grid Layout** - Clean grid of available wallets
- **Loading States** - Shows connection progress
- **Error Handling** - Displays connection errors
- **Responsive Design** - Works on all screen sizes

## 🛠️ Custom Implementation

### Creating Custom Deep Linking Logic

```tsx
import { MobileDeepLinkingService } from '@tokai/wallet-connectors';

const deepLinkingService = new MobileDeepLinkingService({
  enabled: true,
  customSchemes: {
    'mywallet': 'mywallet://wc?uri='
  }
});

// Add custom wallet scheme
deepLinkingService.addCustomScheme('customwallet', 'custom://wc?uri=', ['ethereum']);

// Handle deep linking
const success = await deepLinkingService.handleDeepLink(uri, 'ethereum');
```

### Custom Wallet Detection

```tsx
// Extend the service for custom wallet detection
class CustomDeepLinkingService extends MobileDeepLinkingService {
  protected async detectInstalledWallet(): Promise<string | null> {
    // Custom detection logic
    if (window.customWallet) {
      return 'customwallet';
    }
    
    // Fall back to parent implementation
    return super.detectInstalledWallet();
  }
}
```

## 🔍 Debugging

### Enable Debug Logging

```tsx
const deepLinkingService = new MobileDeepLinkingService({
  enabled: true,
  timeout: 3000
});

// Add debug logging
deepLinkingService.on('debug', (message) => {
  console.log('Deep linking debug:', message);
});
```

### Common Issues

1. **Deep link not opening wallet**
   - Check if wallet app is installed
   - Verify deep link scheme is correct
   - Ensure URI is properly encoded

2. **Fallback not working**
   - Check if Web3Modal is properly initialized
   - Verify fallback URL is accessible

3. **Mobile detection issues**
   - Check user agent string
   - Verify screen dimensions
   - Test on actual mobile devices

## 📊 Performance Considerations

- **Timeout Configuration** - Balance between responsiveness and reliability
- **Retry Logic** - Multiple attempts with exponential backoff
- **Memory Management** - Clean up event listeners and iframes
- **Network Fallback** - Graceful degradation when deep linking fails

## 🔒 Security Best Practices

1. **URI Validation** - Always validate URIs before processing
2. **Origin Checking** - Verify deep link origins
3. **Error Handling** - Don't expose sensitive information in errors
4. **Fallback Security** - Ensure fallback URLs are secure

## 📱 Testing

### Mobile Device Testing

```bash
# Test on iOS Simulator
npx ios-simulator

# Test on Android Emulator
npx android-emulator

# Test on physical device
# Use browser dev tools mobile simulation
```

### Deep Link Testing

```bash
# Test MetaMask deep link
open "metamask://wc?uri=wc%3A..."

# Test Trust Wallet deep link
open "trust://wc?uri=wc%3A..."
```

## 🚀 Production Deployment

### Environment Variables

```bash
# Required
WALLETCONNECT_PROJECT_ID=your-project-id

# Optional
MOBILE_DEEP_LINKING_ENABLED=true
MOBILE_DEEP_LINKING_TIMEOUT=3000
MOBILE_DEEP_LINKING_RETRY_ATTEMPTS=2
```

### Monitoring

```tsx
// Add analytics for deep linking success rates
const trackDeepLinkSuccess = (walletName: string, success: boolean) => {
  analytics.track('deep_link_attempt', {
    wallet: walletName,
    success,
    timestamp: Date.now()
  });
};
```

## 📚 API Reference

### MobileDeepLinkingService

```typescript
class MobileDeepLinkingService {
  constructor(config: MobileDeepLinkingConfig)
  
  async handleDeepLink(uri: string, chainId?: string): Promise<boolean>
  getIsMobile(): boolean
  getSupportedWallets(): WalletScheme[]
  addCustomScheme(name: string, scheme: string, chains?: string[]): void
}
```

### useMobileDeepLinking Hook

```typescript
function useMobileDeepLinking(config?: UseMobileDeepLinkingConfig): {
  isMobile: boolean
  isConnecting: boolean
  detectedWallet: string | null
  supportedWallets: string[]
  error: string | null
  connect: (uri: string, chainId?: string) => Promise<boolean>
  detectWallet: () => Promise<string | null>
  getSupportedWallets: () => string[]
  addCustomScheme: (name: string, scheme: string, chains?: string[]) => void
}
```

## 🤝 Contributing

To add support for new mobile wallets:

1. **Add Wallet Scheme** - Add the deep link scheme to `MobileDeepLinkingService`
2. **Update Detection** - Add detection logic for the wallet
3. **Add Icons** - Include wallet icons in the UI components
4. **Test Thoroughly** - Test on actual mobile devices
5. **Update Documentation** - Add the wallet to this documentation

## 📄 License

This feature is part of the Tokai wallet connectors package and follows the same license terms.