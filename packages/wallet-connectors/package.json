{"name": "@tokai/wallet-connectors", "version": "1.0.0", "description": "Tokai multi-chain wallet connectors for React with hooks and provider pattern", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "tsc && pnpm run copy-assets", "dev": "tsc --watch", "copy-assets": "cp -r src/assets dist/ && cp src/css/*.css dist/ && cp src/react/components/*.css dist/react/components/", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "keywords": ["wallet", "connector", "ethereum", "solana", "react", "hooks", "metamask", "phantom"], "author": "Tokai Team", "license": "MIT", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@types/react": "^18.3.1", "@types/react-dom": "^18.2.22", "typescript": "^5.0.0"}, "repository": {"type": "git", "url": "https://github.com/tokai/wallet-connectors"}, "bugs": {"url": "https://github.com/tokai/wallet-connectors/issues"}, "homepage": "https://github.com/tokai/wallet-connectors#readme"}