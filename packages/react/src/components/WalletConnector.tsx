import React, { useState } from 'react';
// import { 
//   WalletProvider, 
//   WalletConnectButton, 
//   useWallet 
// } from '@tokai/wallet-connectors';
import { useTokai } from '../context';
import { TokaiApi } from '../api';

interface WalletConnectorProps {
  apiKey?: string;
  backendUrl?: string;
  theme?: 'light' | 'dark';
  chains?: string[];
  children?: React.ReactNode;
}

// Wallet configuration
const _getWalletConfig = (theme: 'light' | 'dark' = 'dark') => ({
  autoConnect: true,
  theme,
  chains: [
    {
      id: 'ethereum',
      name: 'Ethereum',
      symbol: 'ETH',
      icon: '/icons/ethereum.svg',
      nativeCurrency: {
        name: 'Ether',
        symbol: 'ETH',
        decimals: 18,
      },
      rpcUrls: ['https://mainnet.infura.io/v3/your-project-id'],
      blockExplorerUrls: ['https://etherscan.io'],
      autoConnect: true,
      theme,
    },
    {
      id: 'solana',
      name: '<PERSON><PERSON>',
      symbol: '<PERSON><PERSON>',
      icon: '/icons/solana.svg',
      nativeCurrency: {
        name: '<PERSON><PERSON>',
        symbol: 'S<PERSON>',
        decimals: 9,
      },
      rpcUrls: ['https://api.mainnet-beta.solana.com'],
      blockExplorerUrls: ['https://explorer.solana.com'],
      autoConnect: true,
      theme,
    },
  ],
  ethereum: {
    autoConnect: true,
    theme
  },
  solana: {
    autoConnect: true,
    theme
  }
});

// Main Wallet Connector Component
export const TokaiWalletConnector: React.FC<WalletConnectorProps> = ({
  apiKey,
  backendUrl = 'http://localhost:3001',
  theme = 'dark',
  chains = ['ethereum', 'solana'],
  children
}) => {
  const [_tokaiApi] = useState(() => new TokaiApi({ apiUrl: backendUrl, apiKey: apiKey || '' }));
  
  return (
    <div>
      {/* <WalletProvider config={getWalletConfig(theme)}>
        <TokaiWalletIntegration api={tokaiApi}>
          {children}
        </TokaiWalletIntegration>
      </WalletProvider> */}
      {children}
    </div>
  );
};

// Internal component that handles the integration
const _TokaiWalletIntegration: React.FC<{
  api: TokaiApi;
  children?: React.ReactNode;
}> = ({ api, children }) => {
  // const { isConnected, account, chain, disconnect } = useWallet();
  const { user: _user, wallets: _wallets, createWallet: _createWallet, isLoading: _isLoading } = useTokai();

  // Sync wallet connection with backend
  // useEffect(() => {
  //   if (isConnected && account && user) {
  //     // Check if wallet exists in backend, if not create it
  //     const walletExists = wallets.some(w => w.wallet_address.toLowerCase() === account.toLowerCase());
  //     if (!walletExists) {
  //       createWallet(chain?.name?.toLowerCase() || 'ethereum');
  //     }
  //   }
  // }, [isConnected, account, user, wallets, createWallet, chain]);

  return (
    <div className="tokai-wallet-integration">
      {children}
      
      {/* Wallet Connection UI */}
      <div className="tokai-wallet-ui">
        {/* {!isConnected ? (
          <WalletConnectButton variant="primary" size="lg">
            Connect Wallet
          </WalletConnectButton>
        ) : (
          <div className="tokai-wallet-info">
            <div className="tokai-wallet-account">
              <span>Connected: {account?.substring(0, 6)}...{account?.substring(account.length - 4)}</span>
              {chain && (
                <span className="tokai-wallet-chain">
                  {chain.name} ({chain.symbol})
                </span>
              )}
            </div>
            <button 
              onClick={() => disconnect()}
              className="tokai-disconnect-btn"
              disabled={isLoading}
            >
              Disconnect
            </button>
          </div>
        )} */}
        <div>Wallet connector placeholder</div>
      </div>
    </div>
  );
};

// Standalone Wallet Connect Button
export const TokaiConnectButton: React.FC<{
  apiKey?: string;
  backendUrl?: string;
  theme?: 'light' | 'dark';
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
}> = ({ 
  apiKey, 
  backendUrl = 'http://localhost:3001', 
  theme = 'dark',
  variant = 'primary',
  size = 'md'
}) => {
  return (
    <div>
      {/* <WalletProvider config={getWalletConfig(theme)}>
        <WalletConnectButton variant={variant} size={size}>
          Connect Wallet
        </WalletConnectButton>
      </WalletProvider> */}
      <button>Connect Wallet</button>
    </div>
  );
};

// Wallet Dashboard Component
export const TokaiWalletDashboard: React.FC<{
  apiKey?: string;
  backendUrl?: string;
}> = ({ apiKey, backendUrl = 'http://localhost:3001' }) => {
  // const { isConnected, account, chain } = useWallet();
  const { user, wallets, createWallet, isLoading } = useTokai();

  const handleCreateWallet = async (network: string) => {
    if (!user) return;
    await createWallet(network);
  };

  if (!user) {
    return <div>Please login to access wallet dashboard</div>;
  }

  return (
    <div className="tokai-wallet-dashboard">
      <h2>Wallet Dashboard</h2>
      
      {/* Connection Status */}
      <div className="tokai-connection-status">
        <h3>Connection Status</h3>
        {/* {isConnected ? (
          <div className="tokai-connected">
            <p>✅ Connected to {chain?.name}</p>
            <p>Account: {account}</p>
          </div>
        ) : (
          <div className="tokai-disconnected">
            <p>❌ Not connected</p>
            <TokaiConnectButton apiKey={apiKey} backendUrl={backendUrl} />
          </div>
        )} */}
        <div className="tokai-disconnected">
          <p>❌ Not connected</p>
          <TokaiConnectButton apiKey={apiKey} backendUrl={backendUrl} />
        </div>
      </div>

      {/* Backend Wallets */}
      <div className="tokai-backend-wallets">
        <h3>Your Wallets</h3>
        {wallets.length > 0 ? (
          <div className="tokai-wallet-list">
            {wallets.map((wallet) => (
              <div key={wallet.id} className="tokai-wallet-item">
                <span>{wallet.network}</span>
                <span>{wallet.wallet_address.substring(0, 6)}...{wallet.wallet_address.substring(wallet.wallet_address.length - 4)}</span>
                <span>{wallet.is_active ? '✅ Active' : '❌ Inactive'}</span>
              </div>
            ))}
          </div>
        ) : (
          <p>No wallets found. Create one below.</p>
        )}
      </div>

      {/* Create Wallet */}
      <div className="tokai-create-wallet">
        <h3>Create New Wallet</h3>
        <div className="tokai-wallet-options">
          {['ethereum', 'solana', 'polygon', 'bsc', 'avalanche'].map((network) => (
            <button
              key={network}
              onClick={() => handleCreateWallet(network)}
              disabled={isLoading}
              className="tokai-create-btn"
            >
              Create {network.charAt(0).toUpperCase() + network.slice(1)} Wallet
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}; 