import React from 'react';
import { useTokai } from '../context';
import { TokaiAuth } from './TokaiAuth';
import { SocialProvider, ThemeCustomization, Chain } from '../types';

interface TokaiModalProps {
  appName?: string;
  socialProviders?: SocialProvider[];
  theme?: 'light' | 'dark' | 'auto';
  customization?: ThemeCustomization;
  onSuccess?: (user: any) => void;
  onError?: (error: string) => void;
  showEmailAuth?: boolean;
  autoCreateWallet?: boolean;
  embeddedWallets?: boolean;
  showExternalWallets?: boolean;
  supportedChains?: Chain[];
  // Feature flags
  authType?: 'password' | 'otp' | 'both';
  emailOtpEnabled?: boolean;
  emailVerificationRequired?: boolean;
}

export const TokaiModal: React.FC<TokaiModalProps> = ({
  appName = 'Tokai App',
  socialProviders = [
    { id: 'google', enabled: true },
    { id: 'discord', enabled: true },
    { id: 'twitter', enabled: true },
    { id: 'github', enabled: true },
  ],
  theme = 'dark',
  customization,
  onSuccess,
  onError,
  showEmailAuth = true,
  autoCreateWallet = true,
  embeddedWallets = true,
  showExternalWallets = true,
  supportedChains = ['ethereum', 'solana'] as Chain[],
  authType = 'password',
  emailOtpEnabled = true,
  emailVerificationRequired = false,
}) => {
  const { isModalOpen, closeModal, appConfig } = useTokai();

  // Use context appConfig or fallback to props
  const featureFlags = {
    authType: authType || appConfig?.authType || 'password',
    emailOtpEnabled: emailOtpEnabled ?? appConfig?.emailOtpEnabled ?? true,
    emailVerificationRequired: emailVerificationRequired ?? appConfig?.emailVerificationRequired ?? false,
  };

  const handleSuccess = (user: any) => {
    onSuccess?.(user);
    closeModal();
  };

  const handleError = (error: string) => {
    onError?.(error);
  };

  if (!isModalOpen) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 9999,
      }}
      onClick={closeModal}
    >
      <div
        style={{
          backgroundColor: customization?.backgroundColor || (theme === 'dark' ? '#1a1a1a' : '#ffffff'),
          borderRadius: customization?.borderRadius || '12px',
          padding: '24px',
          maxWidth: '400px',
          width: '90%',
          maxHeight: '80vh',
          overflow: 'auto',
          position: 'relative',
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          onClick={closeModal}
          style={{
            position: 'absolute',
            top: '16px',
            right: '16px',
            background: 'none',
            border: 'none',
            fontSize: '24px',
            cursor: 'pointer',
            color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
            padding: '4px',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          ✕
        </button>

        {/* Auth Content */}
        <TokaiAuth
          appName={appName}
          socialProviders={socialProviders}
          theme={theme}
          customization={customization}
          onSuccess={handleSuccess}
          onError={handleError}
          showEmailAuth={showEmailAuth}
          autoCreateWallet={autoCreateWallet}
          embeddedWallets={embeddedWallets}
          authType={featureFlags.authType}
          emailOtpEnabled={featureFlags.emailOtpEnabled}
          emailVerificationRequired={featureFlags.emailVerificationRequired}
          showExternalWallets={showExternalWallets}
          supportedChains={supportedChains}
        />
      </div>
    </div>
  );
};