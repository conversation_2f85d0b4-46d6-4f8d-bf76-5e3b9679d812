//
import React, { useState } from 'react';
import { useTokai } from '../context';
import { ThemeCustomization } from '../types';

interface TokaiConnectButtonProps {
  theme?: 'light' | 'dark' | 'auto';
  customization?: ThemeCustomization;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
  children?: React.ReactNode;
  // Feature flags (if not provided, will be used from context)
  emailOtpEnabled?: boolean;
  emailVerificationRequired?: boolean;
}

export const TokaiConnectButton: React.FC<TokaiConnectButtonProps> = ({
  theme = 'dark',
  customization,
  size = 'md',
  variant = 'primary',
  children
}) => {
  const { user, wallets, logout, isLoading, openModal } = useTokai();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const getSizeStyles = () => {
    const sizes = {
      sm: { padding: '8px 16px', fontSize: '14px', borderRadius: '6px' },
      md: { padding: '12px 20px', fontSize: '16px', borderRadius: '8px' },
      lg: { padding: '16px 24px', fontSize: '18px', borderRadius: '10px' },
    };
    return sizes[size];
  };

  const getVariantStyles = () => {
    const primaryColor = customization?.primaryColor || '#6366f1';
    const variants = {
      primary: {
        backgroundColor: primaryColor,
        color: '#ffffff',
        border: 'none',
      },
      secondary: {
        backgroundColor: theme === 'dark' ? '#2a2a2a' : '#f8f9fa',
        color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
        border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
      },
      outline: {
        backgroundColor: 'transparent',
        color: primaryColor,
        border: `2px solid ${primaryColor}`,
      },
    };
    return variants[variant];
  };

  const buttonStyles = {
    ...getSizeStyles(),
    ...getVariantStyles(),
    cursor: 'pointer',
    fontWeight: '500',
    transition: 'all 0.2s ease',
    position: 'relative' as const,
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    fontFamily: customization?.fontFamily || '"Inter", sans-serif',
  };

  const handleConnect = () => {
    openModal();
  };

  const handleLogout = async () => {
    try {
      await logout();
      setShowUserMenu(false);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const getPrimaryWallet = () => {
    return wallets.find(w => w.is_active) || wallets[0];
  };

  const formatAddress = (address: string) => {
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  // If user is not connected, show connect button
  if (!user) {
    return (
      <>
        <button
          onClick={handleConnect}
          disabled={isLoading}
          style={buttonStyles}
          onMouseEnter={(e) => {
            e.currentTarget.style.opacity = '0.9';
            e.currentTarget.style.transform = 'translateY(-1px)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.opacity = '1';
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          {isLoading ? (
            <>
              <div className="spinner" style={{
                width: '16px',
                height: '16px',
                border: '2px solid transparent',
                borderTop: '2px solid currentColor',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
              Connecting...
            </>
          ) : (
            <>
              🔗
              {children || 'Connect Wallet'}
            </>
          )}
        </button>

        <style dangerouslySetInnerHTML={{
          __html: `
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `
        }} />
      </>
    );
  }

  // If user is connected, show user menu
  const primaryWallet = getPrimaryWallet();
  
  return (
    <div className="tokai-user-menu" style={{ position: 'relative' }}>
      <button
        onClick={() => setShowUserMenu(!showUserMenu)}
        style={{
          ...buttonStyles,
          backgroundColor: theme === 'dark' ? '#2a2a2a' : '#f8f9fa',
          color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
          border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
        }}
      >
        {user.avatar_url ? (
          <img 
            src={user.avatar_url} 
            alt="User avatar" 
            style={{ 
              width: '20px', 
              height: '20px', 
              borderRadius: '50%' 
            }}
          />
        ) : (
          <div style={{
            width: '20px',
            height: '20px',
            borderRadius: '50%',
            backgroundColor: customization?.primaryColor || '#6366f1',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px',
            fontWeight: 'bold',
            color: '#ffffff'
          }}>
            {(user.full_name || user.email || 'U')[0].toUpperCase()}
          </div>
        )}
        
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
          <span style={{ fontSize: '14px', fontWeight: '500' }}>
            {user.full_name || user.email}
          </span>
          {primaryWallet && (
            <span style={{ fontSize: '12px', opacity: 0.7 }}>
              {formatAddress(primaryWallet.wallet_address)}
            </span>
          )}
        </div>
        
        <span style={{ fontSize: '12px' }}>▼</span>
      </button>

      {showUserMenu && (
        <div className="tokai-dropdown" style={{
          position: 'absolute',
          top: '100%',
          right: '0',
          marginTop: '8px',
          backgroundColor: customization?.backgroundColor || (theme === 'dark' ? '#2a2a2a' : '#ffffff'),
          border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
          borderRadius: customization?.borderRadius || '8px',
          padding: '8px',
          minWidth: '250px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          zIndex: 1000,
        }}>
          {/* User Info */}
          <div style={{
            padding: '12px',
            borderBottom: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
            marginBottom: '8px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
              {user.avatar_url ? (
                <img 
                  src={user.avatar_url} 
                  alt="User avatar" 
                  style={{ width: '32px', height: '32px', borderRadius: '50%' }}
                />
              ) : (
                <div style={{
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  backgroundColor: customization?.primaryColor || '#6366f1',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: '#ffffff'
                }}>
                  {(user.full_name || user.email || 'U')[0].toUpperCase()}
                </div>
              )}
              <div>
                <div style={{ fontWeight: '500', fontSize: '14px' }}>
                  {user.full_name || user.email}
                </div>
                {user.email_verified && (
                  <div style={{ fontSize: '12px', color: '#10b981' }}>
                    ✓ Verified
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Wallets */}
          {primaryWallet && (
            <div style={{ padding: '8px 12px', marginBottom: '8px' }}>
              <div style={{ fontSize: '12px', opacity: 0.7, marginBottom: '4px' }}>
                Primary Wallet
              </div>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'space-between',
                padding: '8px',
                backgroundColor: theme === 'dark' ? '#1a1a1a' : '#f8f9fa',
                borderRadius: '6px'
              }}>
                <div>
                  <div style={{ fontSize: '14px', fontWeight: '500' }}>
                    {primaryWallet.network.toUpperCase()}
                  </div>
                  <div style={{ fontSize: '12px', opacity: 0.7 }}>
                    {formatAddress(primaryWallet.wallet_address)}
                  </div>
                </div>
                <div style={{ fontSize: '12px', color: '#10b981' }}>
                  {primaryWallet.is_embedded ? '🔐 Embedded' : '🔗 Connected'}
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div style={{ borderTop: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`, paddingTop: '8px' }}>
            <button
              onClick={() => {
                setShowUserMenu(false);
                // Handle view wallets
              }}
              style={{
                width: '100%',
                padding: '8px 12px',
                backgroundColor: 'transparent',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
                textAlign: 'left',
                marginBottom: '4px'
              }}
            >
              💼 View Wallets
            </button>
            
            <button
              onClick={() => {
                setShowUserMenu(false);
                // Handle settings
              }}
              style={{
                width: '100%',
                padding: '8px 12px',
                backgroundColor: 'transparent',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
                textAlign: 'left',
                marginBottom: '4px'
              }}
            >
              ⚙️ Settings
            </button>
            
            <button
              onClick={handleLogout}
              style={{
                width: '100%',
                padding: '8px 12px',
                backgroundColor: 'transparent',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                color: '#ef4444',
                textAlign: 'left'
              }}
            >
              🚪 Sign Out
            </button>
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {showUserMenu && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 999
          }}
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </div>
  );
};

export default TokaiConnectButton;