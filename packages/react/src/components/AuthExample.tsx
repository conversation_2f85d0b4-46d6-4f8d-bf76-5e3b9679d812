import React, { useState } from 'react';
import { useEmailVerification, useOTPAuth, useAppAuthConfig } from '../hooks';

interface AuthExampleProps {
  appId?: string;
}

export const AuthExample: React.FC<AuthExampleProps> = ({ appId }) => {
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [token, setToken] = useState('');
  const [step, setStep] = useState<'email' | 'otp' | 'verification'>('email');
  
  const { sendVerificationEmail, verifyEmail, error: verificationError, isLoading: verificationLoading } = useEmailVerification();
  const { sendOTP, verifyOTP, error: otpError, isLoading: otpLoading } = useOTPAuth();
  const { config, isLoading: configLoading } = useAppAuthConfig(appId || '');

  const handleSendVerificationEmail = async () => {
    try {
      await sendVerificationEmail(email);
      alert('Verification email sent!');
    } catch (error) {
      console.error('Failed to send verification email:', error);
    }
  };

  const handleVerifyEmail = async () => {
    try {
      await verifyEmail(token);
      alert('Email verified successfully!');
    } catch (error) {
      console.error('Failed to verify email:', error);
    }
  };

  const handleSendOTP = async () => {
    try {
      await sendOTP(email);
      setStep('otp');
      alert('OTP sent to your email!');
    } catch (error) {
      console.error('Failed to send OTP:', error);
    }
  };

  const handleVerifyOTP = async () => {
    try {
      await verifyOTP(email, otp);
      alert('OTP verified successfully!');
    } catch (error) {
      console.error('Failed to verify OTP:', error);
    }
  };

  return (
    <div className="tokai-auth-example">
      <h2>Authentication Example</h2>
      
      {/* App Configuration Display */}
      {appId && (
        <div className="app-config">
          <h3>App Configuration</h3>
          {configLoading ? (
            <p>Loading app configuration...</p>
          ) : config ? (
            <div>
              <p><strong>Auth Type:</strong> {config.data.auth_config.auth_type}</p>
              <p><strong>Email OTP Enabled:</strong> {config.data.auth_config.email_otp_enabled ? 'Yes' : 'No'}</p>
              <p><strong>Email Verification Required:</strong> {config.data.auth_config.email_verification_required ? 'Yes' : 'No'}</p>
            </div>
          ) : (
            <p>No app configuration found</p>
          )}
        </div>
      )}

      {/* Email Verification Section */}
      <div className="email-verification">
        <h3>Email Verification</h3>
        <div>
          <input
            type="email"
            placeholder="Enter email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <button 
            onClick={handleSendVerificationEmail}
            disabled={verificationLoading}
          >
            {verificationLoading ? 'Sending...' : 'Send Verification Email'}
          </button>
        </div>
        
        <div>
          <input
            type="text"
            placeholder="Enter verification token"
            value={token}
            onChange={(e) => setToken(e.target.value)}
          />
          <button 
            onClick={handleVerifyEmail}
            disabled={verificationLoading}
          >
            {verificationLoading ? 'Verifying...' : 'Verify Email'}
          </button>
        </div>
        
        {verificationError && (
          <p className="error">Verification Error: {verificationError}</p>
        )}
      </div>

      {/* OTP Authentication Section */}
      <div className="otp-auth">
        <h3>OTP Authentication</h3>
        {step === 'email' ? (
          <div>
            <input
              type="email"
              placeholder="Enter email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <button 
              onClick={handleSendOTP}
              disabled={otpLoading}
            >
              {otpLoading ? 'Sending...' : 'Send OTP'}
            </button>
          </div>
        ) : (
          <div>
            <input
              type="text"
              placeholder="Enter 6-digit OTP"
              value={otp}
              onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
              maxLength={6}
            />
            <button 
              onClick={handleVerifyOTP}
              disabled={otpLoading || otp.length !== 6}
            >
              {otpLoading ? 'Verifying...' : 'Verify OTP'}
            </button>
            <button onClick={() => setStep('email')}>
              Back to Email
            </button>
          </div>
        )}
        
        {otpError && (
          <p className="error">OTP Error: {otpError}</p>
        )}
      </div>
    </div>
  );
};