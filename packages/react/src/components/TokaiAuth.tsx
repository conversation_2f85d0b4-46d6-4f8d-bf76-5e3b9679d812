//
import React, { useState } from 'react';
import { useTokai } from '../context';
import { SocialProvider, ThemeCustomization, Chain } from '../types';

interface TokaiAuthProps {
  appName?: string;
  socialProviders?: SocialProvider[];
  theme?: 'light' | 'dark' | 'auto';
  customization?: ThemeCustomization;
  onSuccess?: (user: any) => void;
  onError?: (error: string) => void;
  showEmailAuth?: boolean;
  autoCreateWallet?: boolean;
  embeddedWallets?: boolean;
  // Feature flags
  authType?: 'password' | 'otp' | 'both';
  emailOtpEnabled?: boolean;
  emailVerificationRequired?: boolean;
  // External wallet options
  showExternalWallets?: boolean;
  supportedChains?: Chain[];
}

export const TokaiAuth: React.FC<TokaiAuthProps> = ({
  appName = 'Tokai App',
  socialProviders = [
    { id: 'google', enabled: true },
    { id: 'discord', enabled: true },
    { id: 'twitter', enabled: true },
    { id: 'github', enabled: true },
  ],
  theme = 'dark',
  customization,
  onSuccess,
  onError,
  showEmailAuth = true,
  embeddedWallets = true,
  // Feature flags
  showExternalWallets = true,
  supportedChains = ['ethereum', 'solana'] as Chain[],
}) => {
  const { user, login, register, connectExternalWallet, isLoading, error, clearError } = useTokai();
  const [authMode, setAuthMode] = useState<'login' | 'register' | 'social' | 'otp' | 'external'>('social');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [selectedChain, setSelectedChain] = useState('ethereum');

  const getThemeStyles = () => {
    const baseStyles = {
      backgroundColor: customization?.backgroundColor || (theme === 'dark' ? '#1a1a1a' : '#ffffff'),
      color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
      borderRadius: customization?.borderRadius || '12px',
      fontFamily: customization?.fontFamily || '"Inter", sans-serif',
    };
    return baseStyles;
  };

  const handleSocialLogin = async (provider: string) => {
    try {
      clearError();
      // This would trigger OAuth flow in a real implementation
      window.location.href = `/api/auth/social/${provider}?redirect=${window.location.origin}`;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : `Failed to login with ${provider}`;
      onError?.(errorMsg);
    }
  };

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      clearError();
      if (authMode === 'register') {
        await register(email, password, fullName);
      } else {
        await login(email, password);
      }
      onSuccess?.(user);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Authentication failed';
      onError?.(errorMsg);
    }
  };

  const handleExternalWallet = async (walletType: string) => {
    try {
      clearError();
      await connectExternalWallet(selectedChain as Chain, walletType);
      onSuccess?.(user);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : `Failed to connect ${walletType}`;
      onError?.(errorMsg);
    }
  };

  const getSocialIcon = (provider: string) => {
    const icons = {
      google: '🔍',
      discord: '💬',
      twitter: '🐦',
      github: '⚡',
      apple: '🍎',
    };
    return icons[provider as keyof typeof icons] || '🔗';
  };

  const getSocialLabel = (provider: string) => {
    return `Continue with ${provider.charAt(0).toUpperCase() + provider.slice(1)}`;
  };

  if (user) {
    return (
      <div className="tokai-auth-success" style={getThemeStyles()}>
        <div className="tokai-welcome">
          <h2>Welcome back!</h2>
          <div className="tokai-user-info">
            {user.avatar_url && (
              <img 
                src={user.avatar_url} 
                alt="User avatar" 
                className="tokai-avatar"
                style={{ width: '48px', height: '48px', borderRadius: '50%' }}
              />
            )}
            <div>
              <p className="tokai-user-name">{user.full_name || user.email}</p>
              {user.embedded_wallet && (
                <p className="tokai-wallet-address">
                  Wallet: {user.embedded_wallet.address.substring(0, 6)}...
                  {user.embedded_wallet.address.substring(user.embedded_wallet.address.length - 4)}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tokai-auth-container" style={getThemeStyles()}>
      <div className="tokai-auth-modal">
        {/* Header */}
        <div className="tokai-auth-header">
          {customization?.brandLogo ? (
            <img src={customization.brandLogo} alt={appName} className="tokai-brand-logo" />
          ) : (
            <h2 className="tokai-app-name">{appName}</h2>
          )}
          <p className="tokai-auth-subtitle">
            {authMode === 'social' ? 'Sign in to continue' : 
             authMode === 'register' ? 'Create your account' : 'Welcome back'}
          </p>
        </div>

        {error && (
          <div className="tokai-error-message" style={{ 
            backgroundColor: theme === 'dark' ? '#2d1b1b' : '#fef2f2',
            color: theme === 'dark' ? '#f87171' : '#dc2626',
            padding: '12px',
            borderRadius: '8px',
            marginBottom: '16px'
          }}>
            {error}
          </div>
        )}

        {/* Social Login Buttons */}
        {authMode === 'social' && (
          <div className="tokai-social-buttons">
            {socialProviders.filter(p => p.enabled).map((provider) => (
              <button
                key={provider.id}
                onClick={() => handleSocialLogin(provider.id)}
                disabled={isLoading}
                className="tokai-social-button"
                style={{
                  backgroundColor: theme === 'dark' ? '#2a2a2a' : '#f8f9fa',
                  color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
                  border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
                  borderRadius: customization?.borderRadius || '8px',
                  padding: '12px 16px',
                  marginBottom: '8px',
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  transition: 'all 0.2s ease',
                }}
              >
                <span style={{ fontSize: '18px' }}>{getSocialIcon(provider.id)}</span>
                {getSocialLabel(provider.id)}
              </button>
            ))}

            {showExternalWallets && (
              <>
                <div className="tokai-divider" style={{
                  display: 'flex',
                  alignItems: 'center',
                  margin: '20px 0',
                  color: theme === 'dark' ? '#666' : '#999'
                }}>
                  <div style={{ flex: 1, height: '1px', backgroundColor: 'currentColor', opacity: 0.3 }} />
                  <span style={{ padding: '0 16px', fontSize: '12px' }}>OR</span>
                  <div style={{ flex: 1, height: '1px', backgroundColor: 'currentColor', opacity: 0.3 }} />
                </div>

                <div className="tokai-external-wallets">
                  <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: '600' }}>
                    Connect External Wallet
                  </h3>
                  
                  {/* Chain Selector */}
                  <div style={{ marginBottom: '16px' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontSize: '14px' }}>
                      Select Network
                    </label>
                    <select
                      value={selectedChain}
                      onChange={(e) => setSelectedChain(e.target.value)}
                      style={{
                        width: '100%',
                        padding: '12px',
                        borderRadius: customization?.borderRadius || '8px',
                        border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
                        backgroundColor: theme === 'dark' ? '#2a2a2a' : '#ffffff',
                        color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
                        fontSize: '14px',
                      }}
                    >
                      {supportedChains.map(chain => (
                        <option key={chain} value={chain}>
                          {chain.charAt(0).toUpperCase() + chain.slice(1)}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Wallet Buttons */}
                  <div className="tokai-wallet-buttons">
                    {selectedChain === 'ethereum' && (
                      <>
                        <button
                          onClick={() => handleExternalWallet('metamask')}
                          disabled={isLoading}
                          className="tokai-wallet-button"
                          style={{
                            backgroundColor: theme === 'dark' ? '#2a2a2a' : '#f8f9fa',
                            color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
                            border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
                            borderRadius: customization?.borderRadius || '8px',
                            padding: '12px 16px',
                            marginBottom: '8px',
                            width: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '8px',
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'all 0.2s ease',
                          }}
                        >
                          <span style={{ fontSize: '18px' }}>🦊</span>
                          Connect MetaMask
                        </button>

                        <button
                          onClick={() => handleExternalWallet('coinbase')}
                          disabled={isLoading}
                          className="tokai-wallet-button"
                          style={{
                            backgroundColor: theme === 'dark' ? '#2a2a2a' : '#f8f9fa',
                            color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
                            border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
                            borderRadius: customization?.borderRadius || '8px',
                            padding: '12px 16px',
                            marginBottom: '8px',
                            width: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '8px',
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'all 0.2s ease',
                          }}
                        >
                          <span style={{ fontSize: '18px' }}>🪙</span>
                          Coinbase Wallet
                        </button>
                      </>
                    )}
                    
                    {selectedChain === 'solana' && (
                      <>
                        <button
                          onClick={() => handleExternalWallet('phantom')}
                          disabled={isLoading}
                          className="tokai-wallet-button"
                          style={{
                            backgroundColor: theme === 'dark' ? '#2a2a2a' : '#f8f9fa',
                            color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
                            border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
                            borderRadius: customization?.borderRadius || '8px',
                            padding: '12px 16px',
                            marginBottom: '8px',
                            width: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '8px',
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'all 0.2s ease',
                          }}
                        >
                          <span style={{ fontSize: '18px' }}>👻</span>
                          Phantom
                        </button>
                        <button
                          onClick={() => handleExternalWallet('solflare')}
                          disabled={isLoading}
                          className="tokai-wallet-button"
                          style={{
                            backgroundColor: theme === 'dark' ? '#2a2a2a' : '#f8f9fa',
                            color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
                            border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
                            borderRadius: customization?.borderRadius || '8px',
                            padding: '12px 16px',
                            marginBottom: '8px',
                            width: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '8px',
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'all 0.2s ease',
                          }}
                        >
                          <span style={{ fontSize: '18px' }}>🔥</span>
                          Solflare
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </>
            )}

            {showEmailAuth && (
              <>
                <div className="tokai-divider" style={{
                  display: 'flex',
                  alignItems: 'center',
                  margin: '20px 0',
                  color: theme === 'dark' ? '#666' : '#999'
                }}>
                  <div style={{ flex: 1, height: '1px', backgroundColor: 'currentColor', opacity: 0.3 }} />
                  <span style={{ padding: '0 16px', fontSize: '12px' }}>OR</span>
                  <div style={{ flex: 1, height: '1px', backgroundColor: 'currentColor', opacity: 0.3 }} />
                </div>

                <button
                  onClick={() => setAuthMode('login')}
                  className="tokai-email-button"
                  style={{
                    backgroundColor: customization?.primaryColor || '#6366f1',
                    color: '#ffffff',
                    border: 'none',
                    borderRadius: customization?.borderRadius || '8px',
                    padding: '12px 16px',
                    width: '100%',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500',
                    transition: 'all 0.2s ease',
                  }}
                >
                  Continue with Email
                </button>
              </>
            )}
          </div>
        )}

        {/* Email Authentication Form */}
        {(authMode === 'login' || authMode === 'register') && (
          <form onSubmit={handleEmailAuth} className="tokai-email-form">
            {authMode === 'register' && (
              <div className="tokai-form-field">
                <label htmlFor="fullName" style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>
                  Full Name
                </label>
                <input
                  id="fullName"
                  type="text"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  disabled={isLoading}
                  style={{
                    width: '100%',
                    padding: '12px',
                    borderRadius: customization?.borderRadius || '8px',
                    border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
                    backgroundColor: theme === 'dark' ? '#2a2a2a' : '#ffffff',
                    color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
                    fontSize: '14px',
                  }}
                  placeholder="Enter your full name"
                />
              </div>
            )}

            <div className="tokai-form-field">
              <label htmlFor="email" style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>
                Email
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
                style={{
                  width: '100%',
                  padding: '12px',
                  borderRadius: customization?.borderRadius || '8px',
                  border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
                  backgroundColor: theme === 'dark' ? '#2a2a2a' : '#ffffff',
                  color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
                  fontSize: '14px',
                }}
                placeholder="Enter your email"
              />
            </div>

            <div className="tokai-form-field">
              <label htmlFor="password" style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>
                Password
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading}
                style={{
                  width: '100%',
                  padding: '12px',
                  borderRadius: customization?.borderRadius || '8px',
                  border: `1px solid ${theme === 'dark' ? '#404040' : '#e5e7eb'}`,
                  backgroundColor: theme === 'dark' ? '#2a2a2a' : '#ffffff',
                  color: customization?.textColor || (theme === 'dark' ? '#ffffff' : '#000000'),
                  fontSize: '14px',
                }}
                placeholder="Enter your password"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              style={{
                backgroundColor: customization?.primaryColor || '#6366f1',
                color: '#ffffff',
                border: 'none',
                borderRadius: customization?.borderRadius || '8px',
                padding: '12px 16px',
                width: '100%',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                marginTop: '16px',
                opacity: isLoading ? 0.7 : 1,
              }}
            >
              {isLoading ? 'Please wait...' : 
               authMode === 'register' ? 'Create Account' : 'Sign In'}
            </button>

            <div className="tokai-auth-switch" style={{ 
              textAlign: 'center', 
              marginTop: '16px',
              fontSize: '14px',
              color: theme === 'dark' ? '#999' : '#666'
            }}>
              {authMode === 'register' ? (
                <>
                  Already have an account?{' '}
                  <button
                    type="button"
                    onClick={() => setAuthMode('login')}
                    style={{
                      background: 'none',
                      border: 'none',
                      color: customization?.primaryColor || '#6366f1',
                      cursor: 'pointer',
                      textDecoration: 'underline',
                    }}
                  >
                    Sign in
                  </button>
                </>
              ) : (
                <>
                  Don&apos;t have an account?{' '}
                  <button
                    type="button"
                    onClick={() => setAuthMode('register')}
                    style={{
                      background: 'none',
                      border: 'none',
                      color: customization?.primaryColor || '#6366f1',
                      cursor: 'pointer',
                      textDecoration: 'underline',
                    }}
                  >
                    Sign up
                  </button>
                </>
              )}
            </div>

            <button
              type="button"
              onClick={() => setAuthMode('social')}
              style={{
                background: 'none',
                border: 'none',
                color: theme === 'dark' ? '#999' : '#666',
                cursor: 'pointer',
                fontSize: '12px',
                marginTop: '12px',
                width: '100%',
              }}
            >
              ← Back to social login
            </button>
          </form>
        )}

        {/* Footer */}
        <div className="tokai-auth-footer" style={{
          marginTop: '20px',
          fontSize: '12px',
          color: theme === 'dark' ? '#666' : '#999',
          textAlign: 'center'
        }}>
          {embeddedWallets && (
            <p>🔐 Your wallet will be created automatically and secured with your login method</p>
          )}
          <p>Powered by Tokai • Secure & Private</p>
        </div>
      </div>
    </div>
  );
};

export default TokaiAuth;