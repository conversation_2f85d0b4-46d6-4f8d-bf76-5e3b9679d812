import React, { useState } from 'react';
import { useTokai } from './context';
import { useWallet, useSessionKeys, useNotifications, useWalletList, useTransaction, useWalletSettings } from './hooks';
import { AuthExample } from './components/AuthExample';
import { 
  Wallet, 
  Chain, 
  ConnectButtonProps, 
  WalletDashboardProps,
  WalletType,
  WalletTypeForChain
} from './types';

// Enhanced Login Component
export const TokaiLogin: React.FC<{ onSuccess?: () => void; className?: string }> = ({ onSuccess, className }) => {
  const { login, isLoading, error, clearError } = useTokai();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    try {
      await login(email, password);
      onSuccess?.();
    } catch (err) {
      // Error is handled by the context
    }
  };

  return (
    <div className={`tokai-login ${className || ''}`}>
      <h2>Login to Tokai</h2>
      {error && <div className="tokai-error">{error}</div>}
      <form onSubmit={handleSubmit}>
        <div className="tokai-form-group">
          <label htmlFor="email">Email:</label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={isLoading}
            className="tokai-input"
          />
        </div>
        <div className="tokai-form-group">
          <label htmlFor="password">Password:</label>
          <input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            disabled={isLoading}
            className="tokai-input"
          />
        </div>
        <button type="submit" disabled={isLoading} className="tokai-button">
          {isLoading ? 'Logging in...' : 'Login'}
        </button>
      </form>
    </div>
  );
};

// Enhanced Register Component
export const TokaiRegister: React.FC<{ onSuccess?: () => void; className?: string }> = ({ onSuccess, className }) => {
  const { register, isLoading, error, clearError } = useTokai();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    try {
      await register(email, password, fullName);
      onSuccess?.();
    } catch (err) {
      // Error is handled by the context
    }
  };

  return (
    <div className={`tokai-register ${className || ''}`}>
      <h2>Register with Tokai</h2>
      {error && <div className="tokai-error">{error}</div>}
      <form onSubmit={handleSubmit}>
        <div className="tokai-form-group">
          <label htmlFor="fullName">Full Name:</label>
          <input
            id="fullName"
            type="text"
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            disabled={isLoading}
            className="tokai-input"
          />
        </div>
        <div className="tokai-form-group">
          <label htmlFor="regEmail">Email:</label>
          <input
            id="regEmail"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={isLoading}
            className="tokai-input"
          />
        </div>
        <div className="tokai-form-group">
          <label htmlFor="regPassword">Password:</label>
          <input
            id="regPassword"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            disabled={isLoading}
            className="tokai-input"
          />
        </div>
        <button type="submit" disabled={isLoading} className="tokai-button">
          {isLoading ? 'Registering...' : 'Register'}
        </button>
      </form>
    </div>
  );
};

// Enhanced Wallet List Component
export const TokaiWalletList: React.FC<{ className?: string }> = ({ className }) => {
  const { wallets, isLoading, error, refreshWallets } = useWalletList();

  if (isLoading) return <div className="tokai-loading">Loading wallets...</div>;
  if (error) return <div className="tokai-error">{error}</div>;

  return (
    <div className={`tokai-wallet-list ${className || ''}`}>
      <div className="tokai-header">
        <h3>Your Wallets</h3>
        <button onClick={refreshWallets} className="tokai-button tokai-button-secondary">
          Refresh
        </button>
      </div>
      {wallets.length === 0 ? (
        <div className="tokai-empty-state">
          <p>No wallets found. Create your first wallet!</p>
        </div>
      ) : (
        <div className="tokai-wallets">
          {wallets.map((wallet) => (
            <div key={wallet.id} className="tokai-wallet-card">
              <div className="tokai-wallet-header">
                <h4>{wallet.network} Wallet</h4>
                <span className={`tokai-status ${wallet.is_active ? 'active' : 'inactive'}`}>
                  {wallet.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div className="tokai-wallet-details">
                <p className="tokai-address">Address: {wallet.wallet_address}</p>
                <p className="tokai-type">Type: {wallet.wallet_type}</p>
                <p className="tokai-date">Created: {new Date(wallet.created_at).toLocaleDateString()}</p>
              </div>
              {wallet.balance && (
                <div className="tokai-balance">
                  <p>Balance: {wallet.balance.total} {wallet.balance.tokens?.[0]?.symbol || 'ETH'}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Enhanced Create Wallet Component
export const TokaiCreateWallet: React.FC<{ onSuccess?: () => void; className?: string }> = ({ onSuccess, className }) => {
  const { createWallet, settings, isLoading, error, clearError } = useWalletList();
  const [network, setNetwork] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const handleCreate = async () => {
    setIsCreating(true);
    clearError();
    try {
      await createWallet(network || undefined);
      setNetwork('');
      onSuccess?.();
    } catch (err) {
      // Error is handled by the context
    } finally {
      setIsCreating(false);
    }
  };

  if (isLoading) return <div className="tokai-loading">Loading...</div>;

  return (
    <div className={`tokai-create-wallet ${className || ''}`}>
      <h3>Create New Wallet</h3>
      {error && <div className="tokai-error">{error}</div>}
      <div className="tokai-form-group">
        <label htmlFor="network">Network:</label>
        <select
          id="network"
          value={network}
          onChange={(e) => setNetwork(e.target.value)}
          disabled={isCreating}
          className="tokai-select"
        >
          <option value="">Use Default ({settings?.defaultWalletType || 'ethereum'})</option>
          {settings?.allowedNetworks.map((net: string) => (
            <option key={net} value={net}>
              {net}
            </option>
          ))}
        </select>
      </div>
      <button onClick={handleCreate} disabled={isCreating} className="tokai-button">
        {isCreating ? 'Creating...' : 'Create Wallet'}
      </button>
    </div>
  );
};

// Session Keys Component
export const TokaiSessionKeys: React.FC<{ className?: string }> = ({ className }) => {
  const { sessionKeys, createSessionKey, revokeSessionKey, isLoading, error } = useSessionKeys();
  const [showCreate, setShowCreate] = useState(false);
  const [name, setName] = useState('');
  const [permissions, setPermissions] = useState<string[]>([]);
  const [expiryHours, setExpiryHours] = useState(24);

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createSessionKey(name, permissions, expiryHours);
      setName('');
      setPermissions([]);
      setExpiryHours(24);
      setShowCreate(false);
    } catch (err) {
      // Error handled by hook
    }
  };

  const handleRevoke = async (id: string) => {
    if (confirm('Are you sure you want to revoke this session key?')) {
      await revokeSessionKey(id);
    }
  };

  return (
    <div className={`tokai-session-keys ${className || ''}`}>
      <div className="tokai-header">
        <h3>Session Keys</h3>
        <button 
          onClick={() => setShowCreate(!showCreate)} 
          className="tokai-button tokai-button-secondary"
        >
          {showCreate ? 'Cancel' : 'Create New'}
        </button>
      </div>

      {error && <div className="tokai-error">{error}</div>}

      {showCreate && (
        <form onSubmit={handleCreate} className="tokai-form">
          <div className="tokai-form-group">
            <label htmlFor="sessionName">Name:</label>
            <input
              id="sessionName"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className="tokai-input"
            />
          </div>
          <div className="tokai-form-group">
            <label htmlFor="expiryHours">Expiry (hours):</label>
            <input
              id="expiryHours"
              type="number"
              value={expiryHours}
              onChange={(e) => setExpiryHours(Number(e.target.value))}
              min="1"
              max="720"
              className="tokai-input"
            />
          </div>
          <button type="submit" disabled={isLoading} className="tokai-button">
            {isLoading ? 'Creating...' : 'Create Session Key'}
          </button>
        </form>
      )}

      <div className="tokai-session-keys-list">
        {sessionKeys.map((key) => (
          <div key={key.id} className="tokai-session-key-card">
            <div className="tokai-session-key-header">
              <h4>{key.name}</h4>
              <span className={`tokai-status ${key.is_active ? 'active' : 'expired'}`}>
                {key.is_active ? 'Active' : 'Expired'}
              </span>
            </div>
            <div className="tokai-session-key-details">
              <p>Expires: {new Date(key.expires_at).toLocaleString()}</p>
              <p>Permissions: {key.permissions.join(', ')}</p>
              {key.last_used && (
                <p>Last used: {new Date(key.last_used).toLocaleString()}</p>
              )}
            </div>
            <button 
              onClick={() => handleRevoke(key.id)}
              className="tokai-button tokai-button-danger"
            >
              Revoke
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

// Notifications Component
export const TokaiNotifications: React.FC<{ className?: string }> = ({ className }) => {
  const { notifications, markAsRead, markAllAsRead, deleteNotification, isLoading, error } = useNotifications();
  const [showRead, setShowRead] = useState(true);

  const filteredNotifications = showRead 
    ? notifications 
    : notifications.filter(n => !n.read);

  const handleMarkAsRead = async (id: string) => {
    await markAsRead(id);
  };

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this notification?')) {
      await deleteNotification(id);
    }
  };

  return (
    <div className={`tokai-notifications ${className || ''}`}>
      <div className="tokai-header">
        <h3>Notifications</h3>
        <div className="tokai-notifications-controls">
          <button 
            onClick={() => setShowRead(!showRead)} 
            className="tokai-button tokai-button-secondary"
          >
            {showRead ? 'Hide Read' : 'Show All'}
          </button>
          <button 
            onClick={markAllAsRead} 
            className="tokai-button tokai-button-secondary"
          >
            Mark All Read
          </button>
        </div>
      </div>

      {error && <div className="tokai-error">{error}</div>}

      {isLoading ? (
        <div className="tokai-loading">Loading notifications...</div>
      ) : filteredNotifications.length === 0 ? (
        <div className="tokai-empty-state">
          <p>No notifications</p>
        </div>
      ) : (
        <div className="tokai-notifications-list">
          {filteredNotifications.map((notification) => (
            <div 
              key={notification.id} 
              className={`tokai-notification-card ${notification.read ? 'read' : 'unread'} ${notification.type}`}
            >
              <div className="tokai-notification-header">
                <h4>{notification.title}</h4>
                <span className="tokai-timestamp">
                  {new Date(notification.timestamp).toLocaleString()}
                </span>
              </div>
              <p className="tokai-notification-message">{notification.message}</p>
              {notification.action && (
                <a href={notification.action.url} className="tokai-action-link">
                  {notification.action.label}
                </a>
              )}
              <div className="tokai-notification-actions">
                {!notification.read && (
                  <button 
                    onClick={() => handleMarkAsRead(notification.id)}
                    className="tokai-button tokai-button-small"
                  >
                    Mark Read
                  </button>
                )}
                <button 
                  onClick={() => handleDelete(notification.id)}
                  className="tokai-button tokai-button-small tokai-button-danger"
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Wallet Connect Button Component with proper typing
export const TokaiConnectButton = <T extends Chain>({ 
  chain = 'ethereum' as T, 
  walletType,
  className,
  children,
  onConnect,
  onError 
}: ConnectButtonProps & { chain?: T; walletType?: WalletTypeForChain<T> }) => {
  const { connect, disconnect, state, isLoading, error } = useWallet(chain, walletType);

  const handleConnect = async () => {
    try {
      await connect();
      onConnect?.(state as any);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect wallet';
      onError?.(errorMessage);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnect();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to disconnect wallet';
      onError?.(errorMessage);
    }
  };

  return (
    <div className={`tokai-connect-button ${className || ''}`}>
      {state.isConnected ? (
        <div className="tokai-connected-state">
          <span className="tokai-account">{state.account}</span>
          <button onClick={handleDisconnect} className="tokai-button tokai-button-secondary">
            Disconnect
          </button>
        </div>
      ) : (
        <button 
          onClick={handleConnect} 
          disabled={isLoading}
          className="tokai-button"
        >
          {isLoading ? 'Connecting...' : children || `Connect ${walletType || 'wallet'}`}
        </button>
      )}
      {error && <div className="tokai-error">{error}</div>}
    </div>
  );
};

// Wallet Dashboard Component
export const TokaiWalletDashboard: React.FC<WalletDashboardProps> = ({ 
  chains = ['ethereum', 'solana'],
  showBalance = true,
  showTransactions = true,
  showSettings = true,
  className 
}) => {
  const { wallets, isLoading, error } = useWalletList();
  const { settings } = useWalletSettings();

  if (isLoading) return <div className="tokai-loading">Loading dashboard...</div>;
  if (error) return <div className="tokai-error">{error}</div>;

  return (
    <div className={`tokai-wallet-dashboard ${className || ''}`}>
      <div className="tokai-dashboard-header">
        <h2>Wallet Dashboard</h2>
        <div className="tokai-dashboard-stats">
          <div className="tokai-stat">
            <span className="tokai-stat-label">Total Wallets</span>
            <span className="tokai-stat-value">{wallets.length}</span>
          </div>
          <div className="tokai-stat">
            <span className="tokai-stat-label">Active Chains</span>
            <span className="tokai-stat-value">{chains.length}</span>
          </div>
        </div>
      </div>

      <div className="tokai-dashboard-content">
        <div className="tokai-dashboard-section">
          <h3>Your Wallets</h3>
          <TokaiWalletList />
        </div>

        {showBalance && (
          <div className="tokai-dashboard-section">
            <h3>Balances</h3>
            <div className="tokai-balances">
              {wallets.map((wallet) => (
                <div key={wallet.id} className="tokai-balance-card">
                  <h4>{wallet.network}</h4>
                  {wallet.balance ? (
                    <p>{wallet.balance.total} {wallet.balance.tokens?.[0]?.symbol || 'ETH'}</p>
                  ) : (
                    <p>Loading balance...</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {showSettings && settings && (
          <div className="tokai-dashboard-section">
            <h3>Settings</h3>
            <div className="tokai-settings">
              <p>Default Wallet Type: {settings.defaultWalletType}</p>
              <p>Auto Create Wallet: {settings.autoCreateWallet ? 'Yes' : 'No'}</p>
              <p>Max Wallets Per User: {settings.maxWalletsPerUser}</p>
              <p>Gasless Transactions: {settings.gaslessTransactions ? 'Enabled' : 'Disabled'}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Export the AuthExample component
export { AuthExample };