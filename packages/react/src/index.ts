// Types
export * from './types';

// API Client
export { TokaiApi } from './api';

// Context and Hooks
export { TokaiProvider, useTokai } from './context';

// Enhanced Hooks
export {
  useWallet,
  useSessionKeys,
  useAnalytics,
  useNotifications,
  useMultiChainWallet,
  useTransaction,
  useWalletList,
  useWalletSettings,
  useEmailVerification,
  useOTPAuth,
  useAppAuthConfig,
  useTokaiModal,
} from './hooks';

// Enhanced Components
export { AuthExample } from './components/AuthExample';

// Wallet Connector Components
export {
  TokaiWalletConnector,
  TokaiConnectButton as TokaiWalletConnectButton,
  TokaiWalletDashboard as TokaiWalletDashboardLegacy,
} from './components/WalletConnector';

// Tokai-style Components
export { TokaiAuth } from './components/TokaiAuth';
export { TokaiConnectButton } from './components/TokaiConnectButton';
export { TokaiModal } from './components/TokaiModal';

// Styles
export { default as styles } from './styles.css';

// Default export for convenience (Tokai-style)
export { TokaiConnectButton as default } from './components/TokaiConnectButton'; 