import * as React from "react";

export interface TokaiConfig {
  apiUrl: string;
  apiKey: string;
  appId?: string; // Optional: when provided, backend will enforce domain whitelist per app
  appName?: string;
  theme?: 'light' | 'dark' | 'auto';
  socialLogins?: SocialProvider[];
  embeddedWallets?: boolean;
  crossAppIdentity?: boolean;
  customization?: ThemeCustomization;
  // Enhanced wallet configuration
  supportedChains?: Chain[];
  defaultChain?: Chain;
  walletConnectProjectId?: string;
  sessionKeys?: boolean;
  analytics?: boolean;
}

export interface SocialProvider {
  id: 'google' | 'discord' | 'twitter' | 'github' | 'apple';
  enabled: boolean;
  clientId?: string;
}

export interface ThemeCustomization {
  primaryColor?: string;
  backgroundColor?: string;
  textColor?: string;
  borderRadius?: string;
  fontFamily?: string;
  brandLogo?: string;
}

// Enhanced Chain types
export type Chain = 
  | 'ethereum' 
  | 'solana' 
  | 'polkadot' 
  | 'algorand' 
  | 'fivire' 
  | 'astar' 
  | 'avalanche' 
  | 'cardano' 
  | 'cosmos' 
  | 'near';

// Wallet Types for each chain
export type EthereumWalletType = 'metamask' | 'coinbase' | 'rainbow' | 'argent';
export type SolanaWalletType = 'phantom' | 'backpack' | 'solflare' | 'slope' | 'exodus';
export type PolkadotWalletType = 'polkadotjs' | 'subwallet' | 'talisman' | 'nova';
export type AlgorandWalletType = 'pera' | 'defly' | 'exodus' | 'myalgo';
export type FivireWalletType = '5ire' | 'metamask';
export type AstarWalletType = 'subwallet' | 'polkadotjs' | 'talisman';
export type AvalancheWalletType = 'core' | 'metamask' | 'coinbase';
export type CardanoWalletType = 'nami' | 'eternl' | 'flint' | 'yoroi';
export type CosmosWalletType = 'keplr' | 'leap' | 'cosmostation' | 'xdefi';
export type NearWalletType = 'sender' | 'meteor' | 'here' | 'my-near-wallet';

// Union type for all wallet types
export type WalletType = 
  | EthereumWalletType 
  | SolanaWalletType 
  | PolkadotWalletType 
  | AlgorandWalletType 
  | FivireWalletType 
  | AstarWalletType 
  | AvalancheWalletType 
  | CardanoWalletType 
  | CosmosWalletType 
  | NearWalletType;

// Type-safe wallet type mapping
export type ChainWalletTypes = {
  ethereum: EthereumWalletType;
  solana: SolanaWalletType;
  polkadot: PolkadotWalletType;
  algorand: AlgorandWalletType;
  fivire: FivireWalletType;
  astar: AstarWalletType;
  avalanche: AvalancheWalletType;
  cardano: CardanoWalletType;
  cosmos: CosmosWalletType;
  near: NearWalletType;
};

// Helper type to get wallet type for a specific chain
export type WalletTypeForChain<T extends Chain> = ChainWalletTypes[T];

export interface ChainConfig {
  id: Chain;
  name: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  rpcUrl: string;
  blockExplorer: string;
  chainId: string;
}

export interface WalletNetwork {
  name: string;
  chainId: string;
  rpcUrl: string;
  blockExplorer: string;
  currencySymbol: string;
  currencyDecimals: number;
}

export interface WalletBalance {
  total: string;
  available: string;
  tokens: TokenBalance[];
}

export interface TokenBalance {
  address: string;
  symbol: string;
  name: string;
  balance: string;
  decimals: number;
  logo?: string;
}

export interface User {
  id: string;
  email?: string;
  full_name?: string;
  avatar_url?: string;
  email_verified: boolean;
  email_verified_at?: string;
  phone_verified?: boolean;
  created_at: string;
  // Social login info
  social_accounts?: SocialAccount[];
  // Embedded wallet info
  embedded_wallet?: EmbeddedWallet;
  // Cross-app identity
  global_user_id?: string;
  // Session management
  session_keys?: SessionKey[];
  // Analytics
  last_login?: string;
  login_count?: number;
}

export interface SocialAccount {
  provider: string;
  provider_id: string;
  email?: string;
  name?: string;
  avatar_url?: string;
  verified: boolean;
  connected_at: string;
}

export interface EmbeddedWallet {
  id: string;
  type: 'smart_account' | 'eoa';
  address: string;
  recovery_method: 'social' | 'email' | 'sms';
  is_deployed: boolean;
  supported_chains: string[];
}

export interface SessionKey {
  id: string;
  name: string;
  permissions: string[];
  expires_at: string;
  created_at: string;
  last_used?: string;
  is_active: boolean;
}

export interface Wallet {
  id: string;
  user_id: string;
  network: string;
  wallet_type: string;
  wallet_address: string;
  public_key: string;
  derivation_path: string;
  is_active: boolean;
  is_embedded: boolean;
  metadata: string;
  created_at: string;
  updated_at: string;
  // Enhanced wallet info
  chain?: Chain;
  balance?: WalletBalance;
  is_connected?: boolean;
  wallet_manager?: string;
}

export interface WalletSettings {
  defaultWalletType: string;
  autoCreateWallet: boolean;
  allowedNetworks: string[];
  maxWalletsPerUser: number;
  requireBackup: boolean;
  enableSocialRecovery: boolean;
  gaslessTransactions: boolean;
  // Enhanced settings
  sessionKeyExpiry?: number; // in hours
  maxSessionKeys?: number;
  enableAnalytics?: boolean;
  enableNotifications?: boolean;
}

export interface CreateWalletRequest {
  network?: string;
  type?: 'embedded' | 'standard';
  chain?: Chain;
  walletType?: string;
}

export interface AuthResponse {
  success: boolean;
  data: {
    user: User;
    token: string;
    expires_at: string;
    auto_created_wallet?: {
      id: string;
      network: string;
      wallet_address: string;
      is_embedded: boolean;
    };
  };
  message: string;
}

export interface SocialAuthResponse {
  success: boolean;
  data: {
    user: User;
    token: string;
    expires_at: string;
    is_new_user: boolean;
    embedded_wallet?: EmbeddedWallet;
  };
  message: string;
}

export interface WalletResponse {
  success: boolean;
  data: Wallet[];
  message: string;
}

export interface SettingsResponse {
  success: boolean;
  data: WalletSettings;
  message: string;
}

export interface ApiError {
  success: false;
  error: string;
  details?: any[];
}

// Wallet Manager Types
export interface WalletManagerState {
  account: string | null;
  chainId: string | null;
  error: string | null;
  isConnected: boolean;
  isConnecting: boolean;
  walletType: string | null;
  network?: string;
  balance?: WalletBalance;
}

export interface WalletManager {
  connectWallet: (walletType: string) => Promise<void>;
  disconnect: () => Promise<void>;
  getState: () => WalletManagerState;
  subscribe: (callback: (state: WalletManagerState) => void) => () => void;
  destroy: () => void;
  signMessage: (message: string) => Promise<string | null>;
  signTransaction: (transaction: any) => Promise<string | null>;
  sendTransaction: (transaction: any) => Promise<string | null>;
  getBalance: (address?: string) => Promise<string | null>;
  switchNetwork: (chainId: string) => Promise<boolean>;
}

// Analytics Types
export interface AnalyticsEvent {
  event: string;
  properties?: Record<string, any>;
  timestamp?: number;
  userId?: string;
}

export interface AnalyticsResponse {
  success: boolean;
  message: string;
}

// Notification Types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  action?: {
    label: string;
    url: string;
  };
}

export interface NotificationResponse {
  success: boolean;
  data: Notification[];
  message: string;
}

export interface WalletConnectorProps {
  chain?: Chain;
  walletType?: string;
  onConnect?: (wallet: WalletManager) => void;
  onDisconnect?: () => void;
  onError?: (error: string) => void;
  children?: React.ReactNode;
}

export interface ConnectButtonProps {
  chain?: Chain;
  walletType?: string;
  className?: string;
  children?: React.ReactNode;
  onConnect?: (wallet: WalletManager) => void;
  onError?: (error: string) => void;
}

export interface WalletDashboardProps {
  chains?: Chain[];
  showBalance?: boolean;
  showTransactions?: boolean;
  showSettings?: boolean;
  className?: string;
}

export interface UseWalletReturn {
  wallet: WalletManager | null;
  state: WalletManagerState;
  connect: (walletType?: WalletType) => Promise<void>;
  disconnect: () => Promise<void>;
  signMessage: (message: string) => Promise<string | null>;
  signTransaction: (transaction: any) => Promise<string | null>;
  sendTransaction: (transaction: any) => Promise<string | null>;
  getBalance: (address?: string) => Promise<string | null>;
  switchNetwork: (chainId: string) => Promise<boolean>;
  isLoading: boolean;
  error: string | null;
}

export interface UseSessionKeysReturn {
  sessionKeys: SessionKey[];
  createSessionKey: (name: string, permissions: string[], expiryHours?: number) => Promise<SessionKey>;
  revokeSessionKey: (id: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export interface UseAnalyticsReturn {
  track: (event: string, properties?: Record<string, any>) => Promise<void>;
  identify: (userId: string, traits?: Record<string, any>) => Promise<void>;
  page: (pageName: string, properties?: Record<string, any>) => Promise<void>;
}

export interface UseNotificationsReturn {
  notifications: Notification[];
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

// Email Verification and OTP Types
export interface EmailVerificationResponse {
  success: boolean;
  message: string;
  user?: User;
}

export interface OTPResponse {
  success: boolean;
  message: string;
  user?: User;
  auto_created_wallet?: {
    id: string;
    network: string;
    wallet_address: string;
    is_embedded: boolean;
  };
}

export interface AppAuthConfig {
  auth_type: 'password' | 'otp' | 'both';
  email_otp_enabled: boolean;
  google_auth_enabled: boolean;
  email_verification_required: boolean;
}

export interface AppAuthConfigResponse {
  success: boolean;
  data: {
    app: {
      id: string;
      name: string;
    };
    auth_config: AppAuthConfig;
  };
}