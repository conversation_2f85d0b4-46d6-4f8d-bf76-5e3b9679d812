import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { TokaiApi } from './api';
import { TokaiModal } from './components/TokaiModal';
import { 
  TokaiConfig, 
  User, 
  Wallet, 
  WalletSettings, 
  SessionKey,
  Notification,
  Chain,
  WalletManager,
  WalletManagerState,
  UseWalletReturn,
  UseSessionKeysReturn,
  UseAnalyticsReturn,
  UseNotificationsReturn,
  EmailVerificationResponse,
  OTPResponse,
  AppAuthConfigResponse,
  SocialProvider,
  ThemeCustomization
} from './types';

interface TokaiContextType {
  api: TokaiApi;
  user: User | null;
  wallets: Wallet[];
  settings: WalletSettings | null;
  isLoading: boolean;
  error: string | null;
  
  // App Configuration
  appConfig: {
    authType: 'password' | 'otp' | 'both';
    emailOtpEnabled: boolean;
    emailVerificationRequired: boolean;
    googleAuthEnabled: boolean;
  } | null;
  
  // Modal Management
  isModalOpen: boolean;
  openModal: () => void;
  closeModal: () => void;
  
  // Authentication
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, fullName?: string) => Promise<void>;
  sendVerificationEmail: (email: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  sendOTP: (email: string) => Promise<void>;
  verifyOTP: (email: string, otp: string) => Promise<void>;
  getAppAuthConfig: (appId: string) => Promise<AppAuthConfigResponse>;
  logout: () => Promise<void>;
  
  // Wallet Management
  createWallet: (network?: string) => Promise<Wallet>;
  refreshWallets: () => Promise<void>;
  refreshSettings: () => Promise<void>;
  
  // Enhanced Wallet Features
  getWalletBalance: (walletId: string) => Promise<any>;
  getWalletTransactions: (walletId: string, limit?: number, offset?: number) => Promise<any[]>;
  sendTransaction: (walletId: string, transaction: any) => Promise<{ transactionHash: string }>;
  signMessage: (walletId: string, message: string) => Promise<{ signature: string }>;
  
  // Session Keys
  sessionKeys: SessionKey[];
  createSessionKey: (name: string, permissions: string[], expiryHours?: number) => Promise<SessionKey>;
  revokeSessionKey: (id: string) => Promise<void>;
  refreshSessionKeys: () => Promise<void>;
  
  // Analytics
  trackEvent: (event: string, properties?: Record<string, any>) => Promise<void>;
  identifyUser: (userId: string, traits?: Record<string, any>) => Promise<void>;
  trackPage: (pageName: string, properties?: Record<string, any>) => Promise<void>;
  
  // Notifications
  notifications: Notification[];
  markNotificationAsRead: (id: string) => Promise<void>;
  markAllNotificationsAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  refreshNotifications: () => Promise<void>;
  
  // Multi-chain Support
  supportedChains: Chain[];
  getChainConfig: (chain: Chain) => Promise<any>;
  
  // External Wallet Management
  connectExternalWallet: (chain: Chain, walletType: string) => Promise<WalletManagerState>;
  disconnectExternalWallet: (chain: Chain) => Promise<void>;
  getWalletManager: (chain: Chain) => Promise<WalletManager | null>;
  
  // Utility
  clearError: () => void;
}

const TokaiContext = createContext<TokaiContextType | undefined>(undefined);

interface TokaiProviderProps {
  config?: TokaiConfig;
  backendUrl?: string;
  apiKey?: string;
  appId?: string; // App ID to fetch configuration
  children: ReactNode;
  // Modal configuration
  modalConfig?: {
    appName?: string;
    socialProviders?: SocialProvider[];
    theme?: 'light' | 'dark' | 'auto';
    customization?: ThemeCustomization;
    showEmailAuth?: boolean;
    autoCreateWallet?: boolean;
    embeddedWallets?: boolean;
    showExternalWallets?: boolean;
    supportedChains?: Chain[];
    authType?: 'password' | 'otp' | 'both';
    emailOtpEnabled?: boolean;
    emailVerificationRequired?: boolean;
  };
}

export const TokaiProvider: React.FC<TokaiProviderProps> = ({ 
  config, 
  backendUrl, 
  apiKey, 
  appId, 
  modalConfig,
  children 
}) => {
  const [api] = useState(() => {
    if (config) {
      return new TokaiApi(config);
    }
    if (backendUrl && apiKey) {
      return new TokaiApi({ apiUrl: backendUrl, apiKey });
    }
    throw new Error('Either config or both backendUrl and apiKey must be provided');
  });
  
  const [user, setUser] = useState<User | null>(null);
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [settings, setSettings] = useState<WalletSettings | null>(null);
  const [sessionKeys, setSessionKeys] = useState<SessionKey[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [supportedChains, setSupportedChains] = useState<Chain[]>([]);
  const [appConfig, setAppConfig] = useState<{
    authType: 'password' | 'otp' | 'both';
    emailOtpEnabled: boolean;
    emailVerificationRequired: boolean;
    googleAuthEnabled: boolean;
  } | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const clearError = () => setError(null);
  
  // Modal Management
  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  // Authentication methods
  const login = async (email: string, password: string) => {
    try {
      setError(null);
      setIsLoading(true);
      const response = await api.login(email, password);
      api.setToken(response.data.token);
      setUser(response.data.user);
      
      // Load all data
      await Promise.all([
        refreshWallets(),
        refreshSettings(),
        refreshSessionKeys(),
        refreshNotifications(),
        loadSupportedChains(),
      ]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, fullName?: string) => {
    try {
      setError(null);
      setIsLoading(true);
      const response = await api.register(email, password, fullName);
      
      // Check if email verification is required
      if ((response.data as any)?.requires_verification) {
        throw new Error('Please check your email to verify your account before logging in.');
      }
      
      api.setToken(response.data.token);
      setUser(response.data.user);
      
      // Load all data
      await Promise.all([
        refreshWallets(),
        refreshSettings(),
        refreshSessionKeys(),
        refreshNotifications(),
        loadSupportedChains(),
      ]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Registration failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const sendVerificationEmail = async (email: string) => {
    try {
      setError(null);
      await api.sendVerificationEmail(email);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send verification email');
      throw err;
    }
  };

  const verifyEmail = async (token: string) => {
    try {
      setError(null);
      setIsLoading(true);
      const response = await api.verifyEmail(token);
      if (response.user) {
        setUser(response.user);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Email verification failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const sendOTP = async (email: string) => {
    try {
      setError(null);
      await api.sendOTP(email);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send OTP');
      throw err;
    }
  };

  const verifyOTP = async (email: string, otp: string) => {
    try {
      setError(null);
      setIsLoading(true);
      const response = await api.verifyOTP(email, otp);
      if (response.user) {
        setUser(response.user);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'OTP verification failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const getAppAuthConfig = async (appId: string) => {
    try {
      setError(null);
      return await api.getAppAuthConfig(appId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get app auth config');
      throw err;
    }
  };

  const logout = async () => {
    try {
      await api.logout();
      setUser(null);
      setWallets([]);
      setSettings(null);
      setSessionKeys([]);
      setNotifications([]);
      setSupportedChains([]);
    } catch (err) {
      console.error('Logout error:', err);
    }
  };

  // Wallet management methods
  const createWallet = async (network?: string): Promise<Wallet> => {
    try {
      setError(null);
      const wallet = await api.createWallet({ network });
      setWallets(prev => [...prev, wallet]);
      return wallet;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create wallet');
      throw err;
    }
  };

  const refreshWallets = async () => {
    try {
      const walletList = await api.getWallets();
      setWallets(walletList);
    } catch (err) {
      console.error('Failed to refresh wallets:', err);
    }
  };

  const refreshSettings = async () => {
    try {
      const walletSettings = await api.getWalletSettings();
      setSettings(walletSettings);
    } catch (err) {
      console.error('Failed to refresh settings:', err);
    }
  };

  // Enhanced wallet features
  const getWalletBalance = async (walletId: string) => {
    try {
      return await api.getWalletBalance(walletId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get wallet balance');
      throw err;
    }
  };

  const getWalletTransactions = async (walletId: string, limit?: number, offset?: number) => {
    try {
      return await api.getWalletTransactions(walletId, limit, offset);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get wallet transactions');
      throw err;
    }
  };

  const sendTransaction = async (walletId: string, transaction: any) => {
    try {
      return await api.sendTransaction(walletId, transaction);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send transaction');
      throw err;
    }
  };

  const signMessage = async (walletId: string, message: string) => {
    try {
      return await api.signMessage(walletId, message);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sign message');
      throw err;
    }
  };

  // Session keys management
  const createSessionKey = async (name: string, permissions: string[], expiryHours?: number): Promise<SessionKey> => {
    try {
      setError(null);
      const sessionKey = await api.createSessionKey(name, permissions, expiryHours);
      setSessionKeys(prev => [...prev, sessionKey]);
      return sessionKey;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create session key');
      throw err;
    }
  };

  const revokeSessionKey = async (id: string) => {
    try {
      await api.revokeSessionKey(id);
      setSessionKeys(prev => prev.filter(key => key.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to revoke session key');
      throw err;
    }
  };

  const refreshSessionKeys = async () => {
    try {
      const keys = await api.getSessionKeys();
      setSessionKeys(keys);
    } catch (err) {
      console.error('Failed to refresh session keys:', err);
    }
  };

  // Analytics methods
  const trackEvent = async (event: string, properties?: Record<string, any>) => {
    try {
      await api.trackEvent(event, properties);
    } catch (err) {
      console.error('Failed to track event:', err);
    }
  };

  const identifyUser = async (userId: string, traits?: Record<string, any>) => {
    try {
      await api.identifyUser(userId, traits);
    } catch (err) {
      console.error('Failed to identify user:', err);
    }
  };

  const trackPage = async (pageName: string, properties?: Record<string, any>) => {
    try {
      await api.trackPage(pageName, properties);
    } catch (err) {
      console.error('Failed to track page:', err);
    }
  };

  // Notifications management
  const markNotificationAsRead = async (id: string) => {
    try {
      await api.markNotificationAsRead(id);
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, read: true }
            : notification
        )
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark notification as read');
      throw err;
    }
  };

  const markAllNotificationsAsRead = async () => {
    try {
      await api.markAllNotificationsAsRead();
      setNotifications(prev => prev.map(notification => ({ ...notification, read: true })));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark all notifications as read');
      throw err;
    }
  };

  const deleteNotification = async (id: string) => {
    try {
      await api.deleteNotification(id);
      setNotifications(prev => prev.filter(notification => notification.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete notification');
      throw err;
    }
  };

  const refreshNotifications = async () => {
    try {
      const notificationList = await api.getNotifications();
      setNotifications(notificationList);
    } catch (err) {
      console.error('Failed to refresh notifications:', err);
    }
  };

  // Multi-chain support
  const loadSupportedChains = async () => {
    try {
      const chains = await api.getSupportedChains();
      setSupportedChains(chains);
    } catch (err) {
      console.error('Failed to load supported chains:', err);
    }
  };

  const getChainConfig = async (chain: Chain) => {
    try {
      return await api.getChainConfig(chain);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get chain config');
      throw err;
    }
  };

  // External wallet management
  const connectExternalWallet = async (chain: Chain, walletType: string) => {
    try {
      return await api.connectExternalWallet(chain, walletType);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to connect external wallet');
      throw err;
    }
  };

  const disconnectExternalWallet = async (chain: Chain) => {
    try {
      await api.disconnectExternalWallet(chain);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to disconnect external wallet');
      throw err;
    }
  };

  const getWalletManager = async (chain: Chain) => {
    try {
      return await api.getWalletManager(chain);
    } catch (err) {
      console.error('Failed to get wallet manager:', err);
      return null;
    }
  };

  // Load app configuration
  const loadAppConfig = async () => {
    if (appId) {
      try {
        const response = await api.getAppAuthConfig(appId);
        setAppConfig({
          authType: response.data.auth_config.auth_type,
          emailOtpEnabled: response.data.auth_config.email_otp_enabled,
          emailVerificationRequired: response.data.auth_config.email_verification_required,
          googleAuthEnabled: response.data.auth_config.google_auth_enabled,
        });
      } catch (err) {
        console.warn('Failed to load app configuration:', err);
        // Set default config
        setAppConfig({
          authType: 'password',
          emailOtpEnabled: true,
          emailVerificationRequired: false,
          googleAuthEnabled: true,
        });
      }
    }
  };

  // Check for existing session on mount
  useEffect(() => {
    const checkSession = async () => {
      try {
        // Load app configuration first
        await loadAppConfig();
        
        // For API key authentication (sandbox), skip user auth and just load wallets
        const isApiKeyAuth = !!(backendUrl && apiKey);
        
        if (isApiKeyAuth) {
          console.log('Using API key authentication - skipping user session check');
          // API key users don't need user profile info
          await Promise.all([
            refreshWallets(),
            refreshSettings(),
            refreshSessionKeys(),
            refreshNotifications(),
            loadSupportedChains(),
          ]);
        } else {
          // JWT authentication (dashboard) - get user info
          const currentUser = await api.getCurrentUser();
          setUser(currentUser);
          await Promise.all([
            refreshWallets(),
            refreshSettings(),
            refreshSessionKeys(),
            refreshNotifications(),
            loadSupportedChains(),
          ]);
        }
      } catch (err) {
        // No valid session, user needs to login
        console.log('No valid session found:', err);
      } finally {
        setIsLoading(false);
      }
    };

    checkSession();
  }, [api, backendUrl, apiKey, appId]);

  const value: TokaiContextType = {
    api,
    user,
    wallets,
    settings,
    isLoading,
    error,
    
    // App Configuration
    appConfig,
    
    // Modal Management
    isModalOpen,
    openModal,
    closeModal,
    
    // Authentication
    login,
    register,
    sendVerificationEmail,
    verifyEmail,
    sendOTP,
    verifyOTP,
    getAppAuthConfig,
    logout,
    
    // Wallet Management
    createWallet,
    refreshWallets,
    refreshSettings,
    
    // Enhanced Wallet Features
    getWalletBalance,
    getWalletTransactions,
    sendTransaction,
    signMessage,
    
    // Session Keys
    sessionKeys,
    createSessionKey,
    revokeSessionKey,
    refreshSessionKeys,
    
    // Analytics
    trackEvent,
    identifyUser,
    trackPage,
    
    // Notifications
    notifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    deleteNotification,
    refreshNotifications,
    
    // Multi-chain Support
    supportedChains,
    getChainConfig,
    
    // External Wallet Management
    connectExternalWallet,
    disconnectExternalWallet,
    getWalletManager,
    
    // Utility
    clearError,
  };

  return (
    <TokaiContext.Provider value={value}>
      {children}
      {/* Built-in modal that's always available */}
      <TokaiModal 
        appName={modalConfig?.appName}
        socialProviders={modalConfig?.socialProviders}
        theme={modalConfig?.theme}
        customization={modalConfig?.customization}
        showEmailAuth={modalConfig?.showEmailAuth}
        autoCreateWallet={modalConfig?.autoCreateWallet}
        embeddedWallets={modalConfig?.embeddedWallets}
        showExternalWallets={modalConfig?.showExternalWallets}
        supportedChains={modalConfig?.supportedChains}
        authType={modalConfig?.authType}
        emailOtpEnabled={modalConfig?.emailOtpEnabled}
        emailVerificationRequired={modalConfig?.emailVerificationRequired}
      />
    </TokaiContext.Provider>
  );
};

export const useTokai = (): TokaiContextType => {
  const context = useContext(TokaiContext);
  if (context === undefined) {
    throw new Error('useTokai must be used within a TokaiProvider');
  }
  return context;
}; 