import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  TokaiConfig, 
  User, 
  Wallet, 
  WalletSettings, 
  AuthResponse, 
  WalletResponse, 
  SettingsResponse,
  CreateWalletRequest,
  SocialAuthResponse,
  EmbeddedWallet,
  SocialAccount,
  SessionKey,
  AnalyticsResponse,
  Notification,
  NotificationResponse,
  EmailVerificationResponse,
  OTPResponse,
  AppAuthConfigResponse,
  Chain,
  WalletManager,
  WalletManagerState
} from './types';

export class TokaiApi {
  private client: AxiosInstance;
  private token: string | null = null;

  constructor(config: TokaiConfig) {
    const baseHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-API-Key': config.apiKey,
    };
    if (config.appId) {
      baseHeaders['X-App-Id'] = config.appId;
    }

    this.client = axios.create({
      baseURL: config.apiUrl,
      headers: baseHeaders,
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        const status = error?.response?.status as number | undefined;
        const payload = error?.response?.data as any;
        // Normalize special origin error for SDK consumers
        if (status === 460 || payload?.code === 'ORIGIN_NOT_ALLOWED') {
          const e = new Error('Origin not allowed for this app');
          (e as any).code = 'ORIGIN_NOT_ALLOWED';
          (e as any).status = 460;
          throw e;
        }
        if (payload?.error) {
          const e = new Error(payload.error);
          (e as any).status = status;
          throw e;
        }
        throw error;
      }
    );
  }

  private getAuthHeaders() {
    const headers: Record<string, string> = {};
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }
    return headers;
  }

  setToken(token: string) {
    this.token = token;
  }

  clearToken() {
    this.token = null;
  }

  // Authentication
  async register(email: string, password: string, fullName?: string): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.client.post('/auth/register', {
      email,
      password,
      full_name: fullName,
    });
    return response.data;
  }

  async login(email: string, password: string): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.client.post('/auth/login', {
      email,
      password,
    });
    return response.data;
  }

  async logout(): Promise<void> {
    await this.client.post('/auth/logout', {}, { headers: this.getAuthHeaders() });
    this.clearToken();
  }

  async getCurrentUser(): Promise<User> {
    const response: AxiosResponse<{ success: boolean; data: User }> = await this.client.get('/auth/me', {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  // Email verification methods
  async sendVerificationEmail(email: string): Promise<void> {
    await this.client.post('/auth/send-verification-email', { email });
  }

  async verifyEmail(token: string): Promise<EmailVerificationResponse> {
    const response: AxiosResponse<EmailVerificationResponse> = await this.client.post('/auth/verify-email', { token });
    return response.data;
  }

  // OTP authentication methods
  async sendOTP(email: string): Promise<{ isNewUser: boolean }> {
    const response = await this.client.post('/auth/send-otp', { email });
    return response.data;
  }

  async verifyOTP(email: string, otp: string): Promise<OTPResponse> {
    const response: AxiosResponse<OTPResponse> = await this.client.post('/auth/verify-otp', { email, otp });
    return response.data;
  }

  // App authentication configuration
  async getAppAuthConfig(appId: string): Promise<AppAuthConfigResponse> {
    const response: AxiosResponse<AppAuthConfigResponse> = await this.client.get(`/app-settings/${appId}/auth-config`);
    return response.data;
  }

  // Social Authentication
  async initiateSocialAuth(provider: string, redirectUrl?: string): Promise<{ authUrl: string }> {
    const response: AxiosResponse<{ success: boolean; data: { authUrl: string } }> = await this.client.post('/auth/social/initiate', {
      provider,
      redirectUrl: redirectUrl || (typeof window !== "undefined" ? window.location.origin : 'http://localhost:3000'),
    });
    return response.data.data;
  }

  async completeSocialAuth(provider: string, code: string, state?: string): Promise<SocialAuthResponse> {
    const response: AxiosResponse<SocialAuthResponse> = await this.client.post('/auth/social/complete', {
      provider,
      code,
      state,
    });
    return response.data;
  }

  async linkSocialAccount(provider: string, code: string): Promise<SocialAccount> {
    const response: AxiosResponse<{ success: boolean; data: SocialAccount }> = await this.client.post('/auth/social/link', {
      provider,
      code,
    }, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async unlinkSocialAccount(provider: string): Promise<void> {
    await this.client.delete(`/auth/social/unlink/${provider}`, {
      headers: this.getAuthHeaders(),
    });
  }

  async getSocialAccounts(): Promise<SocialAccount[]> {
    const response: AxiosResponse<{ success: boolean; data: SocialAccount[] }> = await this.client.get('/auth/social/accounts', {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  // Wallets
  async getWallets(): Promise<Wallet[]> {
    const response: AxiosResponse<WalletResponse> = await this.client.get('/wallets', {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async createWallet(request: CreateWalletRequest = {}): Promise<Wallet> {
    const response: AxiosResponse<{ success: boolean; data: Wallet }> = await this.client.post('/wallets', request, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async getWallet(walletId: string): Promise<Wallet> {
    const response: AxiosResponse<{ success: boolean; data: Wallet }> = await this.client.get(`/wallets/${walletId}`, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async getWalletSeedPhrase(walletId: string, password: string): Promise<string> {
    const response: AxiosResponse<{ success: boolean; data: { seed_phrase: string } }> = await this.client.post(
      `/wallets/${walletId}/seed-phrase`,
      { password },
      { headers: this.getAuthHeaders() }
    );
    return response.data.data.seed_phrase;
  }

  async getWalletPrivateKey(walletId: string, password: string): Promise<string> {
    const response: AxiosResponse<{ success: boolean; data: { private_key: string } }> = await this.client.post(
      `/wallets/${walletId}/private-key`,
      { password },
      { headers: this.getAuthHeaders() }
    );
    return response.data.data.private_key;
  }

  async autoCreateWallet(): Promise<Wallet> {
    const response: AxiosResponse<{ success: boolean; data: Wallet }> = await this.client.post('/wallets/auto-create', {}, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async getSupportedNetworks(): Promise<string[]> {
    const response: AxiosResponse<{ success: boolean; data: string[] }> = await this.client.get('/wallets/networks', {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  // Enhanced Wallet Management
  async getWalletBalance(walletId: string): Promise<any> {
    const response: AxiosResponse<{ success: boolean; data: any }> = await this.client.get(`/wallets/${walletId}/balance`, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async getWalletTransactions(walletId: string, limit?: number, offset?: number): Promise<any[]> {
    const response: AxiosResponse<{ success: boolean; data: any[] }> = await this.client.get(`/wallets/${walletId}/transactions`, {
      headers: this.getAuthHeaders(),
      params: { limit, offset },
    });
    return response.data.data;
  }

  async sendTransaction(walletId: string, transaction: any): Promise<{ transactionHash: string }> {
    const response: AxiosResponse<{ success: boolean; data: { transactionHash: string } }> = await this.client.post(
      `/wallets/${walletId}/send`,
      transaction,
      { headers: this.getAuthHeaders() }
    );
    return response.data.data;
  }

  async signMessage(walletId: string, message: string): Promise<{ signature: string }> {
    const response: AxiosResponse<{ success: boolean; data: { signature: string } }> = await this.client.post(
      `/wallets/${walletId}/sign`,
      { message },
      { headers: this.getAuthHeaders() }
    );
    return response.data.data;
  }

  // Settings
  async getWalletSettings(): Promise<WalletSettings> {
    const response: AxiosResponse<SettingsResponse> = await this.client.get('/config/wallet/settings', {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async updateWalletSettings(settings: Partial<WalletSettings>): Promise<WalletSettings> {
    const response: AxiosResponse<SettingsResponse> = await this.client.put('/config/wallet/settings', settings, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  // Session Keys Management
  async getSessionKeys(): Promise<SessionKey[]> {
    const response: AxiosResponse<{ success: boolean; data: SessionKey[] }> = await this.client.get('/session-keys', {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async createSessionKey(name: string, permissions: string[], expiryHours?: number): Promise<SessionKey> {
    const response: AxiosResponse<{ success: boolean; data: SessionKey }> = await this.client.post('/session-keys', {
      name,
      permissions,
      expiry_hours: expiryHours || 24,
    }, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async revokeSessionKey(id: string): Promise<void> {
    await this.client.delete(`/session-keys/${id}`, {
      headers: this.getAuthHeaders(),
    });
  }

  async updateSessionKey(id: string, updates: Partial<SessionKey>): Promise<SessionKey> {
    const response: AxiosResponse<{ success: boolean; data: SessionKey }> = await this.client.put(`/session-keys/${id}`, updates, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  // Analytics
  async trackEvent(event: string, properties?: Record<string, any>): Promise<AnalyticsResponse> {
    const response: AxiosResponse<AnalyticsResponse> = await this.client.post('/analytics/track', {
      event,
      properties,
      timestamp: Date.now(),
    }, {
      headers: this.getAuthHeaders(),
    });
    return response.data;
  }

  async identifyUser(userId: string, traits?: Record<string, any>): Promise<AnalyticsResponse> {
    const response: AxiosResponse<AnalyticsResponse> = await this.client.post('/analytics/identify', {
      userId,
      traits,
    }, {
      headers: this.getAuthHeaders(),
    });
    return response.data;
  }

  async trackPage(pageName: string, properties?: Record<string, any>): Promise<AnalyticsResponse> {
    const response: AxiosResponse<AnalyticsResponse> = await this.client.post('/analytics/page', {
      pageName,
      properties,
      timestamp: Date.now(),
    }, {
      headers: this.getAuthHeaders(),
    });
    return response.data;
  }

  // Notifications
  async getNotifications(): Promise<Notification[]> {
    const response: AxiosResponse<NotificationResponse> = await this.client.get('/notifications', {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async markNotificationAsRead(id: string): Promise<void> {
    await this.client.put(`/notifications/${id}/read`, {}, {
      headers: this.getAuthHeaders(),
    });
  }

  async markAllNotificationsAsRead(): Promise<void> {
    await this.client.put('/notifications/read-all', {}, {
      headers: this.getAuthHeaders(),
    });
  }

  async deleteNotification(id: string): Promise<void> {
    await this.client.delete(`/notifications/${id}`, {
      headers: this.getAuthHeaders(),
    });
  }

  // Embedded Wallets
  async createEmbeddedWallet(network?: string, recoveryMethod?: 'social' | 'email' | 'sms'): Promise<EmbeddedWallet> {
    const response: AxiosResponse<{ success: boolean; data: EmbeddedWallet }> = await this.client.post('/wallets/embedded', {
      network: network || 'ethereum',
      recoveryMethod: recoveryMethod || 'social',
    }, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async deployEmbeddedWallet(walletId: string): Promise<EmbeddedWallet> {
    const response: AxiosResponse<{ success: boolean; data: EmbeddedWallet }> = await this.client.post(`/wallets/embedded/${walletId}/deploy`, {}, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async getEmbeddedWallet(): Promise<EmbeddedWallet | null> {
    try {
      const response: AxiosResponse<{ success: boolean; data: EmbeddedWallet }> = await this.client.get('/wallets/embedded', {
        headers: this.getAuthHeaders(),
      });
      return response.data.data;
    } catch (error) {
      // Return null if no embedded wallet exists
      return null;
    }
  }

  async recoverEmbeddedWallet(recoveryCode: string): Promise<EmbeddedWallet> {
    const response: AxiosResponse<{ success: boolean; data: EmbeddedWallet }> = await this.client.post('/wallets/embedded/recover', {
      recoveryCode,
    }, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  // Gasless Transactions
  async sendGaslessTransaction(to: string, data: string, value?: string): Promise<{ transactionHash: string }> {
    const response: AxiosResponse<{ success: boolean; data: { transactionHash: string } }> = await this.client.post('/wallets/gasless/send', {
      to,
      data,
      value: value || '0',
    }, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async estimateGaslessTransaction(to: string, data: string, value?: string): Promise<{ gasEstimate: string }> {
    const response: AxiosResponse<{ success: boolean; data: { gasEstimate: string } }> = await this.client.post('/wallets/gasless/estimate', {
      to,
      data,
      value: value || '0',
    }, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  // Multi-chain Support
  async getSupportedChains(): Promise<Chain[]> {
    const response: AxiosResponse<{ success: boolean; data: Chain[] }> = await this.client.get('/chains/supported', {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async getChainConfig(chain: Chain): Promise<any> {
    const response: AxiosResponse<{ success: boolean; data: any }> = await this.client.get(`/chains/${chain}/config`, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  // Wallet Manager Integration
  async getWalletManager(chain: Chain): Promise<WalletManager | null> {
    try {
      const response: AxiosResponse<{ success: boolean; data: any }> = await this.client.get(`/wallet-managers/${chain}`, {
        headers: this.getAuthHeaders(),
      });
      return response.data.data;
    } catch (error) {
      return null;
    }
  }

  async connectExternalWallet(chain: Chain, walletType: string): Promise<WalletManagerState> {
    const response: AxiosResponse<{ success: boolean; data: WalletManagerState }> = await this.client.post('/wallet-managers/connect', {
      chain,
      walletType,
    }, {
      headers: this.getAuthHeaders(),
    });
    return response.data.data;
  }

  async disconnectExternalWallet(chain: Chain): Promise<void> {
    await this.client.post('/wallet-managers/disconnect', {
      chain,
    }, {
      headers: this.getAuthHeaders(),
    });
  }
} 