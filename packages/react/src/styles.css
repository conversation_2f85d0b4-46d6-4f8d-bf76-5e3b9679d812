/* Tokai React SDK Styles */

/* Base Styles */
.tokai-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #333;
  line-height: 1.6;
}

/* Form Styles */
.tokai-form-group {
  margin-bottom: 1rem;
}

.tokai-form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.tokai-input,
.tokai-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.tokai-input:focus,
.tokai-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tokai-input:disabled,
.tokai-select:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
}

/* <PERSON><PERSON> Styles */
.tokai-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  background-color: #3b82f6;
  color: white;
}

.tokai-button:hover:not(:disabled) {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.tokai-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.tokai-button-secondary {
  background-color: #6b7280;
  color: white;
}

.tokai-button-secondary:hover:not(:disabled) {
  background-color: #4b5563;
}

.tokai-button-danger {
  background-color: #ef4444;
  color: white;
}

.tokai-button-danger:hover:not(:disabled) {
  background-color: #dc2626;
}

.tokai-button-small {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

/* Status Indicators */
.tokai-status {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.tokai-status.active {
  background-color: #dcfce7;
  color: #166534;
}

.tokai-status.inactive {
  background-color: #fef2f2;
  color: #991b1b;
}

.tokai-status.expired {
  background-color: #fef3c7;
  color: #92400e;
}

/* Card Styles */
.tokai-wallet-card,
.tokai-session-key-card,
.tokai-notification-card,
.tokai-balance-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.15s ease-in-out;
}

.tokai-wallet-card:hover,
.tokai-session-key-card:hover,
.tokai-notification-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.tokai-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.tokai-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

/* Dashboard Styles */
.tokai-dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0.5rem;
  color: white;
}

.tokai-dashboard-stats {
  display: flex;
  gap: 2rem;
}

.tokai-stat {
  text-align: center;
}

.tokai-stat-label {
  display: block;
  font-size: 0.75rem;
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

.tokai-stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
}

.tokai-dashboard-section {
  margin-bottom: 2rem;
}

.tokai-dashboard-section h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

/* Wallet Details */
.tokai-wallet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.tokai-wallet-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

.tokai-wallet-details p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.tokai-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
}

/* Balance Styles */
.tokai-balance {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #f3f4f6;
}

.tokai-balance p {
  margin: 0;
  font-weight: 600;
  color: #059669;
}

.tokai-balances {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

/* Session Keys */
.tokai-session-key-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.tokai-session-key-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

.tokai-session-key-details p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: #6b7280;
}

/* Notifications */
.tokai-notifications-controls {
  display: flex;
  gap: 0.5rem;
}

.tokai-notification-card {
  position: relative;
}

.tokai-notification-card.unread {
  border-left: 4px solid #3b82f6;
}

.tokai-notification-card.info {
  border-left-color: #3b82f6;
}

.tokai-notification-card.success {
  border-left-color: #10b981;
}

.tokai-notification-card.warning {
  border-left-color: #f59e0b;
}

.tokai-notification-card.error {
  border-left-color: #ef4444;
}

.tokai-notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.tokai-notification-header h4 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
}

.tokai-timestamp {
  font-size: 0.75rem;
  color: #9ca3af;
}

.tokai-notification-message {
  margin: 0.5rem 0;
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.5;
}

.tokai-action-link {
  display: inline-block;
  margin-top: 0.5rem;
  color: #3b82f6;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
}

.tokai-action-link:hover {
  text-decoration: underline;
}

.tokai-notification-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

/* Connect Button */
.tokai-connect-button {
  display: inline-block;
}

.tokai-connected-state {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background-color: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 0.375rem;
}

.tokai-account {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #0c4a6e;
  word-break: break-all;
}

/* Loading States */
.tokai-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.tokai-loading::before {
  content: '';
  width: 1rem;
  height: 1rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.tokai-error {
  padding: 0.75rem;
  margin-bottom: 1rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
  color: #991b1b;
  font-size: 0.875rem;
}

/* Empty States */
.tokai-empty-state {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.tokai-empty-state p {
  margin: 0;
  font-size: 0.875rem;
}

/* Settings */
.tokai-settings {
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
}

.tokai-settings p {
  margin: 0.5rem 0;
  font-size: 0.875rem;
  color: #374151;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tokai-dashboard-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .tokai-dashboard-stats {
    gap: 1rem;
  }

  .tokai-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .tokai-notifications-controls {
    flex-direction: column;
  }

  .tokai-connected-state {
    flex-direction: column;
    align-items: flex-start;
  }

  .tokai-balances {
    grid-template-columns: 1fr;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .tokai-container {
    color: #e5e7eb;
  }

  .tokai-input,
  .tokai-select {
    background-color: #374151;
    border-color: #4b5563;
    color: #e5e7eb;
  }

  .tokai-wallet-card,
  .tokai-session-key-card,
  .tokai-notification-card,
  .tokai-balance-card {
    background-color: #1f2937;
    border-color: #374151;
  }

  .tokai-header {
    border-bottom-color: #374151;
  }

  .tokai-header h3,
  .tokai-wallet-header h4,
  .tokai-session-key-header h4,
  .tokai-notification-header h4 {
    color: #e5e7eb;
  }

  .tokai-wallet-details p,
  .tokai-session-key-details p,
  .tokai-notification-message {
    color: #9ca3af;
  }

  .tokai-loading {
    color: #9ca3af;
  }

  .tokai-settings {
    background-color: #374151;
  }

  .tokai-settings p {
    color: #9ca3af;
  }
}