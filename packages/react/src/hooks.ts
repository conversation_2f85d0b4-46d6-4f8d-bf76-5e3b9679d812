//
import { useState, useEffect, useCallback } from 'react';
import { useTokai } from './context';
import { 
  Chain, 
  WalletManager, 
  WalletManagerState, 
  UseWalletReturn,
  UseSessionKeysReturn,
  UseAnalyticsReturn,
  UseNotificationsReturn,
  WalletType,
  WalletTypeForChain,
  AppAuthConfigResponse
} from './types';

// Enhanced Wallet Hook with proper TypeScript types
export const useWallet = <T extends Chain>(
  chain?: T, 
  walletType?: WalletTypeForChain<T>
): UseWalletReturn => {
  const { 
    getWalletManager,
    error,
  } = useTokai();
  
  const [wallet, setWallet] = useState<WalletManager | null>(null);
  const [state, setState] = useState<WalletManagerState>({
    account: null,
    chainId: null,
    error: null,
    isConnected: false,
    isConnecting: false,
    walletType: null,
    network: undefined,
    balance: undefined,
  });
  const [isLoading, setIsLoading] = useState(false);

  // Default wallet types for each chain with proper typing
  const getDefaultWalletType = (chain: Chain): WalletType => {
    const defaults: Record<Chain, WalletType> = {
      ethereum: 'metamask',
      solana: 'phantom',
      polkadot: 'polkadotjs',
      algorand: 'pera',
      fivire: '5ire',
      astar: 'subwallet',
      avalanche: 'core',
      cardano: 'nami',
      cosmos: 'keplr',
      near: 'sender'
    };
    return defaults[chain] || 'metamask';
  };

  const connect = useCallback(async (walletTypeToConnect?: WalletType) => {
    if (!chain) {
      throw new Error('Chain is required for wallet connection');
    }

    // Determine the wallet type to use with proper typing
    let targetWalletType: WalletType;
    
    if (walletTypeToConnect) {
      // Use the explicitly passed wallet type
      targetWalletType = walletTypeToConnect;
    } else if (walletType) {
      // Use the wallet type from the hook initialization
      targetWalletType = walletType;
    } else {
      // Use the default wallet type for this chain
      targetWalletType = getDefaultWalletType(chain);
      console.log(`No wallet type specified, using default for ${chain}: ${targetWalletType}`);
    }

    setIsLoading(true);

    try {
      // Get the appropriate wallet manager for this chain
      const walletManager = await getWalletManager(chain);
      if (!walletManager) {
        throw new Error(`No wallet manager available for chain: ${chain}`);
      }

      // Connect using the specific wallet type
      await walletManager.connectWallet(targetWalletType);
      setWallet(walletManager);

      // Subscribe to state changes
      const _unsubscribe = walletManager.subscribe((newState) => {
        setState(newState);
      });

      // Store cleanup function for later use
      // Note: We don't return the cleanup function as the type expects Promise<void>
      // The cleanup will be handled in the disconnect function or component unmount
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect wallet';
      setState(prev => ({ ...prev, error: errorMessage }));
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [chain, walletType, getWalletManager]);

  const disconnect = useCallback(async () => {
    if (!wallet) return;

    try {
      await wallet.disconnect();
      setWallet(null);
      setState({
        account: null,
        chainId: null,
        error: null,
        isConnected: false,
        isConnecting: false,
        walletType: null,
        network: undefined,
        balance: undefined,
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to disconnect wallet';
      setState(prev => ({ ...prev, error: errorMessage }));
    }
  }, [wallet]);

  const signMessage = useCallback(async (message: string) => {
    if (!wallet) return null;
    return await wallet.signMessage(message);
  }, [wallet]);

  const signTransaction = useCallback(async (transaction: any) => {
    if (!wallet) return null;
    return await wallet.signTransaction(transaction);
  }, [wallet]);

  const sendTransaction = useCallback(async (transaction: any) => {
    if (!wallet) return null;
    return await wallet.sendTransaction(transaction);
  }, [wallet]);

  const getBalance = useCallback(async (address?: string) => {
    if (!wallet) return null;
    return await wallet.getBalance(address);
  }, [wallet]);

  const switchNetwork = useCallback(async (chainId: string) => {
    if (!wallet) return false;
    return await wallet.switchNetwork(chainId);
  }, [wallet]);

  return {
    wallet,
    state,
    connect,
    disconnect,
    signMessage,
    signTransaction,
    sendTransaction,
    getBalance,
    switchNetwork,
    isLoading,
    error,
  };
};

// Session Keys Hook
export const useSessionKeys = (): UseSessionKeysReturn => {
  const { 
    sessionKeys, 
    createSessionKey, 
    revokeSessionKey, 
    refreshSessionKeys,
    error,
    clearError 
  } = useTokai();
  
  const [isLoading, setIsLoading] = useState(false);

  const createKey = useCallback(async (name: string, permissions: string[], expiryHours?: number) => {
    setIsLoading(true);
    clearError();
    
    try {
      return await createSessionKey(name, permissions, expiryHours);
    } finally {
      setIsLoading(false);
    }
  }, [createSessionKey, clearError]);

  const revokeKey = useCallback(async (id: string) => {
    setIsLoading(true);
    clearError();
    
    try {
      await revokeSessionKey(id);
    } finally {
      setIsLoading(false);
    }
  }, [revokeSessionKey, clearError]);

  // Refresh session keys on mount
  useEffect(() => {
    refreshSessionKeys();
  }, [refreshSessionKeys]);

  return {
    sessionKeys,
    createSessionKey: createKey,
    revokeSessionKey: revokeKey,
    isLoading,
    error,
  };
};

// Analytics Hook
export const useAnalytics = (): UseAnalyticsReturn => {
  const { trackEvent, identifyUser, trackPage } = useTokai();

  const track = useCallback(async (event: string, properties?: Record<string, any>) => {
    await trackEvent(event, properties);
  }, [trackEvent]);

  const identify = useCallback(async (userId: string, traits?: Record<string, any>) => {
    await identifyUser(userId, traits);
  }, [identifyUser]);

  const page = useCallback(async (pageName: string, properties?: Record<string, any>) => {
    await trackPage(pageName, properties);
  }, [trackPage]);

  return {
    track,
    identify,
    page,
  };
};

// Notifications Hook
export const useNotifications = (): UseNotificationsReturn => {
  const { 
    notifications, 
    markNotificationAsRead, 
    markAllNotificationsAsRead, 
    deleteNotification,
    refreshNotifications,
    error,
    clearError 
  } = useTokai();
  
  const [isLoading, setIsLoading] = useState(false);

  const markAsRead = useCallback(async (id: string) => {
    setIsLoading(true);
    clearError();
    
    try {
      await markNotificationAsRead(id);
    } finally {
      setIsLoading(false);
    }
  }, [markNotificationAsRead, clearError]);

  const markAllAsRead = useCallback(async () => {
    setIsLoading(true);
    clearError();
    
    try {
      await markAllNotificationsAsRead();
    } finally {
      setIsLoading(false);
    }
  }, [markAllNotificationsAsRead, clearError]);

  const deleteNotif = useCallback(async (id: string) => {
    setIsLoading(true);
    clearError();
    
    try {
      await deleteNotification(id);
    } finally {
      setIsLoading(false);
    }
  }, [deleteNotification, clearError]);

  // Refresh notifications on mount
  useEffect(() => {
    refreshNotifications();
  }, [refreshNotifications]);

  return {
    notifications,
    markAsRead,
    markAllAsRead,
    deleteNotification: deleteNotif,
    isLoading,
    error,
  };
};

// Multi-chain Wallet Hook
export const useMultiChainWallet = (chains: Chain[]) => {
  const { supportedChains, getChainConfig, getWalletManager } = useTokai();
  const [activeChains, setActiveChains] = useState<Record<Chain, WalletManager | null>>({} as Record<Chain, WalletManager | null>);
  const [isLoading, setIsLoading] = useState(false);

  const connectChain = useCallback(async (chain: Chain, walletType: WalletType) => {
    setIsLoading(true);
    
    try {
      const wallet = await getWalletManager(chain);
      if (wallet) {
        await wallet.connectWallet(walletType);
        setActiveChains(prev => ({ ...prev, [chain]: wallet }));
      }
    } finally {
      setIsLoading(false);
    }
  }, [getWalletManager]);

  const disconnectChain = useCallback(async (chain: Chain) => {
    const wallet = activeChains[chain];
    if (wallet) {
      await wallet.disconnect();
      setActiveChains(prev => ({ ...prev, [chain]: null }));
    }
  }, [activeChains]);

  const getChainWallet = useCallback((chain: Chain) => {
    return activeChains[chain] || null;
  }, [activeChains]);

  return {
    activeChains,
    connectChain,
    disconnectChain,
    getChainWallet,
    supportedChains,
    getChainConfig,
    isLoading,
  };
};

// Transaction Hook
export const useTransaction = (walletId?: string) => {
  const { sendTransaction, signMessage, getWalletBalance, error, clearError } = useTokai();
  const [isLoading, setIsLoading] = useState(false);
  const [transactionHash, setTransactionHash] = useState<string | null>(null);

  const send = useCallback(async (transaction: any) => {
    if (!walletId) {
      throw new Error('Wallet ID is required');
    }

    setIsLoading(true);
    clearError();
    setTransactionHash(null);

    try {
      const result = await sendTransaction(walletId, transaction);
      setTransactionHash(result.transactionHash);
      return result;
    } finally {
      setIsLoading(false);
    }
  }, [walletId, sendTransaction, clearError]);

  const sign = useCallback(async (message: string) => {
    if (!walletId) {
      throw new Error('Wallet ID is required');
    }

    setIsLoading(true);
    clearError();

    try {
      return await signMessage(walletId, message);
    } finally {
      setIsLoading(false);
    }
  }, [walletId, signMessage, clearError]);

  const getBalance = useCallback(async () => {
    if (!walletId) {
      throw new Error('Wallet ID is required');
    }

    return await getWalletBalance(walletId);
  }, [walletId, getWalletBalance]);

  return {
    send,
    sign,
    getBalance,
    transactionHash,
    isLoading,
    error,
  };
};

// Wallet List Hook
export const useWalletList = () => {
  const { wallets, refreshWallets, createWallet, settings, error, clearError } = useTokai();
  const [isLoading, setIsLoading] = useState(false);

  const create = useCallback(async (network?: string) => {
    setIsLoading(true);
    clearError();

    try {
      return await createWallet(network);
    } finally {
      setIsLoading(false);
    }
  }, [createWallet, clearError]);

  const refresh = useCallback(async () => {
    setIsLoading(true);
    
    try {
      await refreshWallets();
    } finally {
      setIsLoading(false);
    }
  }, [refreshWallets]);

  return {
    wallets,
    createWallet: create,
    refreshWallets: refresh,
    settings,
    isLoading,
    error,
    clearError,
  };
};

// Settings Hook
export const useWalletSettings = () => {
  const { settings, refreshSettings, error, clearError } = useTokai();
  const [isLoading, setIsLoading] = useState(false);

  const refresh = useCallback(async () => {
    setIsLoading(true);
    
    try {
      await refreshSettings();
    } finally {
      setIsLoading(false);
    }
  }, [refreshSettings]);

  return {
    settings,
    refreshSettings: refresh,
    isLoading,
    error,
  };
};

// Email Verification Hook
export const useEmailVerification = () => {
  const { sendVerificationEmail, verifyEmail, error, isLoading } = useTokai();
  
  return {
    sendVerificationEmail,
    verifyEmail,
    error,
    isLoading,
  };
};

// OTP Authentication Hook
export const useOTPAuth = () => {
  const { sendOTP, verifyOTP, error, isLoading } = useTokai();
  
  return {
    sendOTP,
    verifyOTP,
    error,
    isLoading,
  };
};

// App Authentication Configuration Hook
export const useAppAuthConfig = (appId: string) => {
  const { getAppAuthConfig, error } = useTokai();
  const [config, setConfig] = useState<AppAuthConfigResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const loadConfig = useCallback(async () => {
    if (!appId) return;
    
    setIsLoading(true);
    try {
      const response = await getAppAuthConfig(appId);
      setConfig(response);
    } catch (err) {
      console.error('Failed to load app auth config:', err);
    } finally {
      setIsLoading(false);
    }
  }, [appId, getAppAuthConfig]);

  useEffect(() => {
    loadConfig();
  }, [loadConfig]);

  return {
    config,
    loadConfig,
    isLoading,
    error,
  };
};

/**
 * Hook for managing the Tokai wallet modal
 * Programmatic control similar to other wallet SDK modals
 */
export const useTokaiModal = () => {
  const { isModalOpen, openModal, closeModal, appConfig } = useTokai();
  
  return {
    isModalOpen,
    openModal,
    closeModal,
    appConfig,
  };
};