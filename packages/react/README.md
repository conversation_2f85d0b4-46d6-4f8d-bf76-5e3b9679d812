# 🚀 Tokai React SDK

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://www.npmjs.com/package/@tokai/react)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

A comprehensive React SDK for integrating multi-chain wallet management, authentication, and blockchain functionality into your applications.

## ✨ Features

- **🔐 Multi-Chain Support**: Ethereum, Solana, Polkadot, Algorand, Fivire, Astar, Avalanche, Cardano, Cosmos, NEAR
- **👛 Wallet Management**: Embedded wallets, external wallet connections, balance tracking
- **🔑 Session Keys**: Delegated permissions with time-limited access
- **📊 Analytics**: Built-in event tracking and user identification
- **🔔 Notifications**: Real-time notification system
- **🎨 Modern UI**: Beautiful, responsive components with dark mode support
- **⚡ TypeScript**: Full TypeScript support with excellent IntelliSense
- **🛡️ Security**: Secure authentication with social login support
- **🚀 Built-in Modal**: Provider-managed modal with automatic feature flag detection
- **🎯 Zero Setup**: No manual modal configuration required

## 📦 Installation

```bash
npm install @tokai/react
# or
yarn add @tokai/react
```

## 🚀 Quick Start

### 1. Setup Provider

```tsx
import { TokaiProvider } from '@tokai/react';

function App() {
  return (
    <TokaiProvider 
      backendUrl="https://your-backend.com" 
      apiKey="your-api-key"
      appId="your-app-id" // Fetches app configuration automatically
      modalConfig={{
        appName: "My DApp",
        theme: "dark",
        showExternalWallets: true,
        supportedChains: ['ethereum', 'solana'],
        // Feature flags (optional - will use appConfig if not provided)
        authType: 'both',
        emailOtpEnabled: true,
        emailVerificationRequired: false,
      }}
    >
      <YourApp />
    </TokaiProvider>
  );
}
```

### 2. Basic Wallet Connection

```tsx
import { TokaiConnectButton } from '@tokai/react';

function WalletConnect() {
  return (
    <TokaiConnectButton 
      onSuccess={(user) => console.log('Connected:', user)}
      onError={(error) => console.error('Error:', error)}
    >
      Connect Wallet
    </TokaiConnectButton>
  );
}
```

### 3. Configuration Flow

1. **Provider loads app config** using `appId`
2. **Components use config** from context automatically
3. **No need to pass appId** to individual components

```tsx
// ✅ Correct: appId in provider
<TokaiProvider appId="your-app-id">
  <TokaiConnectButton /> {/* Uses provider's config */}
</TokaiProvider>

// ❌ Wrong: appId in component
<TokaiConnectButton appId="your-app-id" /> {/* Don't do this */}
```

### 4. Programmatic Modal Control

```tsx
import { useTokaiModal } from '@tokai/react';

function CustomConnectButton() {
  const { openModal, closeModal, isModalOpen } = useTokaiModal();
  
  return (
    <div>
      <button onClick={openModal}>Custom Connect</button>
      {/* Modal is automatically available - no need to add TokaiModal */}
    </div>
  );
}
```

### 5. Wallet Dashboard

```tsx
import { TokaiWalletDashboard } from '@tokai/react';

function Dashboard() {
  return (
    <TokaiWalletDashboard 
      chains={['ethereum', 'solana']}
      showBalance={true}
      showTransactions={true}
      showSettings={true}
    />
  );
}
```

## 🎯 Core Hooks

### Authentication Hooks

#### useEmailVerification

Handle email verification for user accounts.

```tsx
import { useEmailVerification } from '@tokai/react';

function EmailVerification() {
  const { 
    sendVerificationEmail, 
    verifyEmail, 
    error, 
    isLoading 
  } = useEmailVerification();

  const handleSendVerification = async (email: string) => {
    try {
      await sendVerificationEmail(email);
      console.log('Verification email sent!');
    } catch (error) {
      console.error('Failed to send verification:', error);
    }
  };

  const handleVerifyEmail = async (token: string) => {
    try {
      await verifyEmail(token);
      console.log('Email verified successfully!');
    } catch (error) {
      console.error('Failed to verify email:', error);
    }
  };

  return (
    <div>
      {/* Your verification UI */}
    </div>
  );
}
```

#### useOTPAuth

Handle OTP (One-Time Password) authentication.

```tsx
import { useOTPAuth } from '@tokai/react';

function OTPLogin() {
  const { 
    sendOTP, 
    verifyOTP, 
    error, 
    isLoading 
  } = useOTPAuth();

  const handleSendOTP = async (email: string) => {
    try {
      await sendOTP(email);
      console.log('OTP sent to email!');
    } catch (error) {
      console.error('Failed to send OTP:', error);
    }
  };

  const handleVerifyOTP = async (email: string, otp: string) => {
    try {
      await verifyOTP(email, otp);
      console.log('OTP verified successfully!');
    } catch (error) {
      console.error('Failed to verify OTP:', error);
    }
  };

  return (
    <div>
      {/* Your OTP login UI */}
    </div>
  );
}
```

#### App Configuration (Context)

The app configuration is automatically fetched by the provider and available through context.

```tsx
import { useTokai } from '@tokai/react';

function MyComponent() {
  const { appConfig } = useTokai();

  if (!appConfig) {
    return <div>Loading app configuration...</div>;
  }

  return (
    <div>
      <h3>Authentication Configuration</h3>
      <p>Auth Type: {appConfig.authType}</p>
      <p>Email OTP Enabled: {appConfig.emailOtpEnabled ? 'Yes' : 'No'}</p>
      <p>Email Verification Required: {appConfig.emailVerificationRequired ? 'Yes' : 'No'}</p>
      <p>Google Auth Enabled: {appConfig.googleAuthEnabled ? 'Yes' : 'No'}</p>
    </div>
  );
}
```

#### useTokaiModal

Programmatically control the wallet modal.

```tsx
import { useTokaiModal } from '@tokai/react';

function MyApp() {
  const { isModalOpen, openModal, closeModal, appConfig } = useTokaiModal();

  return (
    <div>
      <button onClick={openModal}>
        Open Wallet Modal
      </button>
      
      {/* Modal is automatically rendered by the provider */}
      {/* No need to manually add TokaiModal component */}
      
      {/* Access app configuration */}
      {appConfig && (
        <div>
          <p>Auth Type: {appConfig.authType}</p>
          <p>Email OTP: {appConfig.emailOtpEnabled ? 'Enabled' : 'Disabled'}</p>
        </div>
      )}
    </div>
  );
}
```

#### useAppAuthConfig (Legacy)

Get app-specific authentication configuration manually.

```tsx
import { useAppAuthConfig } from '@tokai/react';

function AppAuthConfig({ appId }: { appId: string }) {
  const { 
    config, 
    loadConfig, 
    isLoading, 
    error 
  } = useAppAuthConfig(appId);

  if (isLoading) return <div>Loading auth config...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h3>Authentication Configuration</h3>
      <p>Auth Type: {config?.data.auth_config.auth_type}</p>
      <p>Email OTP Enabled: {config?.data.auth_config.email_otp_enabled ? 'Yes' : 'No'}</p>
      <p>Email Verification Required: {config?.data.auth_config.email_verification_required ? 'Yes' : 'No'}</p>
    </div>
  );
}
```

### useWallet

Connect to external wallets with full transaction support.

```tsx
import { useWallet } from '@tokai/react';

function WalletComponent() {
  const { 
    connect, 
    disconnect, 
    state, 
    signMessage, 
    sendTransaction,
    isLoading,
    error 
  } = useWallet('ethereum', 'metamask');

  const handleConnect = async () => {
    try {
      await connect('metamask');
    } catch (err) {
      console.error('Connection failed:', err);
    }
  };

  const handleSign = async () => {
    const signature = await signMessage('Hello World!');
    console.log('Signature:', signature);
  };

  const handleSend = async () => {
    const txHash = await sendTransaction({
      to: '0x...',
      value: '0x0',
      data: '0x...'
    });
    console.log('Transaction hash:', txHash);
  };

  return (
    <div>
      {state.isConnected ? (
        <div>
          <p>Connected: {state.account}</p>
          <button onClick={handleSign}>Sign Message</button>
          <button onClick={handleSend}>Send Transaction</button>
          <button onClick={disconnect}>Disconnect</button>
        </div>
      ) : (
        <button onClick={handleConnect} disabled={isLoading}>
          {isLoading ? 'Connecting...' : 'Connect Wallet'}
        </button>
      )}
      {error && <p>Error: {error}</p>}
    </div>
  );
}
```

### useSessionKeys

Manage delegated permissions with session keys.

```tsx
import { useSessionKeys } from '@tokai/react';

function SessionKeysComponent() {
  const { 
    sessionKeys, 
    createSessionKey, 
    revokeSessionKey, 
    isLoading 
  } = useSessionKeys();

  const handleCreate = async () => {
    try {
      const sessionKey = await createSessionKey(
        'Trading Bot', 
        ['send_transaction', 'sign_message'], 
        24 // 24 hours
      );
      console.log('Created session key:', sessionKey);
    } catch (err) {
      console.error('Failed to create session key:', err);
    }
  };

  const handleRevoke = async (id: string) => {
    try {
      await revokeSessionKey(id);
      console.log('Session key revoked');
    } catch (err) {
      console.error('Failed to revoke session key:', err);
    }
  };

  return (
    <div>
      <button onClick={handleCreate} disabled={isLoading}>
        Create Session Key
      </button>
      
      <div>
        {sessionKeys.map((key) => (
          <div key={key.id}>
            <h4>{key.name}</h4>
            <p>Expires: {new Date(key.expires_at).toLocaleString()}</p>
            <p>Permissions: {key.permissions.join(', ')}</p>
            <button onClick={() => handleRevoke(key.id)}>
              Revoke
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
```

### useNotifications

Manage real-time notifications.

```tsx
import { useNotifications } from '@tokai/react';

function NotificationsComponent() {
  const { 
    notifications, 
    markAsRead, 
    markAllAsRead, 
    deleteNotification 
  } = useNotifications();

  return (
    <div>
      <button onClick={markAllAsRead}>Mark All Read</button>
      
      <div>
        {notifications.map((notification) => (
          <div key={notification.id}>
            <h4>{notification.title}</h4>
            <p>{notification.message}</p>
            <span>{new Date(notification.timestamp).toLocaleString()}</span>
            
            {!notification.read && (
              <button onClick={() => markAsRead(notification.id)}>
                Mark Read
              </button>
            )}
            
            <button onClick={() => deleteNotification(notification.id)}>
              Delete
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
```

### useAnalytics

Track events and user behavior.

```tsx
import { useAnalytics } from '@tokai/react';

function AnalyticsComponent() {
  const { track, identify, page } = useAnalytics();

  const handleUserAction = async () => {
    await track('button_clicked', {
      button_name: 'connect_wallet',
      user_id: '123'
    });
  };

  const handleUserLogin = async () => {
    await identify('user_123', {
      email: '<EMAIL>',
      plan: 'premium'
    });
  };

  const handlePageView = async () => {
    await page('wallet_dashboard', {
      referrer: 'homepage'
    });
  };

  return (
    <div>
      <button onClick={handleUserAction}>Track Action</button>
      <button onClick={handleUserLogin}>Identify User</button>
      <button onClick={handlePageView}>Track Page View</button>
    </div>
  );
}
```

## 🎨 Components

### AuthExample

A comprehensive example component demonstrating email verification and OTP authentication.

```tsx
import { AuthExample } from '@tokai/react';

function App() {
  return (
    <AuthExample appId="your-app-id" />
  );
}
```

This component includes:
- Email verification flow
- OTP authentication flow
- App configuration display
- Error handling
- Loading states

### TokaiLogin & TokaiRegister

Beautiful authentication components with built-in error handling.

```tsx
import { TokaiLogin, TokaiRegister } from '@tokai/react';

function AuthComponent() {
  const [isLogin, setIsLogin] = useState(true);

  return (
    <div>
      {isLogin ? (
        <TokaiLogin 
          onSuccess={() => console.log('Login successful!')}
          className="custom-login-styles"
        />
      ) : (
        <TokaiRegister 
          onSuccess={() => console.log('Registration successful!')}
          className="custom-register-styles"
        />
      )}
      
      <button onClick={() => setIsLogin(!isLogin)}>
        {isLogin ? 'Need an account?' : 'Already have an account?'}
      </button>
    </div>
  );
}
```

### TokaiWalletList

Display and manage user wallets.

```tsx
import { TokaiWalletList } from '@tokai/react';

function WalletListComponent() {
  return (
    <TokaiWalletList className="custom-wallet-list" />
  );
}
```

### TokaiSessionKeys

Manage session keys with full CRUD operations.

```tsx
import { TokaiSessionKeys } from '@tokai/react';

function SessionKeysComponent() {
  return (
    <TokaiSessionKeys className="custom-session-keys" />
  );
}
```

### TokaiNotifications

Real-time notification management.

```tsx
import { TokaiNotifications } from '@tokai/react';

function NotificationsComponent() {
  return (
    <TokaiNotifications className="custom-notifications" />
  );
}
```

### TokaiWalletDashboard

Complete wallet management dashboard.

```tsx
import { TokaiWalletDashboard } from '@tokai/react';

function DashboardComponent() {
  return (
    <TokaiWalletDashboard 
      chains={['ethereum', 'solana', 'polkadot']}
      showBalance={true}
      showTransactions={true}
      showSettings={true}
      className="custom-dashboard"
    />
  );
}
```

## 🔧 Configuration

### Provider Configuration

```tsx
<TokaiProvider 
  config={{
    apiUrl: 'https://your-backend.com',
    apiKey: 'your-api-key',
    appName: 'My App',
    theme: 'dark', // 'light' | 'dark' | 'auto'
    socialLogins: [
      { id: 'google', enabled: true, clientId: 'your-google-client-id' },
      { id: 'discord', enabled: true },
      { id: 'twitter', enabled: true },
      { id: 'github', enabled: true }
    ],
    embeddedWallets: true,
    crossAppIdentity: true,
    supportedChains: ['ethereum', 'solana', 'polkadot'],
    defaultChain: 'ethereum',
    walletConnectProjectId: 'your-walletconnect-project-id',
    sessionKeys: true,
    analytics: true,
    customization: {
      primaryColor: '#3b82f6',
      backgroundColor: '#ffffff',
      textColor: '#333333',
      borderRadius: '0.375rem',
      fontFamily: 'Inter, sans-serif',
      brandLogo: 'https://your-logo.com/logo.svg'
    }
  }}
>
  <YourApp />
</TokaiProvider>
```

## 🔗 External Wallet Support

The wallet connect button supports both **embedded wallets** (with email/OTP auth) and **external wallets** (like MetaMask).

### Supported External Wallets

#### **Ethereum:**
- **MetaMask** 🦊 - Most popular Ethereum wallet
- **WalletConnect** 📱 - Mobile wallet connection
- **Coinbase Wallet** 🪙 - Coinbase's wallet

#### **Solana:**
- **Phantom** 👻 - Popular Solana wallet
- **Solflare** 🔥 - Alternative Solana wallet

### Usage

```tsx
// ✅ Correct: Uses provider's configuration
<TokaiProvider 
  appId="your-app-id"
  modalConfig={{
    showExternalWallets: true,
    supportedChains: ['ethereum', 'solana'],
  }}
>
  <TokaiConnectButton />
</TokaiProvider>

// ✅ With overrides
<TokaiConnectButton 
  appName="My DApp"
  theme="dark"
  onSuccess={(user) => console.log('Connected:', user)}
  onError={(error) => console.error('Error:', error)}
  // Optional: Override feature flags from provider
  authType="both"
  emailOtpEnabled={true}
  emailVerificationRequired={false}
/>
```

### Authentication Flow

#### **Embedded Wallets:**
1. User clicks connect → Email/OTP auth modal
2. User authenticates → Embedded wallet created
3. User gets full wallet functionality

#### **External Wallets:**
1. User clicks connect → External wallet modal
2. User selects wallet → Direct connection
3. User gets immediate wallet access

## 🚀 Provider-Based Modal Architecture

The Tokai SDK uses a **provider-managed modal system** that automatically handles authentication and wallet connections.

### How It Works

1. **Provider Setup**: Configure modal options in the provider
2. **Automatic Rendering**: Modal is automatically available throughout your app
3. **Context Access**: Use `useTokaiModal()` to control the modal programmatically

### Provider Configuration

```tsx
<TokaiProvider 
  appId="your-app-id"
  modalConfig={{
    appName: "My DApp",
    theme: "dark",
    showExternalWallets: true,
    supportedChains: ['ethereum', 'solana'],
    // Feature flags
    authType: 'both',
    emailOtpEnabled: true,
    emailVerificationRequired: false,
    // Customization
    customization: {
      primaryColor: '#6366f1',
      borderRadius: '12px',
      fontFamily: 'Inter, sans-serif',
    },
    // Social providers
    socialProviders: [
      { id: 'google', enabled: true },
      { id: 'discord', enabled: true },
      { id: 'twitter', enabled: true },
      { id: 'github', enabled: true },
    ],
  }}
>
  <YourApp />
</TokaiProvider>
```

### Modal Control

```tsx
import { useTokaiModal } from '@tokai/react';

function MyApp() {
  const { openModal, closeModal, isModalOpen } = useTokaiModal();
  
  return (
    <div>
      {/* Custom button */}
      <button onClick={openModal}>Connect Wallet</button>
      
      {/* TokaiConnectButton uses same modal */}
      <TokaiConnectButton />
      
      {/* Modal is automatically available - no setup needed */}
    </div>
  );
}
```

### Benefits

- ✅ **Zero Setup**: No manual modal configuration
- ✅ **Consistent UI**: Same modal across all components
- ✅ **Feature Flags**: Automatic app configuration detection
- ✅ **Type Safety**: Full TypeScript support
- ✅ **Performance**: Single modal instance for entire app

## 🎨 Styling

The SDK includes comprehensive CSS styles that you can import:

```tsx
import '@tokai/react/dist/styles.css';
```

### Custom Styling

All components accept a `className` prop for custom styling:

```tsx
<TokaiConnectButton className="my-custom-button">
  Connect Wallet
</TokaiConnectButton>
```

### Dark Mode

The SDK automatically supports dark mode based on user preferences:

```css
/* Your custom dark mode overrides */
@media (prefers-color-scheme: dark) {
  .tokai-button {
    background-color: #1f2937;
    color: #e5e7eb;
  }
}
```

## 🔗 Multi-Chain Support

The SDK supports 10 different blockchain networks:

| Chain | Wallet Types | Features |
|-------|-------------|----------|
| **Ethereum** | MetaMask, Coinbase, WalletConnect | Full transaction support |
| **Solana** | Phantom, Backpack, Solflare | SPL token support |
| **Polkadot** | Polkadot.js, SubWallet | Substrate integration |
| **Algorand** | Pera, Defly, Exodus | Asset management |
| **Fivire** | 5ire Wallet, MetaMask | Sustainability features |
| **Astar** | SubWallet, Polkadot.js | dApps integration |
| **Avalanche** | Core Wallet, MetaMask | C-Chain support |
| **Cardano** | Nami, Eternl, Flint | UTXO management |
| **Cosmos** | Keplr, Leap, Cosmostation | IBC support |
| **NEAR** | Sender, Meteor, Here | Account-based auth |

## 📊 Analytics Events

The SDK automatically tracks important events:

- `wallet_connected` - When a wallet is connected
- `transaction_sent` - When a transaction is sent
- `session_key_created` - When a session key is created
- `notification_received` - When a notification is received
- `user_login` - When a user logs in
- `user_register` - When a user registers

## 🔐 Security Features

- **Session Keys**: Time-limited delegated permissions
- **Social Recovery**: Recover wallets using social accounts
- **Multi-Factor Authentication**: TOTP and SMS support
- **Rate Limiting**: Built-in protection against abuse
- **Secure Storage**: Encrypted wallet storage
- **Audit Logging**: Complete activity tracking

## 🔄 Migration Guide

### From Manual Modal Setup

#### **Before (Manual Modal):**
```tsx
// ❌ Old way - manual modal management
function App() {
  const [showModal, setShowModal] = useState(false);
  
  return (
    <div>
      <button onClick={() => setShowModal(true)}>Connect</button>
      {showModal && (
        <TokaiModal 
          appId="your-app-id"
          onSuccess={handleSuccess}
          onError={handleError}
        />
      )}
    </div>
  );
}
```

#### **After (Provider Modal):**
```tsx
// ✅ New way - automatic modal
function App() {
  const { openModal } = useTokaiModal();
  
  return (
    <div>
      <button onClick={openModal}>Connect</button>
      {/* Modal is automatically available */}
    </div>
  );
}
```

### From Wrong appId Usage

#### **Before (Wrong):**
```tsx
// ❌ appId in component
<TokaiConnectButton appId="your-app-id" />
```

#### **After (Correct):**
```tsx
// ✅ appId in provider
<TokaiProvider appId="your-app-id">
  <TokaiConnectButton />
</TokaiProvider>
```

## 🚀 Advanced Usage

### Multi-Chain Wallet Management

### Troubleshooting

#### **Modal Not Opening**
```tsx
// ❌ Wrong: Missing provider
function App() {
  const { openModal } = useTokaiModal(); // Error: No provider
  return <button onClick={openModal}>Connect</button>;
}

// ✅ Correct: Wrap with provider
function App() {
  return (
    <TokaiProvider appId="your-app-id">
      <ConnectButton />
    </TokaiProvider>
  );
}
```

#### **Feature Flags Not Working**
```tsx
// ❌ Wrong: appId in component
<TokaiConnectButton appId="your-app-id" />

// ✅ Correct: appId in provider
<TokaiProvider appId="your-app-id">
  <TokaiConnectButton />
</TokaiProvider>
```

#### **Type Errors**
```tsx
// ❌ Wrong: Missing Chain import
import { SocialProvider, ThemeCustomization } from '../types';

// ✅ Correct: Include Chain import
import { SocialProvider, ThemeCustomization, Chain } from '../types';
```

### Multi-Chain Wallet Management

```tsx
import { useMultiChainWallet } from '@tokai/react';

function MultiChainComponent() {
  const { 
    activeChains, 
    connectChain, 
    disconnectChain, 
    getChainWallet 
  } = useMultiChainWallet(['ethereum', 'solana', 'polkadot']);

  const handleConnectAll = async () => {
    await connectChain('ethereum', 'metamask');
    await connectChain('solana', 'phantom');
    await connectChain('polkadot', 'polkadotjs');
  };

  return (
    <div>
      <button onClick={handleConnectAll}>Connect All Chains</button>
      
      {Object.entries(activeChains).map(([chain, wallet]) => (
        <div key={chain}>
          <h4>{chain}</h4>
          {wallet ? (
            <p>Connected: {wallet.getState().account}</p>
          ) : (
            <p>Not connected</p>
          )}
        </div>
      ))}
    </div>
  );
}
```

### Transaction Management

```tsx
import { useTransaction } from '@tokai/react';

function TransactionComponent({ walletId }) {
  const { 
    send, 
    sign, 
    getBalance, 
    transactionHash, 
    isLoading 
  } = useTransaction(walletId);

  const handleSendTransaction = async () => {
    try {
      const result = await send({
        to: '******************************************',
        value: '0x1000000000000000000', // 1 ETH
        data: '0x'
      });
      console.log('Transaction sent:', result.transactionHash);
    } catch (err) {
      console.error('Transaction failed:', err);
    }
  };

  return (
    <div>
      <button onClick={handleSendTransaction} disabled={isLoading}>
        {isLoading ? 'Sending...' : 'Send Transaction'}
      </button>
      
      {transactionHash && (
        <p>Transaction Hash: {transactionHash}</p>
      )}
    </div>
  );
}
```

## 🎯 Key Benefits

### **Provider-Based Architecture**
- ✅ **Zero Setup**: Modal automatically available
- ✅ **Consistent UI**: Same modal across all components
- ✅ **Feature Flags**: Automatic app configuration detection
- ✅ **Type Safety**: Full TypeScript support

### **Developer Experience**
- ✅ **Simple Integration**: Just wrap with provider
- ✅ **Flexible Control**: Programmatic modal access
- ✅ **Automatic Configuration**: Uses app settings
- ✅ **Performance Optimized**: Single modal instance

### **Production Ready**
- ✅ **Multi-Chain Support**: 10+ blockchain networks
- ✅ **Security Features**: Session keys, social recovery
- ✅ **Analytics**: Built-in event tracking
- ✅ **Notifications**: Real-time updates

## 🚀 Getting Started

1. **Install the SDK**
   ```bash
   npm install @tokai/react
   ```

2. **Setup Provider**
   ```tsx
   <TokaiProvider appId="your-app-id">
     <YourApp />
   </TokaiProvider>
   ```

3. **Use Components**
   ```tsx
   <TokaiConnectButton />
   ```

4. **Control Programmatically**
   ```tsx
   const { openModal } = useTokaiModal();
   ```

That's it! The modal is automatically available and configured based on your app settings. 🎉

## 📚 API Reference

### Hooks

- `useTokai()` - Main context hook
- `useWallet(chain, walletType)` - Wallet management
- `useSessionKeys()` - Session key management
- `useAnalytics()` - Analytics tracking
- `useNotifications()` - Notification management
- `useMultiChainWallet(chains)` - Multi-chain support
- `useTransaction(walletId)` - Transaction management
- `useWalletList()` - Wallet list management
- `useWalletSettings()` - Settings management

### Components

- `TokaiProvider` - Main provider component
- `TokaiLogin` - Login form component
- `TokaiRegister` - Registration form component
- `TokaiWalletList` - Wallet list component
- `TokaiCreateWallet` - Wallet creation component
- `TokaiSessionKeys` - Session keys management
- `TokaiNotifications` - Notifications component
- `TokaiConnectButton` - Wallet connection button
- `TokaiWalletDashboard` - Complete dashboard

### Types

All TypeScript types are exported for custom implementations:

```tsx
import { 
  Chain, 
  WalletManager, 
  SessionKey, 
  Notification,
  TokaiConfig 
} from '@tokai/react';
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📖 [Documentation](https://docs.tokai.com)
- 💬 [Discord Community](https://discord.gg/tokai)
- 🐛 [Issue Tracker](https://github.com/tokai/tokai/issues)
- 📧 [Email Support](mailto:<EMAIL>)

---

**Last Updated**: August 8, 2025 at 05:13 AM IST (GMT+5:30)

Made with ❤️ by the Tokai Team 