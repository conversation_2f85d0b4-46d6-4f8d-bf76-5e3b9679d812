{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "CommonJS", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "declaration": true, "declarationDir": "./dist", "outDir": "./dist", "rootDir": "./src"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}