{"name": "@tokai/react", "version": "1.0.0", "description": "Tokai Wallet React Components", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "type-check": "tsc --noEmit", "lint": "eslint src/**/*.ts src/**/*.tsx", "format": "prettier --write src/**/*.ts src/**/*.tsx"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "dependencies": {"@tokai/hooks": "workspace:*", "@tokai/wallet-connectors": "workspace:*", "@tokai/wallet-managers": "workspace:*", "@tokai/wallet-types": "workspace:*", "axios": "^1.6.0"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "prettier": "^3.0.0", "typescript": "^5.0.0"}, "keywords": ["tokai", "wallet", "react", "crypto", "blockchain"], "author": "Tokai Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tokai/wallet-react"}}