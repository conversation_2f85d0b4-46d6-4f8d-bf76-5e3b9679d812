{"name": "@tokai/wallet-types", "version": "0.0.1", "description": "Tokai TypeScript types for wallet operations and blockchain interactions", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc --outDir dist", "lint": "eslint . --ext .ts,.tsx", "clean": "rm -rf dist", "dev": "echo 'Wallet types package temporarily disabled'"}, "devDependencies": {"typescript": "^5.5.4", "eslint": "^9.15.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0"}}