// global.d.ts
export {};

// Add EIP-6963 Multi-Wallet Discovery interfaces
interface EIP6963ProviderInfo {
  uuid: string;
  name: string;
  icon: string;
  rdns: string;
}

interface EIP6963ProviderDetail {
  info: EIP6963ProviderInfo;
  provider: import("./providers").EthereumProvider;
}

interface EIP6963EventsWindow extends Window {
  addEventListener(type: 'eip6963:announceProvider', listener: (event: CustomEvent<EIP6963ProviderDetail>) => void): void;
  addEventListener(type: 'eip6963:requestProvider', listener: (event: Event) => void): void;
  dispatchEvent(event: CustomEvent<any> | Event): boolean;
}

// Add Wallet Standard interfaces
interface WalletStandardWallet {
  name: string;
  icon: string;
  version: string;
  accounts: Array<{ address: string; publicKey: Uint8Array }>;
  features: string[];
}

interface WalletEventsWindow extends Window {
  addEventListener(type: 'wallet-standard:register-wallet', listener: (event: CustomEvent<{ wallet: WalletStandardWallet }>) => void): void;
  addEventListener(type: 'wallet-standard:app-ready', listener: (event: CustomEvent) => void): void;
  dispatchEvent(event: CustomEvent<any>): boolean;
}

declare module "*.svg" {
  const content: string;
  export default content;
}

// Import your provider types
declare global {
  interface Window extends EIP6963EventsWindow, WalletEventsWindow {
    // EVM & Compatible
    ethereum?: import("./providers").EthereumProvider;
    solana?: import("./providers").SolanaProvider;

    // Solana specific
    phantom?: {
      solana: import("./providers").SolanaProvider;
      ethereum: import("./providers").EthereumProvider;
    };
    backpack?: {
      solana: import("./providers").SolanaProvider;
      ethereum: import("./providers").EthereumProvider;
    };
    solflare?: import("./providers").SolanaProvider;

    // Astar specific
    SubWallet?: import("./providers").AstarProvider;
    injectedWeb3?: {
      ["polkadot-js"]?: import("./providers").PolkadotProvider;
      ["subwallet-js"]?: import("./providers").PolkadotProvider;
      nova?: import("./providers").PolkadotProvider;
    };

    // Algorand specific
    PeraWallet?: import("./providers").AlgorandProvider;
    DeflyWallet?: import("./providers").AlgorandProvider;
    ExodusWallet?: import("./providers").AlgorandProvider;
    MyAlgoConnect?: import("./providers").AlgorandProvider;

    // Cardano specific
    cardano?: {
      nami?: import("./providers").CardanoProvider;
      eternl?: import("./providers").CardanoProvider;
      flint?: import("./providers").CardanoProvider;
      typhon?: import("./providers").CardanoProvider;
      yoroi?: import("./providers").CardanoProvider;
    };

    // Bitcoin specific
    unisat?: import("./providers").BitcoinProvider;
    xverse?: {
      bitcoin?: import("./providers").BitcoinProvider;
    };
    hiro?: import("./providers").BitcoinProvider;

    // Near specific
    near?: {
      isSender?: boolean;
      isMeteor?: boolean;
      isHereWallet?: boolean;
    };
    nearWallets?: {
      sender?: import("./providers").NearProvider;
      meteor?: import("./providers").NearProvider;
      here?: import("./providers").NearProvider;
    };

    avalanche?: import("./providers").AvalancheProvider;

    // Cosmos specific
    keplr?: import("./providers").CosmosProvider;
    leap?: import("./providers").CosmosProvider;
    cosmostation?: import("./providers").CosmosProvider;

    // Polkadot specific
    polkadot?: import("./providers").PolkadotProvider;
    talisman?: import("./providers").PolkadotProvider;
    subwallet?: import("./providers").PolkadotProvider;
    nova?: import("./providers").PolkadotProvider;

    // Tron specific
    tronWeb?: import("./providers").TronProvider;
    tronLink?: import("./providers").TronProvider;
    okxwallet?: {
      tron: import("./providers").TronProvider;
    };
    tokenPocket?: {
      tron: import("./providers").TronProvider;
    };
    mathwallet?: {
      tron: import("./providers").TronProvider;
    };

    // 5ire specific
    fire?: import("./providers").FivireProvider;

    // Trust specific
    trust?: import("./providers").TrustProvider;

    // Coinbase specific
    coinbase?: import("./providers").CoinbaseProvider;
  }
}
