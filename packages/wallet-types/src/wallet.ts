export interface WalletInfo {
  name: string;
  icon: string;
  downloadUrl: string;
  websiteUrl: string;
  supportUrl: string;
  chainId: string | number;
  network: string;
  isInstalled: boolean;
}

export interface WalletBalance {
  total: string;
  available: string;
  staked?: string;
  locked?: string;
  tokens?: {
    symbol: string;
    amount: string;
    usdValue?: string;
  }[];
}

export interface WalletNetwork {
  name: string;
  chainId: string | number;
  rpcUrl: string;
  blockExplorer: string;
  currencySymbol: string;
  currencyDecimals: number;
}

export interface WalletMetadata {
  name: string;
  version: string;
  description: string;
  icons: {
    light: string;
    dark: string;
  };
  chains: string[];
  platforms: ("browser" | "ios" | "android")[];
}

export interface WalletHookState {
  account?: string | null;
  error: string | null;
  isConnecting: boolean;
  chainId?: string | number | null;
}

export interface WalletHookActions {
  connect: () => Promise<void>;
  disconnect: () => void;
}

export interface BaseWalletHook extends WalletHookState {
  actions: WalletHookActions;
}

// Base wallet state interface that all wallet managers should extend
export interface BaseWalletState {
  account: string | null;
  error: string | null;
  isConnecting: boolean;
  chainId?: string | number | null;
  provider?: any;
  isConnected: boolean;
}

// Specific wallet state interfaces for each chain
export interface EthereumWalletState extends BaseWalletState {
  networkId?: number;
  gasPrice?: string;
  gasLimit?: number;
}

export interface SolanaWalletState extends BaseWalletState {
  publicKey?: string;
  lamports?: number;
  slot?: number;
}

export interface BitcoinWalletState extends BaseWalletState {
  addresses?: string[];
  utxos?: any[];
  balance?: number;
}

export interface CardanoWalletState extends BaseWalletState {
  addresses?: string[];
  utxos?: any[];
  assets?: any[];
  networkId?: number;
}

export interface CosmosWalletState extends BaseWalletState {
  addresses?: string[];
  offlineSigner?: any;
  key?: any;
}

export interface AlgorandWalletState extends BaseWalletState {
  accounts?: string[];
  suggestedParams?: any;
}

export interface AvalancheWalletState extends BaseWalletState {
  networkId?: number;
  gasPrice?: string;
  gasLimit?: number;
}

export interface AstarWalletState extends BaseWalletState {
  networkId?: number;
  gasPrice?: string;
  gasLimit?: number;
}

export interface FivireWalletState extends BaseWalletState {
  networkId?: number;
  gasPrice?: string;
  gasLimit?: number;
  sustainabilityScore?: number;
  stakedBalance?: string;
}

export interface NearWalletState extends BaseWalletState {
  accountId?: string;
  networkId?: string;
  signInMessage?: string;
}

export interface PolkadotWalletState extends BaseWalletState {
  accounts?: string[];
  genesisHash?: string;
}

export interface TronWalletState extends BaseWalletState {
  addresses?: string[];
  energy?: number;
  bandwidth?: number;
}

// Base types
interface NetworkConfig {
  name: string;
  chainId: string;
  rpcUrl: string;
  blockExplorer: string;
  currencySymbol: string;
  currencyDecimals: number;
  nativeCurrency?: {
    name: string;
    symbol: string;
    decimals: number;
  };
}

interface WalletConfig {
  name: string;
  icon: string;
  platforms: ("browser" | "ios" | "android" | "desktop" | "mobile")[];
  injectedNamespace?: string; // e.g., 'ethereum', 'solana', etc.
  downloadUrls?: {
    browser?: string;
    ios?: string;
    android?: string;
  };
  metadata?: {
    description?: string;
    website?: string;
    documentation?: string;
    repository?: string;
  };
}

// Network types
export type BaseNetwork = "mainnet" | "testnet" | "devnet";
export type EthereumNetwork =
  | "mainnet"
  | "goerli"
  | "sepolia"
  | "rinkeby"
  | "kovan"
  | "arbitrum"
  | "optimism"
  | "base"
  | "avalanche"
  | "polygon"
  | "bsc"
  | "fantom";
export type FivireNetwork = "mainnet" | "testnet";
export type AstarNetwork = "mainnet" | "shibuya" | "shiden";
export type AvalancheNetwork = "mainnet" | "fuji" | "local";
export type SolanaNetwork = "mainnetBeta" | "devnet" | "testnet";
export type CardanoNetwork = "mainnet" | "testnet" | "preview" | "preprod";
export type AlgorandNetwork = "mainnet" | "testnet" | "betanet";
export type CosmosNetwork = "mainnet" | "testnet";
export type NearNetwork = "mainnet" | "testnet" | "betanet";
export type PolkadotNetwork = "mainnet" | "rococo" | "westend";
export type TronNetwork = "mainnet" | "shasta" | "nile";
export type BitcoinNetwork = "mainnet" | "testnet" | "regtest";

export type ChainNetworks = {
  ethereum: BaseNetwork;
  fivire: BaseNetwork;
  astar: AstarNetwork;
  avalanche: BaseNetwork;
  solana: SolanaNetwork;
  cardano: CardanoNetwork;
  algorand: AlgorandNetwork;
  cosmos: CosmosNetwork;
  near: NearNetwork;
  polkadot: PolkadotNetwork;
  tron: TronNetwork;
  bitcoin: BitcoinNetwork;
};

export interface ChainEcosystem<
  T extends string,
  N extends string = BaseNetwork,
> {
  networks: Partial<Record<N, NetworkConfig>>;
  wallets: Partial<Record<T, WalletConfig>>;
}

export type EthereumWallets =
  | "metamask"
  | "walletconnect"
  | "coinbase"
  | "trust";
export type FivireWallets = "5ire" | "metamask" | "walletconnect";
export type AstarWallets = "polkadotjs" | "subwallet" | "nova";
export type AvalancheWallets = "core" | "metamask" | "walletconnect" | "trust";
export type SolanaWallets = "phantom" | "solflare" | "backpack" | "brave";
export type CardanoWallets = "nami" | "eternl" | "flint" | "yoroi" | "typhon";
export type AlgorandWallets = "pera" | "defly" | "exodus";
export type CosmosWallets = "keplr" | "cosmostation" | "leap";
export type NearWallets = "sender" | "meteor" | "here";
export type PolkadotWallets = "polkadotjs" | "subwallet" | "nova";
export type TronWallets = "tronlink" | "tokenpocket" | "mathwallet";
export type BitcoinWallets = "unisat" | "xverse" | "hiro";

export type Chain =
  | "ethereum"
  | "fivire"
  | "astar"
  | "avalanche"
  | "solana"
  | "cardano"
  | "algorand"
  | "cosmos"
  | "near"
  | "polkadot"
  | "tron"
  | "bitcoin";

export type EthereumChain = ChainEcosystem<EthereumWallets, EthereumNetwork>;
export type FivireChain = ChainEcosystem<FivireWallets, FivireNetwork>;
export type AstarChain = ChainEcosystem<AstarWallets, AstarNetwork>;
export type AvalancheChain = ChainEcosystem<AvalancheWallets, AvalancheNetwork>;
export type SolanaChain = ChainEcosystem<SolanaWallets, SolanaNetwork>;
export type CardanoChain = ChainEcosystem<CardanoWallets, CardanoNetwork>;
export type AlgorandChain = ChainEcosystem<AlgorandWallets, AlgorandNetwork>;
export type CosmosChain = ChainEcosystem<CosmosWallets, CosmosNetwork>;
export type NearChain = ChainEcosystem<NearWallets, NearNetwork>;
export type PolkadotChain = ChainEcosystem<PolkadotWallets, PolkadotNetwork>;
export type TronChain = ChainEcosystem<TronWallets, TronNetwork>;
export type BitcoinChain = ChainEcosystem<BitcoinWallets, BitcoinNetwork>;

type WalletEcosystem = {
  readonly ethereum: EthereumChain;
  readonly fivire: FivireChain;
  readonly astar: AstarChain;
  readonly avalanche: AvalancheChain;
  readonly solana: SolanaChain;
  readonly cardano: CardanoChain;
  readonly algorand: AlgorandChain;
  readonly cosmos: CosmosChain;
  readonly near: NearChain;
  readonly polkadot: PolkadotChain;
  readonly tron: TronChain;
  readonly bitcoin: BitcoinChain;
};

export type Network<T extends Chain> = ChainNetworks[T];
export type Wallet<T extends Chain> = keyof WalletEcosystem[T]["wallets"];
