// Base provider interface with common methods
interface BaseProvider {
  id?: string; // Unique identifier for the provider
  on?: (event: string, callback: (args: any) => void) => void;
  removeListener?: (event: string, callback: (args: any) => void) => void;
  removeAllListeners?: () => void;
  disconnect?: () => Promise<void>;
}

// EVM-compatible provider interface
export interface EthereumProvider extends BaseProvider {
  // Legacy wallet detection (still supported for backwards compatibility)
  isMetaMask?: boolean;
  isCoinbaseWallet?: boolean;
  isWalletConnect?: boolean;
  isTrustWallet?: boolean;
  isBraveWallet?: boolean;
  isAvalanche?: boolean;
  isTrust?: boolean;
  isRabby?: boolean;
  isFrame?: boolean;
  
  // Core EIP-1193 methods
  request: (args: { method: string; params?: any[] }) => Promise<any>;
  
  // Legacy properties (deprecated but still used)
  selectedAddress?: string;
  chainId?: string;
  networkVersion?: string;
  enable?: () => Promise<string[]>; // Deprecated, use eth_requestAccounts instead
  
  // Modern EIP-1193 event handling
  on?: (event: string, handler: (...args: any[]) => void) => void;
  removeListener?: (event: string, handler: (...args: any[]) => void) => void;
  
  // Transaction and signing methods
  signTransaction?: (transaction: any) => Promise<string>;
  signMessage?: (message: string) => Promise<string>;
  signTypedData?: (domain: any, types: any, value: any) => Promise<string>;
  
  // Modern connection state
  isConnected?: () => boolean;
  
  // EIP-5792 Wallet Function Call API support
  wallet_getCapabilities?: () => Promise<Record<string, any>>;
  wallet_sendCalls?: (params: any) => Promise<string>;
  wallet_getCallsStatus?: (bundleId: string) => Promise<any>;
  wallet_showCallsStatus?: (bundleId: string) => Promise<void>;
  
  // EIP-6963 provider info (when discovered via EIP-6963)
  _eip6963?: {
    info: EIP6963ProviderInfo;
    provider: EthereumProvider;
  };
}

// EIP-6963 Multi-Wallet Injected Provider Discovery interfaces
export interface EIP6963ProviderInfo {
  uuid: string;
  name: string;
  icon: string;
  rdns: string; // Reverse DNS identifier (e.g., "io.metamask")
}

export interface EIP6963ProviderDetail {
  info: EIP6963ProviderInfo;
  provider: EthereumProvider;
}

export interface EIP6963AnnounceProviderEvent extends CustomEvent {
  type: "eip6963:announceProvider";
  detail: EIP6963ProviderDetail;
}

export interface EIP6963RequestProviderEvent extends Event {
  type: "eip6963:requestProvider";
}

// Solana provider interface
export interface SolanaProvider extends BaseProvider {
  isPhantom?: boolean;
  isBackpack?: boolean;
  isSolflare?: boolean;
  isBraveWallet?: boolean;
  request?: (args: { method: string; params?: any[] }) => Promise<any>;
  publicKey?: { toString: () => string };
  connection?: any;
  connect?: () => Promise<{ publicKey: string }>;
  disconnect?: () => Promise<void>;
  signTransaction?: (transaction: any) => Promise<any>;
  signAllTransactions?: (transactions: any[]) => Promise<any[]>;
  signAndSendTransaction?: (transaction: any) => Promise<any>;
  signAndSendAllTransactions?: (transactions: any[]) => Promise<any[]>;
  signMessage?: (message: Uint8Array, encoding?: string) => Promise<{ signature: Uint8Array }>;
  isConnected: boolean;
  
  // Modern Wallet Standard additions
  signIn?: (input?: { domain?: string; statement?: string; uri?: string; version?: string; chainId?: string; nonce?: string; issuedAt?: string; expirationTime?: string; notBefore?: string; requestId?: string; resources?: string[] }) => Promise<{ account: { address: string; publicKey: Uint8Array }; signedMessage: Uint8Array; signature: Uint8Array }>;
  signOut?: () => Promise<void>;
  
  // Support for versioned transactions
  signTransactionV0?: (transaction: any) => Promise<any>;
  signAllTransactionsV0?: (transactions: any[]) => Promise<any[]>;
  
  // Modern encryption support
  encrypt?: (message: Uint8Array, publicKey: Uint8Array) => Promise<Uint8Array>;
  decrypt?: (encryptedMessage: Uint8Array, publicKey: Uint8Array) => Promise<Uint8Array>;
}

// Algorand provider interface
export interface AlgorandProvider extends BaseProvider {
  isPera?: boolean;
  isDefly?: boolean;
  isExodus?: boolean;
  isMyAlgo?: boolean;
  connect: () => Promise<{ address: string }>;
  disconnect?: () => Promise<void>;
  off: (event: string, callback: (args: any) => void) => void;
  postTxns?: (txns: Uint8Array[]) => Promise<Uint8Array[]>;
  signTxns?: (
    txns: Array<{ txn: string; message?: string }>
  ) => Promise<string[]>;
  getNetwork?: () => Promise<string>;
  accounts?: Array<{ address: string }>;
}

// Near provider interface
export interface NearProvider extends BaseProvider {
  isSender?: boolean;
  isMeteor?: boolean;
  isHereWallet?: boolean;
  signIn: () => Promise<{ accountId: string }>;
  signOut: () => Promise<void>;
  getNetworkId: () => Promise<string>;
  getBalance?: () => Promise<string>;
  isSignedIn: () => boolean;
  signTransaction?: (transaction: any) => Promise<string>;
  accountId?: string;
}

// Bitcoin provider interface
export interface BitcoinProvider extends BaseProvider {
  isUnisatWallet?: boolean;
  isXverseWallet?: boolean;
  isHiroWallet?: boolean;
  connect: () => Promise<string[]>;
  getNetwork: () => Promise<"mainnet" | "testnet">;
  getBalance?: () => Promise<{
    confirmed: number;
    unconfirmed: number;
    total: number;
  }>;
  getUtxos?: (address: string) => Promise<any[]>;
  signTransaction?: (transaction: any) => Promise<string>;
  sendTransaction?: (transaction: any) => Promise<string>;
}

// Cardano provider interface
export interface CardanoProvider extends BaseProvider {
  enable: () => Promise<CardanoApi>;
  isEnabled: () => Promise<boolean>;
  experimental?: {
    on: (event: string, callback: (args: any) => void) => void;
    off: (event: string, callback: (args: any) => void) => void;
  };
  name: string;
  icon: string;
  apiVersion: string;
  getStakingInfo?: () => Promise<{
    delegatedAmount: string;
    rewards: string;
    poolId: string;
  }>;
}

// Cardano API interface
export interface CardanoApi {
  getNetworkId: () => Promise<number>;
  getBalance: () => Promise<string>;
  getUtxos: () => Promise<string[] | undefined>;
  getUsedAddresses: () => Promise<string[]>;
  getUnusedAddresses: () => Promise<string[]>;
  getChangeAddress: () => Promise<string>;
  getRewardAddresses: () => Promise<string[]>;
  signTx: (tx: string, partialSign: boolean) => Promise<string>;
  signData: (addr: string, payload: string) => Promise<string>;
  submitTx: (tx: string) => Promise<string>;
}

// Avalanche provider interface
export interface AvalancheProvider extends BaseProvider {
  isAvalanche?: boolean;
  isMetaMask?: boolean;
  isWalletConnect?: boolean;
  request: (args: { method: string; params?: any[] }) => Promise<any>;
  enable: () => Promise<string[]>;
  getSubnetInfo?: () => Promise<any>;
  selectedAddress?: string;
  chainId?: string;
  networkVersion?: string;
}

// Astar provider interface
export interface AstarProvider extends BaseProvider {
  isMetaMask?: boolean;
  isSubWallet?: boolean;
  isWalletConnect?: boolean;
  enable: (originName: string) => Promise<{ accounts: string[] }>;
  request: (args: { method: string; params?: any[] }) => Promise<any>;
  selectedAddress?: string;
  chainId?: string;
  networkVersion?: string;
}

type CosmosChainInfo = {
  chainId: string;
  chainName: string;
  rpc: string;
  rest: string;
  bip44: {
    coinType: number;
  };
  bech32Config: {
    bech32PrefixAccAddr: string;
    bech32PrefixAccPub: string;
    bech32PrefixValAddr: string;
    bech32PrefixValPub: string;
    bech32PrefixConsAddr: string;
    bech32PrefixConsPub: string;
  };
  currencies: Array<{
    coinDenom: string;
    coinMinimalDenom: string;
    coinDecimals: number;
  }>;
  feeCurrencies: Array<{
    coinDenom: string;
    coinMinimalDenom: string;
    coinDecimals: number;
  }>;
};

// Cosmos provider interface
export interface CosmosProvider extends BaseProvider {
  isKeplr?: boolean;
  isLeap?: boolean;
  isCosmostation?: boolean;
  enable: (chainIds: string[]) => Promise<void>;
  disconnect: () => Promise<void>;
  getKey: (chainId: string) => Promise<{
    address: string;
    algo: string;
    pubKey: Uint8Array;
  }>;
  getBalance?: (chainId: string, address: string) => Promise<{
    amount: string;
    denom: string;
  }>;
  experimentalSuggestChain?: (chainInfo: CosmosChainInfo) => Promise<void>;
  getStakingInfo?: (chainId: string, address: string) => Promise<any>;
}

// Tron provider interface
export interface TronProvider extends BaseProvider {
  ready: boolean;
  isTronLink?: boolean;
  isOKXWallet?: boolean;
  isTokenPocket?: boolean;
  request: (args: { method: string; params?: any[] }) => Promise<any>;
  getAddress?: () => Promise<string>;
  getNetwork?: () => Promise<"mainnet" | "shasta" | "nile">;
  getBalance?: (address: string) => Promise<{
    trx: string;
    tokens: Array<{ name: string; balance: string }>;
  }>;
  signTransaction?: (transaction: any) => Promise<string>;
  sendTransaction?: (transaction: any) => Promise<string>;
}

// 5ire provider interface
export interface FivireProvider extends BaseProvider {
  is5ire?: boolean;
  isMetaMask?: boolean;
  isWalletConnect?: boolean;
  isAvalanche?: boolean;
  isTrust?: boolean;
  isCoinbaseWallet?: boolean;
  request: (args: { method: string; params?: any[] }) => Promise<any>;
  enable: () => Promise<string[]>;
  getStakingInfo?: () => Promise<{
    totalStaked: string;
    rewards: string;
    unlockTime: number;
  }>;
  getSustainabilityScore?: () => Promise<number>;
}

// Polkadot provider interface
export interface PolkadotProvider extends BaseProvider {
  isPolkadotJs?: boolean;
  isSubwallet?: boolean;
  isNova?: boolean;
  enable: (originName: string) => Promise<string[]>;
  getAccounts: () => Promise<string[]>;
  getBalance?: () => Promise<{
    free: string;
    reserved: string;
    miscFrozen: string;
    feeFrozen: string;
  }>;
  getNetwork: () => Promise<string>;
  getRuntimeVersion?: () => Promise<{
    specVersion: number;
    transactionVersion: number;
  }>;
}

// Export all provider types
export type Provider =
  | EthereumProvider
  | SolanaProvider
  | AlgorandProvider
  | NearProvider
  | BitcoinProvider
  | CardanoProvider
  | AvalancheProvider
  | AstarProvider
  | CosmosProvider
  | TronProvider
  | FivireProvider
  | PolkadotProvider;