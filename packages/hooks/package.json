{"name": "@tokai/hooks", "version": "0.0.1", "description": "Tokai React hooks for wallet management and blockchain interactions", "private": true, "main": "src/index.ts", "scripts": {"build": "tsc --outDir dist", "lint": "eslint . --ext .ts,.tsx", "clean": "rm -rf dist", "dev": "echo 'Hooks package temporarily disabled'"}, "peerDependencies": {"react": "^18.0.0"}, "dependencies": {"@coinbase/wallet-sdk": "4.2.4", "@tokai/wallet-managers": "workspace:*", "@tokai/wallet-types": "workspace:*"}, "devDependencies": {"typescript": "^5.5.4", "@types/react": "^18.0.0", "eslint": "^9.15.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0"}}