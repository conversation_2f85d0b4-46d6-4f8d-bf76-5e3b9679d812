import { useEffect, useState } from "react";

export const useWallet = (walletManager: any) => {
  const [manager] = useState(walletManager ? () => new walletManager() : null);
  const [state, setState] = useState(manager?.getState());

  useEffect(() => {
    const unsubscribe = manager?.subscribe(setState);
    return () => {
      unsubscribe?.();
      manager?.destroy();
    };
  }, [walletManager]);

  return {
    ...state,
    connectWallet: manager?.connectWallet?.bind(manager),
    disconnect: manager?.disconnect?.bind(manager),
  };
};
