import { AvalancheWalletManager } from "@tokai/wallet-managers";
import { useEffect, useState } from "react";

export const useAvalancheWallets = () => {
  const [manager] = useState(() => new AvalancheWalletManager());
  const [state, setState] = useState(manager.getState());

  useEffect(() => {
    const unsubscribe = manager.subscribe(setState);
    return () => {
      unsubscribe();
      manager.destroy();
    };
  }, [manager]);

  return {
    ...state,
    connectWallet: manager.connectWallet.bind(manager),
    disconnect: manager.disconnect.bind(manager),
  };
};
