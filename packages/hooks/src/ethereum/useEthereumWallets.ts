import { EthereumWalletManager } from "@tokai/wallet-managers";
import { useEffect, useState } from "react";

export const useEthereumWallets = () => {
  const [manager] = useState(() => new EthereumWalletManager());
  const [state, setState] = useState(manager.getState());

  useEffect(() => {
    const unsubscribe = manager.subscribe(setState);
    return () => {
      unsubscribe();
      manager.destroy();
    };
  }, [manager]);

  return {
    ...state,
    // Core wallet connection
    connectWallet: manager.connectWallet.bind(manager),
    disconnect: manager.disconnect.bind(manager),
    
    // Connection state management
    refreshConnectionState: manager.refreshConnectionState.bind(manager),
    validateCurrentAddress: manager.validateCurrentAddress.bind(manager),
    isMetaMaskConnected: manager.isMetaMaskConnected.bind(manager),
    getCurrentMetaMaskAccount: manager.getCurrentMetaMaskAccount.bind(manager),
    getCurrentWalletProvider: manager.getCurrentWalletProvider.bind(manager),
    
    // Modern EIP-6963 wallet discovery and connection
    getAvailableWallets: manager.getAvailableWallets.bind(manager),
    connectEIP6963Wallet: manager.connectEIP6963Wallet.bind(manager),
    
    // EIP-5792 Wallet Function Call API
    getWalletCapabilities: manager.getWalletCapabilities.bind(manager),
    sendCalls: manager.sendCalls.bind(manager),
    getCallsStatus: manager.getCallsStatus.bind(manager),
    showCallsStatus: manager.showCallsStatus.bind(manager),
    
    // Transaction and signing methods
    signMessage: manager.signMessage.bind(manager),
    signTransaction: manager.signTransaction.bind(manager),
    sendTransaction: manager.sendTransaction.bind(manager),
    switchNetwork: manager.switchNetwork.bind(manager),
    addNetwork: manager.addNetwork.bind(manager),
    addEthereumChain: manager.addEthereumChain.bind(manager),
    getBalance: manager.getBalance.bind(manager),
    getProviderInstance: manager.getProviderInstance.bind(manager),
  };
};
