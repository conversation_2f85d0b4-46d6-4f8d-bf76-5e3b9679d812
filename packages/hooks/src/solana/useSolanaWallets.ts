import { SolanaWalletManager } from "@tokai/wallet-managers";
import { useEffect, useState } from "react";

export const useSolanaWallets = () => {
  const [manager] = useState(() => new SolanaWalletManager());
  const [state, setState] = useState(manager.getState());

  useEffect(() => {
    const unsubscribe = manager.subscribe(setState);
    return () => {
      unsubscribe();
      manager.destroy();
    };
  }, [manager]);

  return {
    ...state,
    // Connection methods
    connectWallet: manager.connectWallet.bind(manager),
    disconnect: manager.disconnect.bind(manager),
    
    // Wallet detection
    getInstalledWallets: manager.getInstalledWallets.bind(manager),
    getAvailableWallets: manager.getAvailableWallets.bind(manager),
    
    // Provider access
    getProviderInstance: manager.getProviderInstance.bind(manager),
    getAccountDetails: manager.getAccountDetails.bind(manager),
  };
};
