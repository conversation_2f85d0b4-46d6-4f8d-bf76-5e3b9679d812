import { GetWalletConfig } from "@tokai/wallet-managers";
import { Chain } from "@tokai/wallet-types";
import { useEffect, useState } from "react";

const useWallet = (walletManager: GetWalletConfig<Chain, any> | null) => {
  const [state, setState] = useState(walletManager?.getState() || null);

  useEffect(() => {
    if (!walletManager) return;

    const unsubscribe = walletManager.subscribe(setState);
    return () => {
      unsubscribe();
      walletManager.destroy();
    };
  }, [walletManager]);

  return {
    ...state,
    connectWallet: walletManager?.connectWallet.bind(walletManager),
    disconnect: walletManager?.disconnect.bind(walletManager),
  };
};

export default useWallet;
