import { BitcoinWalletManager } from "@tokai/wallet-managers";
import { useEffect, useState } from "react";

export const useBitcoinWallets = () => {
  const [manager] = useState(() => new BitcoinWalletManager());
  const [state, setState] = useState(manager.getState());

  useEffect(() => {
    const unsubscribe = manager.subscribe(setState);
    return () => {
      unsubscribe();
      manager.destroy();
    };
  }, [manager]);

  return {
    ...state,
    connectWallet: manager.connectWallet.bind(manager),
    disconnect: manager.disconnect.bind(manager),
  };
};
