import { AstarWalletManager } from "@tokai/wallet-managers";
import { useEffect, useState } from "react";

export const useAstarWallets = () => {
  const [manager] = useState(() => new AstarWalletManager());
  const [state, setState] = useState(manager.getState());

  useEffect(() => {
    const unsubscribe = manager.subscribe(setState);
    return () => {
      unsubscribe();
      manager.destroy();
    };
  }, [manager]);

  return {
    ...state,
    connectWallet: manager.connectWallet.bind(manager),
    disconnect: manager.disconnect.bind(manager),
  };
};
