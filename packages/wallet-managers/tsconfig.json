{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "es2017"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "dist", "typeRoots": ["./src/types.d.ts", "./node_modules/@types"]}, "include": ["src", "src/types.d.ts", "src/types/global.d.ts"], "exclude": ["node_modules", "dist"]}