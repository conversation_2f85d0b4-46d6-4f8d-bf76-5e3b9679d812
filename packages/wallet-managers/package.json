{"name": "@tokai/wallet-managers", "version": "0.0.1", "description": "Tokai wallet management utilities for multiple blockchain networks", "private": true, "main": "src/index.ts", "scripts": {"build": "tsc --outDir dist", "lint": "eslint . --ext .ts,.tsx", "clean": "rm -rf dist", "dev": "echo 'Wallet managers package temporarily disabled'"}, "dependencies": {"@tokai/wallet-types": "workspace:*"}, "peerDependencies": {"react": "^18.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.31.0", "typescript": "^5.5.4"}}