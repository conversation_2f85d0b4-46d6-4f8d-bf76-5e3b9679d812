import type {
  AstarNetwork,
  AstarProvider,
  Astar<PERSON>allets,
  PolkadotProvider,
  WalletHookState,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

interface AstarWalletState extends WalletHookState {
  walletType?: AstarWallets | null;
  chainId?: string;
  network?: AstarNetwork;
  balance?: {
    astr: string;
    native?: string;
    dapps?: Array<{
      contract: string;
      balance: string;
      symbol: string;
    }>;
  } | null;
}

type EventCallback = (state: AstarWalletState) => void;

export class AstarWalletManager {
  private state: AstarWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;

  constructor() {
    this.state = {
      account: null,
      error: null,
      isConnecting: false,
      walletType: null,
      chainId: undefined,
      network: undefined,
      balance: null,
    };
    this.listeners = new Set();
  }

  private setState(newState: Partial<AstarWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyList<PERSON>s();
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  private getProvider(type: AstarWallets | null): AstarProvider | null {
    if (typeof window === "undefined") return null;

    const providers: Record<
      AstarWallets,
      AstarProvider | PolkadotProvider | null
    > = {
      polkadotjs: window.injectedWeb3?.["polkadot-js"] || null,
      subwallet: window.SubWallet || null,
      nova: window.nova || null,
    };

    const provider = providers[type!];

    if (provider) {
      return {
        ...provider,
        enable: async (originName: string) => {
          const accounts = await provider.enable(originName);
          return { accounts: accounts as string[] };
        },
        request: async ({ method, params }) => {
          if (method === "eth_requestAccounts") {
            const result = await provider.enable("Astar dApp");
            return result;
          }
          throw new Error(`Method ${method} is not supported`);
        },
      };
    }
    return null;
  }

  private getNetworkFromChainId(chainId: string): AstarNetwork | undefined {
    const networkMap: Record<string, AstarNetwork> = {
      "0x250": "mainnet",
      "0x150": "shiden",
      "0x51": "shibuya",
    };
    return networkMap[chainId];
  }

  private async updateBalance(address?: string) {
    if (!address) return;

    try {
      const provider = this.getProvider(this.state.walletType || null);
      if (!provider) return;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return;

      // Get ASTR balance
      const balance = await provider.request({
        method: "eth_getBalance",
        params: [targetAddress, "latest"],
      });

      // Get dApps tokens if available
      let dapps = [];
      if (provider.getDappsTokens) {
        try {
          dapps = await provider.getDappsTokens(targetAddress);
                      } catch (err) {
          console.warn("Failed to fetch dApps tokens:", err);
        }
      }

      this.setState({
        balance: {
          astr: balance,
          native: balance,
          dapps,
        },
      });
          } catch (err) {
        console.error("Failed to update balance:", err);
      }
  }

  private setupWalletListeners(
    provider: AstarProvider,
    type: NonNullable<AstarWallets>
  ) {
    if (!provider) return;

    const handleAccountsChanged = async (accounts: string[]) => {
      if (accounts.length > 0) {
        this.setState({ account: accounts[0] });

        // Update balance
        await this.updateBalance(accounts[0]);
      } else {
        this.setState({
          account: null,
          walletType: null,
          balance: null,
        });
      }
    };

    const handleChainChanged = async (chainId: string) => {
      const network = this.getNetworkFromChainId(chainId);
      this.setState({ chainId, network });

      if (this.state.account) {
        await this.updateBalance(this.state.account);
      }
    };

    const handleDisconnect = () => {
      this.setState({
        account: null,
        walletType: null,
        chainId: undefined,
        network: undefined,
        balance: null,
      });
    };

    provider.on?.("accountsChanged", handleAccountsChanged);
    provider.on?.("chainChanged", handleChainChanged);
    provider.on?.("disconnect", handleDisconnect);

    return () => {
      provider.removeListener?.("accountsChanged", handleAccountsChanged);
      provider.removeListener?.("chainChanged", handleChainChanged);
      provider.removeListener?.("disconnect", handleDisconnect);
    };
  }

  public async connectWallet(walletType: AstarWallets) {
    if (!walletType) return;

    const wallet = WALLET_ECOSYSTEM?.astar?.wallets[walletType];
    if (!wallet) {
      this.setState({ error: `${walletType} configuration not found` });
      return;
    }

    this.setState({ isConnecting: true, error: null });

    try {
      const provider = this.getProvider(walletType);
      if (!provider) {
        throw new Error(`${wallet.name} is not installed`);
      }

      const [accounts, chainId] = await Promise.all([
        provider.request({ method: "eth_requestAccounts" }),
        provider.request({ method: "eth_chainId" }),
      ]);

      if (!accounts || accounts.length === 0) {
        throw new Error("No accounts found");
      }

      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      // Update initial balance
      await this.updateBalance(accounts[0]);

      this.setState({
        account: accounts[0],
        error: null,
        isConnecting: false,
        walletType,
        chainId,
        network: this.getNetworkFromChainId(chainId),
        balance: {
          astr: "0", // Will be updated via event handler
          native: "0",
        },
      });
    } catch (err) {
      this.setState({
        error:
          err instanceof Error
            ? err.message
            : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
    }
  }

  public async disconnect() {
    if (!this.state.walletType) return;

    try {
      const provider = this.getProvider(this.state.walletType);
      provider?.disconnect?.();
      this.setState({
        account: null,
        walletType: null,
        chainId: undefined,
        network: undefined,
        balance: null,
      });
    } catch (err) {
      this.setState({ error: "Failed to disconnect" });
    }
  }

  public async signMessage(message: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      if (provider.signMessage) {
        return await provider.signMessage(message);
      }

      // Fallback to personal_sign
      const signature = await provider.request({
        method: "personal_sign",
        params: [message, this.state.account],
      });

      return signature;
    } catch (err) {
      this.setState({ error: "Failed to sign message" });
      return null;
    }
  }

  public async signTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const signature = await provider.request({
        method: "eth_signTransaction",
        params: [{ from: this.state.account, ...transaction }],
      });

      return signature;
    } catch (err) {
      this.setState({ error: "Failed to sign transaction" });
      return null;
    }
  }

  public async sendTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const txHash = await provider.request({
        method: "eth_sendTransaction",
        params: [{ from: this.state.account, ...transaction }],
      });

      return txHash;
    } catch (err) {
      this.setState({ error: "Failed to send transaction" });
      return null;
    }
  }

  public async getBalance(address?: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return null;

      const balance = await provider.request({
        method: "eth_getBalance",
        params: [targetAddress, "latest"],
      });

      return balance;
    } catch (err) {
      this.setState({ error: "Failed to get balance" });
      return null;
    }
  }

  public async getDappsTokens(address?: string): Promise<any[] | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !provider.getDappsTokens) return null;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return null;

      return await provider.getDappsTokens(targetAddress);
    } catch (err) {
      this.setState({ error: "Failed to get dApps tokens" });
      return null;
    }
  }

  public async switchNetwork(chainId: string): Promise<boolean> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return false;

      await provider.request({
        method: "wallet_switchEthereumChain",
        params: [{ chainId }],
      });

      return true;
    } catch (err: any) {
      if (err.code === 4902) {
        return this.addNetwork(chainId);
      }
      this.setState({ error: "Failed to switch network" });
      return false;
    }
  }

  public async addNetwork(chainId: string): Promise<boolean> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return false;

      const networkConfig = this.getNetworkConfig(chainId);
      if (!networkConfig) return false;

      await provider.request({
        method: "wallet_addEthereumChain",
        params: [networkConfig],
      });

      return true;
    } catch (err) {
      this.setState({ error: "Failed to add network" });
      return false;
    }
  }

  private getNetworkConfig(chainId: string) {
    const networkConfigs: Record<string, any> = {
      "0x250": {
        chainId: "0x250",
        chainName: "Astar Network",
        nativeCurrency: {
          name: "ASTR",
          symbol: "ASTR",
          decimals: 18,
        },
        rpcUrls: ["https://astar.api.onfinality.io/public"],
        blockExplorerUrls: ["https://astar.subscan.io"],
      },
      "0x150": {
        chainId: "0x150",
        chainName: "Shiden Network",
        nativeCurrency: {
          name: "SDN",
          symbol: "SDN",
          decimals: 18,
        },
        rpcUrls: ["https://shiden.api.onfinality.io/public"],
        blockExplorerUrls: ["https://shiden.subscan.io"],
      },
      "0x51": {
        chainId: "0x51",
        chainName: "Shibuya Network",
        nativeCurrency: {
          name: "SBY",
          symbol: "SBY",
          decimals: 18,
        },
        rpcUrls: ["https://shibuya.api.onfinality.io/public"],
        blockExplorerUrls: ["https://shibuya.subscan.io"],
      },
    };
    return networkConfigs[chainId];
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): AstarWalletState {
    return this.state;
  }

  public destroy() {
    this.cleanupListeners?.();
    this.listeners.clear();
  }
}
