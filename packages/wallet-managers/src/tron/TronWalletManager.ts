import type {
  TronNetwork,
  TronProvider,
  TronWallets,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

interface TronWalletState {
  account: string | null;
  error: string | null;
  isConnecting: boolean;
  walletType: TronWallets | null;
  chainId?: string;
  network?: TronNetwork;
  balance?: {
    trx: string;
    tokens?: Array<{
      contract: string;
      balance: string;
      symbol: string;
    }>;
  } | null;
}

type EventCallback = (state: TronWalletState) => void;

export class TronWalletManager {
  private state: TronWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;

  constructor() {
    this.state = {
      account: null,
      error: null,
      isConnecting: false,
      walletType: null,
      chainId: undefined,
      network: undefined,
      balance: null,
    };
    this.listeners = new Set();
  }

  private setState(newState: Partial<TronWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  private getProvider(type: TronWallets | null): TronProvider | null {
    if (typeof window === "undefined") return null;

    switch (type) {
      case "tronlink":
        return window.tronLink || window.tronWeb || null;
      case "tokenpocket":
        return window.tokenPocket?.tron || null;
      case "mathwallet":
        return window.mathwallet?.tron || null;
      default:
        return null;
    }
  }

  private setupWalletListeners(
    provider: TronProvider,
    type: NonNullable<TronWallets>
  ) {
    const handleAccountsChanged = async (account: string) => {
      if (!account) {
        this.setState({
          account: null,
          walletType: null,
          network: undefined,
          chainId: undefined,
          balance: null,
        });
        return;
      }

      try {
        const [network, balanceData] = await Promise.all([
          provider.getNetwork?.() || null,
          provider.getBalance?.(account) || null,
        ]);

        this.setState({
          account,
          network: network as TronNetwork,
          chainId: network ? WALLET_ECOSYSTEM?.tron?.networks?.[network]?.chainId : undefined,
          balance: balanceData ? {
            trx: balanceData.trx,
            tokens: balanceData.tokens.map(token => ({
              contract: token.name,
              balance: token.balance,
              symbol: token.name,
            })),
          } : null,
        });
      } catch (err) {
        this.setState({
          error: err instanceof Error ? err.message : "Failed to update account",
        });
      }
    };

    const handleNetworkChanged = async (network: TronNetwork) => {
      try {
        const balanceData = this.state.account ? await provider.getBalance?.(this.state.account) || null : null;

        this.setState({
          network,
          chainId: WALLET_ECOSYSTEM?.tron?.networks?.[network]?.chainId,
          balance: balanceData ? {
            trx: balanceData.trx,
            tokens: balanceData.tokens.map(token => ({
              contract: token.name,
              balance: token.balance,
              symbol: token.name,
            })),
          } : null,
        });
      } catch (err) {
        this.setState({
          error: err instanceof Error ? err.message : "Failed to update network",
        });
      }
    };

    const handleDisconnect = () => {
      this.setState({
        account: null,
        walletType: null,
        network: undefined,
        chainId: undefined,
        balance: null,
      });
    };

    if (provider.on) {
      provider.on("accountsChanged", handleAccountsChanged);
      provider.on("networkChanged", handleNetworkChanged);
      provider.on("disconnect", handleDisconnect);
    }

    return () => {
      if (provider.removeListener) {
        provider.removeListener("accountsChanged", handleAccountsChanged);
        provider.removeListener("networkChanged", handleNetworkChanged);
        provider.removeListener("disconnect", handleDisconnect);
      }
    };
  }

  public async connectWallet(walletType: TronWallets) {
    if (!walletType) return;
    const wallet = WALLET_ECOSYSTEM?.tron?.wallets[walletType];
    if (!wallet) {
      this.setState({
        error: `${walletType} configuration not found`,
      });
      return;
    }

    this.setState({ isConnecting: true });

    try {
      const provider = this.getProvider(walletType);
      if (!provider) {
        throw new Error(`${wallet.name} is not installed`);
      }

      // Wait for provider to be ready
      if (!provider.ready) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      // Request account access
      const [account] = await provider.request({
        method: "tron_requestAccounts",
      });

      // Get network and balance info
      const [network, balanceData] = await Promise.all([
        provider.getNetwork?.() || null,
        provider.getBalance?.(account) || null,
      ]);

      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      this.setState({
        account,
        error: null,
        isConnecting: false,
        walletType,
        network: network as TronNetwork,
        chainId: network ? WALLET_ECOSYSTEM?.tron?.networks?.[network]?.chainId : undefined,
        balance: balanceData ? {
          trx: balanceData.trx,
          tokens: balanceData.tokens.map(token => ({
            contract: token.name,
            balance: token.balance,
            symbol: token.name,
          })),
        } : null,
      });
    } catch (err) {
      this.setState({
        error: err instanceof Error ? err.message : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
    }
  }

  public async disconnect() {
    if (!this.state.walletType) return;

    try {
      const provider = this.getProvider(this.state.walletType);
      if (provider?.disconnect) {
        await provider.disconnect();
      }
      this.setState({
        account: null,
        walletType: null,
        network: undefined,
        chainId: undefined,
        balance: null,
      });
    } catch (err) {
      this.setState({
        error: "Failed to disconnect",
      });
    }
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): TronWalletState {
    return this.state;
  }

  public destroy() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
    }
    this.listeners.clear();
  }
} 