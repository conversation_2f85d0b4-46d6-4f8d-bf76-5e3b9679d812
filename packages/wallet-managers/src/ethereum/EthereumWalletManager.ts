import type {
  EthereumNetwork,
  EthereumProvider,
  EthereumWallets,
  EIP6963ProviderDetail,
  EIP6963ProviderInfo,
  EthereumWalletState as BaseEthereumWalletState,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

export interface EthereumWalletState extends BaseEthereumWalletState {
  publicKey: string | null;
  walletType: EthereumWallets | null;
  network?: EthereumNetwork | null;
  balance?: {
    eth: string;
    tokens?: Array<{
      address: string;
      amount: string;
      decimals: number;
      symbol: string;
    }>;
  } | null;
  eip6963Info?: EIP6963ProviderInfo;
  availableWallets?: EIP6963ProviderDetail[];
}

type EventCallback = (state: EthereumWalletState) => void;

export class EthereumWalletManager {
  private state: EthereumWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;
  private eip6963Providers: Map<string, EIP6963ProviderDetail> = new Map();
  private eip6963CleanupListeners?: () => void;
  private discoveryTimeout?: ReturnType<typeof setTimeout>;

  constructor() {
    this.state = {
      account: null,
      chainId: null,
      error: null,
      isConnecting: false,
      isConnected: false,
      provider: null,
      publicKey: null,
      walletType: null,
      network: null,
      balance: null,
      availableWallets: [],
    };
    this.listeners = new Set();
    this.setupEIP6963Listeners();
    this.checkExistingConnection();
  }

  private async checkExistingConnection() {
    if (typeof window === 'undefined' || !window.ethereum) {
      this.clearConnectionState();
      return;
    }
    
    // Only check MetaMask connections
    if (!window.ethereum.isMetaMask) {
      this.clearConnectionState();
      return;
    }
    
    try {
      const accounts = await window.ethereum.request({ method: 'eth_accounts' });
      
      if (accounts && accounts.length > 0) {
        const permissions = await window.ethereum.request({ method: 'wallet_getPermissions' });
        const hasAccountPermission = permissions.some((permission: any) => permission.parentCapability === 'eth_accounts');
        
        if (hasAccountPermission) {
          const chainId = await window.ethereum.request({ method: 'eth_chainId' });
          const currentAccounts = await window.ethereum.request({ method: 'eth_accounts' });
          
          if (currentAccounts && currentAccounts.length > 0) {
            this.setState({
              account: currentAccounts[0],
              publicKey: currentAccounts[0],
              isConnected: true,
              chainId,
              network: this.getNetworkFromChainId(chainId),
              walletType: 'metamask',
              provider: window.ethereum,
            });
            
            this.cleanupListeners = this.setupWalletListeners(window.ethereum, 'metamask');
          } else {
            this.clearConnectionState();
          }
        } else {
          this.clearConnectionState();
        }
      } else {
        this.clearConnectionState();
      }
    } catch (err) {
      this.clearConnectionState();
    }
  }

  private clearConnectionState() {
    this.setState({
      account: null,
      publicKey: null,
      isConnected: false,
      chainId: null,
      network: null,
      walletType: null,
      error: null,
      isConnecting: false,
      provider: null,
    });
  }

  private setState(newState: Partial<EthereumWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach(callback => callback(this.state));
  }

  private getProvider(type: EthereumWalletState["walletType"]): EthereumProvider | null {
    if (typeof window === "undefined") return null;

    switch (type) {
      case "metamask":
        return window.ethereum?.isMetaMask ? window.ethereum : null;
      case "coinbase":
        return window.ethereum?.isCoinbaseWallet ? window.ethereum : null;
      case "walletconnect":
        return window.ethereum?.isWalletConnect ? window.ethereum : null;
      default:
        return null;
    }
  }

  private setupWalletListeners(provider: EthereumProvider, type: NonNullable<EthereumWallets>) {
    if (!provider) return;

    const handleAccountsChanged = (accounts: string[]) => {
      if (accounts.length > 0) {
        this.setState({
          account: accounts[0],
          publicKey: accounts[0],
          isConnected: true,
          provider,
        });
      } else {
        this.clearConnectionState();
      }
    };

    const handleChainChanged = (chainId: string) => {
      this.setState({
        chainId,
        network: this.getNetworkFromChainId(chainId),
      });
    };

    const handleDisconnect = () => {
      this.clearConnectionState();
    };

    const handleConnect = () => {
      // Connection event handled by accountsChanged
    };

    provider.on?.("accountsChanged", handleAccountsChanged);
    provider.on?.("chainChanged", handleChainChanged);
    provider.on?.("disconnect", handleDisconnect);
    provider.on?.("connect", handleConnect);

    return () => {
      provider?.removeListener?.("accountsChanged", handleAccountsChanged);
      provider?.removeListener?.("chainChanged", handleChainChanged);
      provider?.removeListener?.("disconnect", handleDisconnect);
      provider?.removeListener?.("connect", handleConnect);
    };
  }

  private getNetworkFromChainId(chainId: string): EthereumWalletState["network"] {
    const networkMap: Record<string, EthereumNetwork> = {
      "0x1": "mainnet",
      "0x5": "goerli",
      "0xaa36a7": "sepolia",
      "0x89": "polygon",
      "0x38": "bsc",
      "0xa86a": "avalanche",
      "0xfa": "fantom",
      "0xa4b1": "arbitrum",
      "0xa": "optimism",
    };
    return networkMap[chainId] || undefined;
  }

  private setupEIP6963Listeners() {
    if (typeof window === "undefined") return;

    const handleWalletAnnounce = (event: Event) => {
      try {
        const customEvent = event as CustomEvent<EIP6963ProviderDetail>;
        const wallet = customEvent.detail;
        
        if (wallet && wallet.info && wallet.info.rdns && wallet.provider) {
          this.eip6963Providers.set(wallet.info.rdns, wallet);
          this.setState({
            availableWallets: Array.from(this.eip6963Providers.values())
          });
        }
      } catch (err) {
        // Silently handle errors
      }
    };

    window.addEventListener('eip6963:announceProvider', handleWalletAnnounce as EventListener);
    
    // Request wallet announcements
    window.dispatchEvent(new Event('eip6963:requestProvider'));
    
    this.discoveryTimeout = setTimeout(() => {
      window.dispatchEvent(new Event('eip6963:requestProvider'));
      
      setTimeout(() => {
        window.dispatchEvent(new Event('eip6963:requestProvider'));
      }, 500);
    }, 100);

    this.eip6963CleanupListeners = () => {
      window.removeEventListener('eip6963:announceProvider', handleWalletAnnounce as EventListener);
      if (this.discoveryTimeout) {
        clearTimeout(this.discoveryTimeout);
      }
    };
  }

  private mapRdnsToWalletType(rdns: string): EthereumWallets | null {
    const mapping: Record<string, EthereumWallets> = {
      'io.metamask': 'metamask',
      'com.coinbase.wallet': 'coinbase',
      'com.walletconnect': 'walletconnect',
    };
    return mapping[rdns] || null;
  }

  private getNetworkConfig(chainId: string) {
    const networkConfigs: Record<string, any> = {
      "0x89": {
        chainId: "0x89",
        chainName: "Polygon",
        nativeCurrency: {
          name: "MATIC",
          symbol: "MATIC",
          decimals: 18,
        },
        rpcUrls: ["https://polygon-rpc.com"],
        blockExplorerUrls: ["https://polygonscan.com"],
      },
      "0xa4b1": {
        chainId: "0xa4b1",
        chainName: "Arbitrum One",
        nativeCurrency: {
          name: "Ether",
          symbol: "ETH",
          decimals: 18,
        },
        rpcUrls: ["https://arb1.arbitrum.io/rpc"],
        blockExplorerUrls: ["https://arbiscan.io"],
      },
    };
    return networkConfigs[chainId];
  }

  public async connectWallet(walletType: EthereumWallets) {
    if (!walletType) return;

    const wallet = WALLET_ECOSYSTEM?.ethereum?.wallets[walletType];
    if (!wallet) {
      this.setState({ error: `${walletType} configuration not found` });
      return;
    }

    try {
      const provider = this.getProvider(walletType);

      if (!provider) {
        throw new Error(`${wallet.name} is not installed`);
      }

      this.setState({ isConnecting: true, error: null });
      
      this.clearConnectionState();
      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      // Check if already connected
      const existingAccounts = await provider.request({
        method: "eth_accounts",
      });

      if (existingAccounts && existingAccounts.length > 0) {
        const chainId = await provider.request({
          method: "eth_chainId",
        });

        this.setState({
          account: existingAccounts[0],
          publicKey: existingAccounts[0],
          error: null,
          isConnecting: false,
          walletType,
          isConnected: true,
          chainId,
          network: this.getNetworkFromChainId(chainId),
          provider,
        });
        return;
      }

      // Request new connection
      const accounts = await provider.request({
        method: "eth_requestAccounts",
      });

      if (accounts && accounts.length > 0) {
        const chainId = await provider.request({
          method: "eth_chainId",
        });

        const currentAccounts = await provider.request({
          method: "eth_accounts",
        });

        if (currentAccounts && currentAccounts.length > 0) {
          const permissions = await provider.request({ method: 'wallet_getPermissions' });
          const hasAccountPermission = permissions.some((permission: any) => permission.parentCapability === 'eth_accounts');
          
          if (hasAccountPermission) {
            this.setState({
              account: currentAccounts[0],
              publicKey: currentAccounts[0],
              error: null,
              isConnecting: false,
              walletType,
              isConnected: true,
              chainId,
              network: this.getNetworkFromChainId(chainId),
              provider,
            });
          } else {
            throw new Error("Permission denied");
          }
        } else {
          throw new Error("No accounts available after connection");
        }
      } else {
        throw new Error("No accounts found");
      }
    } catch (err) {
      this.setState({
        error: err instanceof Error ? err.message : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
      
      if (this.cleanupListeners) {
        this.cleanupListeners();
        this.cleanupListeners = undefined;
      }
    }
  }

  public async connectEIP6963Wallet(rdns: string): Promise<void> {
    let walletDetail = this.eip6963Providers.get(rdns);
    
    if (!walletDetail) {
      for (let attempt = 0; attempt < 3; attempt++) {
        window.dispatchEvent(new Event('eip6963:requestProvider'));
        await new Promise(resolve => setTimeout(resolve, 300 + (attempt * 200)));
        walletDetail = this.eip6963Providers.get(rdns);
        if (walletDetail) break;
      }
    }
    
    if (!walletDetail) {
      const availableWallets = Array.from(this.eip6963Providers.keys());
      this.setState({ 
        error: `Wallet "${rdns}" not found. Available: ${availableWallets.join(', ') || 'none'}` 
      });
      return;
    }

    try {
      this.setState({ isConnecting: true, error: null });

      const provider = walletDetail.provider;
      
      if (!provider) {
        throw new Error('Provider not available');
      }
      
      this.cleanupListeners = this.setupWalletListeners(provider, this.mapRdnsToWalletType(rdns) || 'metamask');

      const accounts = await provider.request({
        method: "eth_requestAccounts",
      });

      if (accounts && accounts.length > 0) {
        const chainId = await provider.request({
          method: "eth_chainId",
        });

        this.setState({
          account: accounts[0],
          publicKey: accounts[0],
          error: null,
          isConnecting: false,
          walletType: this.mapRdnsToWalletType(rdns),
          isConnected: true,
          chainId,
          network: this.getNetworkFromChainId(chainId),
          eip6963Info: walletDetail.info,
        });
      } else {
        throw new Error("No accounts found");
      }
    } catch (err) {
      this.setState({
        error: err instanceof Error ? err.message : `Failed to connect to ${walletDetail.info.name}`,
        isConnecting: false,
      });
    }
  }

  public async disconnect() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
      this.cleanupListeners = undefined;
    }
    this.clearConnectionState();
  }

  public async isMetaMaskConnected(): Promise<boolean> {
    if (typeof window === 'undefined' || !window.ethereum) {
      return false;
    }

    if (!window.ethereum.isMetaMask) {
      return false;
    }

    try {
      const accounts = await window.ethereum.request({ method: 'eth_accounts' });
      return accounts && accounts.length > 0;
    } catch (err) {
      return false;
    }
  }

  public async getCurrentMetaMaskAccount(): Promise<string | null> {
    if (typeof window === 'undefined' || !window.ethereum) {
      return null;
    }

    if (!window.ethereum.isMetaMask) {
      return null;
    }

    try {
      const accounts = await window.ethereum.request({ method: 'eth_accounts' });
      return accounts && accounts.length > 0 ? accounts[0] : null;
    } catch (err) {
      return null;
    }
  }

  public getCurrentWalletProvider(): string | null {
    if (typeof window === 'undefined' || !window.ethereum) {
      return null;
    }

    const provider = window.ethereum as any;

    if (provider.isMetaMask) {
      return 'MetaMask';
    } else if (provider.isCoinbaseWallet) {
      return 'Coinbase Wallet';
    } else if (provider.isWalletConnect) {
      return 'WalletConnect';
    } else if (provider.isPhantom) {
      return 'Phantom';
    } else {
      return 'Unknown';
    }
  }

  public async refreshConnectionState() {
    await this.checkExistingConnection();
  }

  public async validateCurrentAddress(): Promise<boolean> {
    if (!this.state.isConnected || !this.state.account) {
      return false;
    }

    if (typeof window === 'undefined' || !window.ethereum || !window.ethereum.isMetaMask) {
      return false;
    }

    try {
      const currentAccounts = await window.ethereum.request({ method: 'eth_accounts' });
      const storedAccount = this.state.account;
      
      if (currentAccounts && currentAccounts.length > 0) {
        return currentAccounts[0].toLowerCase() === storedAccount.toLowerCase();
      }
      return false;
    } catch (err) {
      return false;
    }
  }

  public getAvailableWallets(): EIP6963ProviderDetail[] {
    return Array.from(this.eip6963Providers.values());
  }

  public async getWalletCapabilities(rdns?: string): Promise<Record<string, any> | null> {
    try {
      let provider: EthereumProvider | null = null;
      
      if (rdns) {
        const walletDetail = this.eip6963Providers.get(rdns);
        provider = walletDetail?.provider || null;
      } else {
        provider = this.getProvider(this.state.walletType);
      }

      if (provider && provider.wallet_getCapabilities) {
        return await provider.wallet_getCapabilities();
      }
      return null;
    } catch (err) {
      return null;
    }
  }

  public async sendCalls(calls: any[]): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !provider.wallet_sendCalls) {
        throw new Error("Wallet does not support batch calls (EIP-5792)");
      }

      const result = await provider.wallet_sendCalls({
        version: "1.0",
        chainId: this.state.chainId,
        from: this.state.account,
        calls: calls
      });

      return result;
    } catch (err) {
      this.setState({ error: "Failed to send batch calls" });
      return null;
    }
  }

  public async getCallsStatus(bundleId: string): Promise<any | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !provider.wallet_getCallsStatus) {
        throw new Error("Wallet does not support call status (EIP-5792)");
      }

      return await provider.wallet_getCallsStatus(bundleId);
    } catch (err) {
      this.setState({ error: "Failed to get call status" });
      return null;
    }
  }

  public async showCallsStatus(bundleId: string): Promise<void> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !provider.wallet_showCallsStatus) {
        throw new Error("Wallet does not support showing call status (EIP-5792)");
      }

      await provider.wallet_showCallsStatus(bundleId);
    } catch (err) {
      this.setState({ error: "Failed to show call status" });
    }
  }

  public async signMessage(message: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const signature = await provider.request({
        method: "personal_sign",
        params: [message, this.state.account],
      });

      return signature;
    } catch (err) {
      this.setState({ error: "Failed to sign message" });
      return null;
    }
  }

  public async signTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const signature = await provider.request({
        method: "eth_signTransaction",
        params: [{ from: this.state.account, ...transaction }],
      });

      return signature;
    } catch (err) {
      this.setState({ error: "Failed to sign transaction" });
      return null;
    }
  }

  public async sendTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const txHash = await provider.request({
        method: "eth_sendTransaction",
        params: [{ from: this.state.account, ...transaction }],
      });

      return txHash;
    } catch (err) {
      this.setState({ error: "Failed to send transaction" });
      return null;
    }
  }

  public async switchNetwork(chainId: string): Promise<boolean> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return false;

      await provider.request({
        method: "wallet_switchEthereumChain",
        params: [{ chainId }],
      });

      return true;
    } catch (err: any) {
      if (err.code === 4902) {
        return this.addNetwork(chainId);
      }
      this.setState({ error: "Failed to switch network" });
      return false;
    }
  }

  public async addNetwork(chainId: string): Promise<boolean> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return false;

      const networkConfig = this.getNetworkConfig(chainId);
      if (!networkConfig) return false;

      await provider.request({
        method: "wallet_addEthereumChain",
        params: [networkConfig],
      });

      return true;
    } catch (err) {
      this.setState({ error: "Failed to add network" });
      return false;
    }
  }

  public async addEthereumChain(chainConfig: {
    chainId: string;
    chainName: string;
    nativeCurrency: {
      name: string;
      symbol: string;
      decimals: number;
    };
    rpcUrls: string[];
    blockExplorerUrls?: string[];
    iconUrls?: string[];
  }): Promise<boolean> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) {
        throw new Error("No provider available");
      }

      await provider.request({
        method: "wallet_addEthereumChain",
        params: [chainConfig],
      });

      return true;
    } catch (err) {
      this.setState({ error: "Failed to add Ethereum chain" });
      return false;
    }
  }

  public async getBalance(address?: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const balance = await provider.request({
        method: "eth_getBalance",
        params: [address || this.state.account, "latest"],
      });

      return balance;
    } catch (err) {
      this.setState({ error: "Failed to get balance" });
      return null;
    }
  }

  public getProviderInstance(): EthereumProvider | null {
    return this.state.walletType ? this.getProvider(this.state.walletType) : null;
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): EthereumWalletState {
    return this.state;
  }

  public destroy() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
    }
    if (this.eip6963CleanupListeners) {
      this.eip6963CleanupListeners();
    }
    if (this.discoveryTimeout) {
      clearTimeout(this.discoveryTimeout);
    }
    this.listeners.clear();
    this.eip6963Providers.clear();
  }
}

