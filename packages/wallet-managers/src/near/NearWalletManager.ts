import type {
  NearNetwork,
  NearProvider,
  NearWallets,
  WalletHookState,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

interface NearWalletState extends WalletHookState {
  walletType?: NearWallets | null;
  networkId?: NearNetwork;
  balance?: string | null;
}

type EventCallback = (state: NearWalletState) => void;

export class NearWalletManager {
  private state: NearWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;

  constructor() {
    this.state = {
      account: null,
      error: null,
      isConnecting: false,
      walletType: null,
      networkId: undefined,
      balance: null,
    };
    this.listeners = new Set();
  }

  private setState(newState: Partial<NearWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  private getProvider(type: NearWallets | null): NearProvider | null {
    if (!window.nearWallets) return null;

    switch (type) {
      case "sender":
        return window.nearWallets.sender || null;
      case "meteor":
        return window.nearWallets.meteor || null;
      case "here":
        return window.nearWallets.here || null;
      default:
        return null;
    }
  }

  private async updateBalance(accountId?: string) {
    if (!accountId) return;

    try {
      const provider = this.getProvider(this.state.walletType || null);
      if (!provider) return;

      const targetAccountId = accountId || this.state.account;
      if (!targetAccountId) return;

      // Get balance if available
      let balance: string | null = null;
      if (provider.getBalance) {
        balance = await provider.getBalance();
      }

      this.setState({
        balance,
      });
          } catch (err) {
        console.error("Failed to update balance:", err);
      }
  }

  private setupWalletListeners(
    provider: NearProvider,
    type: NonNullable<NearWallets>
  ) {
    if (!provider) return;

    const handleAccountChanged = async (accountId: string) => {
      if (accountId) {
        this.setState({
          account: accountId,
        });

        // Update balance
        await this.updateBalance(accountId);
      } else {
        this.setState({
          account: null,
          walletType: null,
          balance: null,
        });
      }
    };

    const handleNetworkChanged = (networkId: NearNetwork) => {
      this.setState({
        networkId,
      });
    };

    const handleDisconnect = () => {
      this.setState({
        account: null,
        walletType: null,
        networkId: undefined,
        balance: null,
      });
    };

    provider.on?.("accountChanged", handleAccountChanged);
    provider.on?.("networkChanged", handleNetworkChanged);
    provider.on?.("signedOut", handleDisconnect);

    return () => {
      provider.removeListener?.("accountChanged", handleAccountChanged);
      provider.removeListener?.("networkChanged", handleNetworkChanged);
      provider.removeListener?.("signedOut", handleDisconnect);
    };
  }

  public async connectWallet(walletType: NearWallets) {
    if (!walletType) return;
    const wallet = WALLET_ECOSYSTEM?.near?.wallets[walletType];
    if (!wallet) {
      this.setState({
        error: `${walletType} configuration not found`,
      });
      return;
    }

    this.setState({ isConnecting: true, error: null });

    try {
      const provider = this.getProvider(walletType);
      if (!provider) {
        throw new Error(`${wallet.name} is not installed`);
      }

      const { accountId } = await provider.signIn();
      const networkId = await provider.getNetworkId();
      let balance: string | null = null;

      if (provider.getBalance) {
        balance = await provider.getBalance();
      }

      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      this.setState({
        account: accountId,
        error: null,
        isConnecting: false,
        walletType,
        networkId: networkId as NearNetwork,
        balance,
      });
    } catch (err) {
      this.setState({
        error:
          err instanceof Error
            ? err.message
            : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
    }
  }

  public async disconnect() {
    if (!this.state.walletType) return;

    try {
      const provider = this.getProvider(this.state.walletType);
      if (provider) {
        await provider.signOut();
      }
      this.setState({
        account: null,
        walletType: null,
        networkId: undefined,
        balance: null,
      });
    } catch (err) {
      this.setState({
        error: "Failed to disconnect",
      });
    }
  }

  public async signMessage(message: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      // Note: NearProvider doesn't have signMessage or signData methods
      return null;
    } catch (err) {
      this.setState({ error: "Failed to sign message" });
      return null;
    }
  }

  public async signTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      if (provider.signTransaction) {
        const signedTx = await provider.signTransaction(transaction);
        return signedTx;
      }

      return null;
    } catch (err) {
      this.setState({ error: "Failed to sign transaction" });
      return null;
    }
  }

  public async sendTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      // Note: NearProvider doesn't have sendTransaction method
      return null;
    } catch (err) {
      this.setState({ error: "Failed to send transaction" });
      return null;
    }
  }

  public async getBalance(accountId?: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !provider.getBalance) return null;

      const targetAccountId = accountId || this.state.account;
      if (!targetAccountId) return null;

      return await provider.getBalance();
    } catch (err) {
      this.setState({ error: "Failed to get balance" });
      return null;
    }
  }

  public async getAccountId(): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      // Note: NearProvider doesn't have getAccountId method, use accountId property
      return provider.accountId || null;
    } catch (err) {
      this.setState({ error: "Failed to get account ID" });
      return null;
    }
  }

  public async getNetworkId(): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const networkId = await provider.getNetworkId();
      return networkId;
    } catch (err) {
      this.setState({ error: "Failed to get network ID" });
      return null;
    }
  }

  public async isSignedIn(): Promise<boolean> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return false;

      return await provider.isSignedIn();
    } catch (err) {
      return false;
    }
  }

  public async signIn(contractId?: string, title?: string): Promise<any> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) throw new Error("Provider not available");

      // Note: NearProvider.signIn doesn't accept parameters
      const result = await provider.signIn();
      return result;
    } catch (err) {
      this.setState({ error: "Failed to sign in" });
      throw err;
    }
  }

  public async signOut(): Promise<void> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return;

      await provider.signOut();
    } catch (err) {
      this.setState({ error: "Failed to sign out" });
    }
  }

  public async getAccount(): Promise<any | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      // Note: NearProvider doesn't have getAccount method
      return null;
    } catch (err) {
      this.setState({ error: "Failed to get account" });
      return null;
    }
  }

  public async getAccounts(): Promise<string[] | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      // Note: NearProvider doesn't have getAccounts method
      return null;
    } catch (err) {
      this.setState({ error: "Failed to get accounts" });
      return null;
    }
  }

  public async verifyOwner(
    receiverId?: string,
    data?: string
  ): Promise<any | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      // Note: NearProvider doesn't have verifyOwner method
      return null;
    } catch (err) {
      this.setState({ error: "Failed to verify owner" });
      return null;
    }
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): NearWalletState {
    return this.state;
  }

  public destroy() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
    }
    this.listeners.clear();
  }
}
