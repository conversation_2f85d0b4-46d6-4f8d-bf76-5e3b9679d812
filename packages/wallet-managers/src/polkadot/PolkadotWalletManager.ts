import type {
  PolkadotNetwork,
  PolkadotProvider,
  PolkadotWallets,
  WalletHookState,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

interface PolkadotWalletState extends WalletHookState {
  walletType?: PolkadotWallets | null;
  network?: PolkadotNetwork;
  chainId?: string;
  balance?: {
    free: string;
    reserved: string;
    miscFrozen: string;
    feeFrozen: string;
  } | null;
  runtimeVersion?: {
    specVersion: number;
    transactionVersion: number;
  } | null;
}

type EventCallback = (state: PolkadotWalletState) => void;

export class PolkadotWalletManager {
  private state: PolkadotWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;

  constructor() {
    this.state = {
      account: null,
      error: null,
      isConnecting: false,
      walletType: null,
      network: undefined,
      chainId: undefined,
      balance: null,
      runtimeVersion: null,
    };
    this.listeners = new Set();
  }

  private setState(newState: Partial<PolkadotWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  private getProvider(type: PolkadotWallets | null): PolkadotProvider | null {
    if (typeof window === "undefined") return null;

    switch (type) {
      case "polkadotjs":
        return window.injectedWeb3?.["polkadot-js"] || null;
      case "subwallet":
        return window.injectedWeb3?.["subwallet-js"] || null;
      case "nova":
        return window.injectedWeb3?.["nova"] || null;
      default:
        return null;
    }
  }

  private setupWalletListeners(
    provider: PolkadotProvider,
    type: NonNullable<PolkadotWallets>
  ) {
    if (!provider) return;

    const handleAccountsChanged = async (accounts: string[]) => {
      if (accounts.length > 0) {
        this.setState({
          account: accounts[0],
        });
        
        // Update balance for new account
        if (provider.getBalance) {
          try {
            const balanceData = await provider.getBalance();
            this.setState({
              balance: {
                free: balanceData.free,
                reserved: balanceData.reserved,
                miscFrozen: balanceData.miscFrozen,
                feeFrozen: balanceData.feeFrozen,
              },
            });
          } catch (err) {
            console.error("Failed to fetch balance:", err);
          }
        }
      } else {
        this.setState({
          account: null,
          walletType: null,
          balance: null,
        });
      }
    };

    const handleChainChanged = async (chainId: string) => {
      this.setState({ chainId });
      
      // Update network info
      try {
        const network = await provider.getNetwork();
        this.setState({ network: network as PolkadotNetwork });
      } catch (err) {
        console.error("Failed to fetch network:", err);
      }

      // Update runtime version
      if (provider.getRuntimeVersion) {
        try {
          const version = await provider.getRuntimeVersion();
          this.setState({ runtimeVersion: version });
        } catch (err) {
          console.error("Failed to fetch runtime version:", err);
        }
      }
    };

    const handleDisconnect = () => {
      this.setState({
        account: null,
        walletType: null,
        network: undefined,
        chainId: undefined,
        balance: null,
        runtimeVersion: null,
      });
    };

    provider.on?.("accountsChanged", handleAccountsChanged);
    provider.on?.("chainChanged", handleChainChanged);
    provider.on?.("disconnect", handleDisconnect);

    return () => {
      provider.removeListener?.("accountsChanged", handleAccountsChanged);
      provider.removeListener?.("chainChanged", handleChainChanged);
      provider.removeListener?.("disconnect", handleDisconnect);
    };
  }

  public async connectWallet(walletType: PolkadotWallets) {
    if (!walletType) return;
    const wallet = WALLET_ECOSYSTEM?.polkadot?.wallets[walletType];
    if (!wallet) {
      this.setState({
        error: `${walletType} configuration not found`,
      });
      return;
    }

    this.setState({ isConnecting: true });

    try {
      const provider = this.getProvider(walletType);
      if (!provider) {
        throw new Error(`${wallet.name} is not installed`);
      }

      const accounts = await provider.enable("Your dApp Name");
      if (accounts.length === 0) {
        throw new Error("No accounts found");
      }

      // Get network, chain, balance, and runtime info
      const [network, balance, runtimeVersion] = await Promise.all([
        provider.getNetwork(),
        provider.getBalance?.() || null,
        provider.getRuntimeVersion?.() || null,
      ]);

      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      this.setState({
        account: accounts[0],
        error: null,
        isConnecting: false,
        walletType,
        network: network as PolkadotNetwork,
        // chainId: provider.id,
        balance: balance && {
          free: balance.free,
          reserved: balance.reserved,
          miscFrozen: balance.miscFrozen,
          feeFrozen: balance.feeFrozen,
        },
        runtimeVersion,
      });
    } catch (err) {
      this.setState({
        error: err instanceof Error ? err.message : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
    }
  }

  public async disconnect() {
    if (!this.state.walletType) return;

    try {
      const provider = this.getProvider(this.state.walletType);
      if (provider?.disconnect) {
        await provider.disconnect();
      }
      this.setState({
        account: null,
        walletType: null,
        network: undefined,
        chainId: undefined,
        balance: null,
        runtimeVersion: null,
      });
    } catch (err) {
      this.setState({
        error: "Failed to disconnect",
      });
    }
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): PolkadotWalletState {
    return this.state;
  }

  public destroy() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
    }
    this.listeners.clear();
  }
} 