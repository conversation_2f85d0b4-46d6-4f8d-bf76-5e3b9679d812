import type {
  FivireNetwork,
  FivireProvider,
  FivireWallets,
  WalletHookState,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

interface FivireWalletState extends WalletHookState {
  walletType?: FivireWallets | null;
  chainId?: string;
  network?: FivireNetwork;
  balance?: {
    native: string;
    staked?: string;
    sustainabilityScore?: number;
    tokens?: Array<{
      symbol: string;
      balance: string;
      address: string;
    }>;
  } | null;
}

type EventCallback = (state: FivireWalletState) => void;

export class FivireWalletManager {
  private state: FivireWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;

  constructor() {
    this.state = {
      account: null,
      error: null,
      isConnecting: false,
      walletType: null,
      chainId: undefined,
      network: undefined,
      balance: null,
    };
    this.listeners = new Set();
  }

  private setState(newState: Partial<FivireWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  private getProvider(type: FivireWallets | null): FivireProvider | null {
    if (typeof window === "undefined") return null;

    switch (type) {
      case "5ire":
        return window.fire || null;
      case "metamask":
        return window.ethereum?.isMetaMask
          ? (window.ethereum as FivireProvider)
          : null;
      default:
        return null;
    }
  }

  private async updateBalance(address?: string) {
    if (!address) return;

    try {
      const provider = this.getProvider(this.state.walletType || null);
      if (!provider) return;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return;

      // Get native balance
      const balance = await provider.request({
        method: "eth_getBalance",
        params: [targetAddress, "latest"],
      });

      let sustainabilityScore: number | undefined;
      if (provider.getSustainabilityScore) {
        sustainabilityScore = await provider.getSustainabilityScore();
      }

      // Get staked balance if available
      let stakedBalance: string | undefined;
      // Note: getStakedBalance method not available in FivireProvider interface

      // Get tokens if available
      let tokens: any[] = [];
      // Note: getTokens method not available in FivireProvider interface

      this.setState({
        balance: {
          native: balance,
          staked: stakedBalance,
          sustainabilityScore,
          tokens,
        },
      });
          } catch (err) {
        console.error("Failed to update balance:", err);
      }
  }

  private setupWalletListeners(
    provider: FivireProvider,
    type: NonNullable<FivireWallets>
  ) {
    if (!provider) return;

    const handleAccountsChanged = async (accounts: string[]) => {
      if (accounts.length > 0) {
        this.setState({
          account: accounts[0],
        });

        // Update balance and sustainability score
        await this.updateBalance(accounts[0]);
      } else {
        this.setState({
          account: null,
          walletType: null,
          balance: null,
        });
      }
    };

    const handleChainChanged = (chainId: string) => {
      this.setState({
        chainId,
        network:
          chainId === "0x3e3"
            ? "mainnet"
            : chainId === "0x3e4"
              ? "testnet"
              : undefined,
      });
    };

    const handleDisconnect = () => {
      this.setState({
        account: null,
        walletType: null,
        chainId: undefined,
        network: undefined,
        balance: null,
      });
    };

    provider.on?.("accountsChanged", handleAccountsChanged);
    provider.on?.("chainChanged", handleChainChanged);
    provider.on?.("disconnect", handleDisconnect);

    return () => {
      provider.removeListener?.("accountsChanged", handleAccountsChanged);
      provider.removeListener?.("chainChanged", handleChainChanged);
      provider.removeListener?.("disconnect", handleDisconnect);
    };
  }

  public async connectWallet(walletType: FivireWallets) {
    if (!walletType) return;
    const wallet = WALLET_ECOSYSTEM?.fivire?.wallets[walletType];
    if (!wallet) {
      this.setState({
        error: `${walletType} configuration not found`,
      });
      return;
    }

    this.setState({ isConnecting: true, error: null });

    try {
      let provider: FivireProvider;
      let accounts: string[];

      switch (walletType) {
        case "5ire":
          provider = this.getProvider("5ire")!;
          if (!provider) {
            throw new Error("5ire wallet is not installed");
          }
          accounts = await provider.enable();
          break;

        case "metamask":
          provider = this.getProvider("metamask")!;
          if (!provider) {
            throw new Error("MetaMask is not configured for 5ire");
          }
          accounts = await provider.request({ method: "eth_requestAccounts" });
          break;

        default:
          throw new Error("Invalid wallet type");
      }

      const chainId = await provider.request({ method: "eth_chainId" });
      let sustainabilityScore: number | undefined;

      if (provider.getSustainabilityScore) {
        sustainabilityScore = await provider.getSustainabilityScore();
      }

      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      // Update initial balance
      await this.updateBalance(accounts[0]);

      this.setState({
        account: accounts[0],
        error: null,
        isConnecting: false,
        walletType,
        chainId,
        network:
          chainId === "0x3e3"
            ? "mainnet"
            : chainId === "0x3e4"
              ? "testnet"
              : undefined,
        balance: {
          native: "0", // Will be updated via event handler
          sustainabilityScore,
        },
      });
    } catch (err) {
      this.setState({
        error:
          err instanceof Error
            ? err.message
            : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
    }
  }

  public async disconnect() {
    if (!this.state.walletType) return;

    try {
      const provider = this.getProvider(this.state.walletType);
      if (provider?.disconnect) {
        await provider.disconnect();
      }
      this.setState({
        account: null,
        walletType: null,
        chainId: undefined,
        network: undefined,
        balance: null,
      });
    } catch (err) {
      this.setState({
        error: "Failed to disconnect",
      });
    }
  }

  public async signMessage(message: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const signature = await provider.request({
        method: "personal_sign",
        params: [message, this.state.account],
      });

      return signature;
    } catch (err) {
      this.setState({ error: "Failed to sign message" });
      return null;
    }
  }

  public async signTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const signature = await provider.request({
        method: "eth_signTransaction",
        params: [{ from: this.state.account, ...transaction }],
      });

      return signature;
    } catch (err) {
      this.setState({ error: "Failed to sign transaction" });
      return null;
    }
  }

  public async sendTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const txHash = await provider.request({
        method: "eth_sendTransaction",
        params: [{ from: this.state.account, ...transaction }],
      });

      return txHash;
    } catch (err) {
      this.setState({ error: "Failed to send transaction" });
      return null;
    }
  }

  public async getBalance(address?: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return null;

      const balance = await provider.request({
        method: "eth_getBalance",
        params: [targetAddress, "latest"],
      });

      return balance;
    } catch (err) {
      this.setState({ error: "Failed to get balance" });
      return null;
    }
  }

  public async getSustainabilityScore(): Promise<number | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !provider.getSustainabilityScore) return null;

      return await provider.getSustainabilityScore();
    } catch (err) {
      this.setState({ error: "Failed to get sustainability score" });
      return null;
    }
  }

  public async getStakedBalance(address?: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return null;

      // Note: getStakedBalance method not available in FivireProvider interface
      return null;
    } catch (err) {
      this.setState({ error: "Failed to get staked balance" });
      return null;
    }
  }

  public async switchNetwork(chainId: string): Promise<boolean> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return false;

      await provider.request({
        method: "wallet_switchEthereumChain",
        params: [{ chainId }],
      });

      return true;
    } catch (err: any) {
      if (err.code === 4902) {
        return this.addNetwork(chainId);
      }
      this.setState({ error: "Failed to switch network" });
      return false;
    }
  }

  public async addNetwork(chainId: string): Promise<boolean> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return false;

      const networkConfig = this.getNetworkConfig(chainId);
      if (!networkConfig) return false;

      await provider.request({
        method: "wallet_addEthereumChain",
        params: [networkConfig],
      });

      return true;
    } catch (err) {
      this.setState({ error: "Failed to add network" });
      return false;
    }
  }

  private getNetworkConfig(chainId: string) {
    const networkConfigs: Record<string, any> = {
      "0x3e3": {
        chainId: "0x3e3",
        chainName: "5ireChain",
        nativeCurrency: {
          name: "5IRE",
          symbol: "5IRE",
          decimals: 18,
        },
        rpcUrls: ["https://rpc.5ire.network"],
        blockExplorerUrls: ["https://5irescan.io"],
      },
      "0x3e4": {
        chainId: "0x3e4",
        chainName: "5ireChain Testnet",
        nativeCurrency: {
          name: "5IRE",
          symbol: "5IRE",
          decimals: 18,
        },
        rpcUrls: ["https://rpc.5ire.network"],
        blockExplorerUrls: ["https://5irescan.io"],
      },
    };
    return networkConfigs[chainId];
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): FivireWalletState {
    return this.state;
  }

  public destroy() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
    }
    this.listeners.clear();
  }
}
