import {
  Chain,
  SolanaNetwork,
  Wallet,
  WalletBalance,
  WalletNetwork,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "./constants/wallets";
import {
  AlgorandWalletManager,
  EthereumWalletManager,
  SolanaWalletManager,
  PolkadotWalletManager,
  FivireWalletManager,
  AstarWalletManager,
  AvalancheWalletManager,
  CardanoWalletManager,
  CosmosWalletManager,
  NearWalletManager,
} from "./index";

export const formatBalance = (
  balance: string,
  decimals: number = 18,
  maxDecimals: number = 4
): string => {
  const number = Number(balance) / Math.pow(10, decimals);
  return number.toFixed(maxDecimals);
};

export const getExplorerUrl = (
  network: WalletNetwork,
  type: "address" | "transaction",
  hash: string
): string => {
  const baseUrl = network.blockExplorer;
  return `${baseUrl}/${type}/${hash}`;
};

export const shortenAddress = (
  address: string,
  length: "short" | "medium" | "long" = "short"
): string => {
  if (!address) {
    return "";
  }

  let sliceLength = 4;

  switch (length) {
    case "short":
      sliceLength = 4;
      break;
    case "medium":
      sliceLength = 6;
      break;
    case "long":
      sliceLength = 8;
      break;
  }

  return `${address.slice(0, sliceLength)}...${address.slice(-sliceLength)}`;
};

// export const isWalletInstalled = (walletType: string): boolean => {
//   switch (walletType) {
//     case "metamask":
//       return typeof window !== "undefined" && !!window.ethereum;
//     case "phantom":
//       return typeof window !== "undefined" && !!window.solana;
//     case "walletconnect":
//       return typeof window !== "undefined" && !!window.walletconnect;

//     // Add more wallet checks...
//     default:
//       return false;
//   }
// };

export const getWalletBalance = async (
  provider: any,
  address: string,
  network: WalletNetwork
): Promise<WalletBalance> => {
  try {
    // Implementation will vary by chain
    const balance = await provider.getBalance(address);
    return {
      total: formatBalance(balance.toString(), network.currencyDecimals),
      available: formatBalance(balance.toString(), network.currencyDecimals),
      tokens: [],
    };
  } catch (error) {
    console.error("Error fetching balance:", error);
    return {
      total: "0",
      available: "0",
      tokens: [],
    };
  }
};

export interface DetectedWallet {
  chain: Chain;
  name: string;
  type: string;
  icon?: string;
  installed: boolean;
}

export const detectInstalledWallets = (): DetectedWallet[] => {
  if (typeof window === "undefined") return [];

  const detectedWallets: DetectedWallet[] = [];

  // Check Ethereum & Compatible wallets
  if (window.ethereum) {
    if (window.ethereum.isMetaMask) {
      detectedWallets.push({
        chain: "ethereum",
        name: "MetaMask",
        type: "metamask",
        icon: WALLET_ECOSYSTEM?.ethereum?.wallets?.metamask?.icon,
        installed: true,
      });
    }
    if (window.ethereum && "isCoinbaseWallet" in window.ethereum) {
      detectedWallets.push({
        chain: "ethereum",
        name: "Coinbase Wallet",
        type: "coinbase",
        icon: WALLET_ECOSYSTEM?.ethereum?.wallets?.coinbase?.icon,
        installed: true,
      });
    }
  }

  // Check Solana wallets
  if (window.phantom?.solana) {
    detectedWallets.push({
      chain: "solana",
      name: "Phantom",
      type: "phantom",
      icon: WALLET_ECOSYSTEM?.solana?.wallets?.phantom?.icon,
      installed: true,
    });
  }
  if (window.backpack) {
    detectedWallets.push({
      chain: "solana",
      name: "Backpack",
      type: "backpack",
      icon: WALLET_ECOSYSTEM?.solana?.wallets?.backpack?.icon,
      installed: true,
    });
  }
  if (window.solflare) {
    detectedWallets.push({
      chain: "solana",
      name: "Solflare",
      type: "solflare",
      icon: WALLET_ECOSYSTEM?.solana?.wallets?.solflare?.icon,
      installed: true,
    });
  }

  // Check 5ire wallet
  if (window.fire) {
    detectedWallets.push({
      chain: "fivire",
      name: "5ire Wallet",
      type: "5ire",
      icon: WALLET_ECOSYSTEM?.fivire?.wallets?.["5ire"]?.icon,
      installed: true,
    });
  }

  // Check Astar wallets
  if (window.SubWallet) {
    detectedWallets.push({
      chain: "astar",
      name: "SubWallet",
      type: "subwallet",
      icon: WALLET_ECOSYSTEM?.astar?.wallets?.subwallet?.icon,
      installed: true,
    });
  }
  if (window.injectedWeb3?.["polkadot-js"]) {
    detectedWallets.push({
      chain: "astar",
      name: "Polkadot.js",
      type: "polkadotjs",
      icon: WALLET_ECOSYSTEM?.astar?.wallets?.polkadotjs?.icon,
      installed: true,
    });
  }

  // Check Cardano wallets
  if (window.cardano) {
    if (window.cardano.nami) {
      detectedWallets.push({
        chain: "cardano",
        name: "Nami",
        type: "nami",
        icon: WALLET_ECOSYSTEM?.cardano?.wallets?.nami?.icon,
        installed: true,
      });
    }
    if (window.cardano.eternl) {
      detectedWallets.push({
        chain: "cardano",
        name: "Eternl",
        type: "eternl",
        icon: WALLET_ECOSYSTEM?.cardano?.wallets?.eternl?.icon,
        installed: true,
      });
    }
  }

  // Check Algorand wallets
  if (window.PeraWallet) {
    detectedWallets.push({
      chain: "algorand",
      name: "Pera",
      type: "pera",
      icon: WALLET_ECOSYSTEM?.algorand?.wallets?.pera?.icon,
      installed: true,
    });
  }

  // Check Cosmos wallets
  if (window.keplr) {
    detectedWallets.push({
      chain: "cosmos",
      name: "Keplr",
      type: "keplr",
      icon: WALLET_ECOSYSTEM?.cosmos?.wallets?.keplr?.icon,
      installed: true,
    });
  }

  // Check NEAR wallets
  if (window.nearWallets) {
    detectedWallets.push({
      chain: "near",
      name: "NEAR Wallets",
      type: "sender",
      icon: WALLET_ECOSYSTEM?.near?.wallets?.sender?.icon,
      installed: true,
    });
  }

  // Add WalletConnect as it's always available
  detectedWallets.push({
    chain: "ethereum",
    name: "WalletConnect",
    type: "walletconnect",
    icon: WALLET_ECOSYSTEM?.ethereum?.wallets?.walletconnect?.icon,
    installed: true,
  });

  return detectedWallets;
};

export type GetWalletConfig<C extends Chain, T> = {
  connectWallet: (walletType: Wallet<C>) => Promise<void>;
  disconnect: () => Promise<void>;
  getState: () => T;
  subscribe: (callback: (state: T) => void) => () => void;
  destroy: () => void;
  getAccountDetails?: (network: SolanaNetwork) => Promise<any>;
};

export const getWallet = (
  chain: Chain,
  walletType?: string
): GetWalletConfig<Chain, any> | null => {
  const wallet = detectInstalledWallets().find(
    (wallet) =>
      wallet.chain === chain && (walletType ? wallet.type === walletType : true)
  );

  if (!wallet) return null;

  // Note: This function supports external wallet connections to any chain
  // Backend embedded wallet creation is restricted to Ethereum and Solana only

  switch (chain) {
    case "solana":
      return new SolanaWalletManager();
    case "ethereum":
      return new EthereumWalletManager();
    case "fivire":
      return new FivireWalletManager();
    case "astar":
      return new AstarWalletManager();
    case "avalanche":
      return new AvalancheWalletManager();
    case "algorand":
      return new AlgorandWalletManager();
    case "cosmos":
      return new CosmosWalletManager();
    case "near":
      return new NearWalletManager();
    case "polkadot":
      return new PolkadotWalletManager();
    case "cardano":
      return new CardanoWalletManager();
    default:
      return null;
  }
};

export const getInstalledWalletsForChain = () => {
  return (chain: Chain, walletType?: string) =>
    detectInstalledWallets().filter(
      (wallet) =>
        wallet.chain === chain &&
        (walletType ? wallet.type === walletType : true)
    );
};
