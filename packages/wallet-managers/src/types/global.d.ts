// Global type definitions for wallet providers

declare global {
  interface Window {
    // Ethereum providers
    ethereum?: any;
    
    // Solana providers
    phantom?: {
      solana?: any;
    };
    backpack?: {
      solana?: any;
    };
    solflare?: any;
    
    // Cardano providers
    cardano?: {
      nami?: any;
      eternl?: any;
      flint?: any;
      yoroi?: any;
      typhon?: any;
    };
    
    // Bitcoin providers
    unisat?: any;
    xverse?: {
      bitcoin?: any;
    };
    hiro?: any;
    
    // Cosmos providers
    keplr?: any;
    leap?: any;
    cosmostation?: any;
    
    // Polkadot providers
    injectedWeb3?: {
      [key: string]: any;
    };
    SubWallet?: any;
    
    // NEAR providers
    nearWallets?: {
      sender?: any;
      meteor?: any;
      here?: any;
    };
    
    // Tron providers
    tronLink?: any;
    tronWeb?: any;
    tokenPocket?: {
      tron?: any;
    };
    mathwallet?: {
      tron?: any;
    };
    
    // Algorand providers
    PeraWallet?: any;
    DeflyWallet?: any;
    ExodusWallet?: any;
    MyAlgoConnect?: any;
    
    // Avalanche providers
    avalanche?: any;
    
    // Fivire providers
    fire?: any;
    
    // Astar providers
    nova?: any;
  }
}

export {};