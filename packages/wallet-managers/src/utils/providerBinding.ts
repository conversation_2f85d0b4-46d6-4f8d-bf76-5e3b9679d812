export function createBoundProvider<T extends Record<string, any>>(
  provider: T | null | undefined
): T | null {
  if (!provider) return null;

  const boundProvider = { ...provider };

  const descriptors = Object.getOwnPropertyDescriptors(provider);

  // Apply all descriptors to the new object
  Object.entries(descriptors).forEach(([key, descriptor]: [string, PropertyDescriptor]) => {
    if (typeof descriptor.value === "function") {
      (boundProvider as any)[key] = descriptor.value.bind(provider);
    } else {
      Object.defineProperty(boundProvider, key, descriptor);
    }
  });

  return boundProvider as T;
}
