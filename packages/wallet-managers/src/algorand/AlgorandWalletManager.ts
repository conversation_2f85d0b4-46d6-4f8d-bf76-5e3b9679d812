import type { AlgorandProvider, AlgorandWallets } from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

interface AlgorandWalletState {
  account: string | null;
  error: string | null;
  isConnecting: boolean;
  walletType?: "pera" | "defly" | "exodus" | "myalgo" | null;
  network?: "mainnet" | "testnet" | "betanet";
  balance?: {
    algo: string;
    assets?: Array<{
      id: number;
      amount: string;
      decimals: number;
      unitName?: string;
    }>;
  } | null;
}

type EventCallback = (state: AlgorandWalletState) => void;

export class AlgorandWalletManager {
  private state: AlgorandWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;

  constructor() {
    this.state = {
      account: null,
      error: null,
      isConnecting: false,
      walletType: null,
      network: undefined,
      balance: null,
    };
    this.listeners = new Set();
  }

  private setState(newState: Partial<AlgorandWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  private getProvider(
    type: AlgorandWalletState["walletType"]
  ): AlgorandProvider | null {
    if (typeof window === "undefined") return null;

    switch (type) {
      case "pera":
        return window.PeraWallet || null;
      case "defly":
        return window.DeflyWallet || null;
      case "exodus":
        return window.ExodusWallet || null;
      case "myalgo":
        return window.MyAlgoConnect || null;
      default:
        return null;
    }
  }

  private setupWalletListeners(
    provider: AlgorandProvider,
    type: NonNullable<AlgorandWalletState["walletType"]>
  ) {
    if (!provider) return;

    const handleAccountChange = async (accounts: Array<{ address: string }>) => {
      if (accounts.length > 0) {
        this.setState({
          account: accounts[0]?.address || null,
        });
        
        // Update balance when account changes
        await this.updateBalance(accounts[0]?.address);
      } else {
        this.setState({
          account: null,
          walletType: null,
          balance: null,
        });
      }
    };

    const handleNetworkChange = (network: string) => {
      this.setState({
        network: network as AlgorandWalletState["network"],
      });
    };

    const handleDisconnect = () => {
      this.setState({
        account: null,
        walletType: null,
        network: undefined,
        balance: null,
      });
    };

    provider.on?.("accountsChanged", handleAccountChange);
    provider.on?.("networkChanged", handleNetworkChange);
    provider.on?.("disconnect", handleDisconnect);

    return () => {
      provider.off?.("accountsChanged", handleAccountChange);
      provider.off?.("networkChanged", handleNetworkChange);
      provider.off?.("disconnect", handleDisconnect);
    };
  }

  private async updateBalance(address?: string) {
    if (!address) return;

    try {
      const provider = this.getProvider(this.state.walletType || null);
      if (!provider) return;

      // Note: AlgorandProvider doesn't have getBalance, getAssets methods
      const algoBalance = "0";
      let assets: any[] = [];

      this.setState({
        balance: {
          algo: algoBalance,
          assets: assets.map((asset: any) => ({
            id: asset.assetId,
            amount: asset.amount,
            decimals: asset.decimals || 0,
            unitName: asset.unitName,
          })),
        },
      });
          } catch (err) {
        console.error("Failed to update balance:", err);
      }
  }

  public async connectWallet(walletType: AlgorandWalletState["walletType"]) {
    if (!walletType) return;
    const wallet =
      WALLET_ECOSYSTEM?.algorand?.wallets[walletType as AlgorandWallets];
    if (!wallet) {
      this.setState({
        error: `${walletType} configuration not found`,
      });
      return;
    }

    this.setState({ isConnecting: true, error: null });

    try {
      const provider = this.getProvider(walletType);
      if (!provider) {
        throw new Error(`${wallet.name} is not installed`);
      }

      // Connect to wallet
      const { address } = await provider.connect();
      let network: AlgorandWalletState["network"] = undefined;

      if (provider.getNetwork) {
        network =
          (await provider.getNetwork()) as AlgorandWalletState["network"];
      }

      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      // Update initial balance
      await this.updateBalance(address);

      this.setState({
        account: address,
        error: null,
        isConnecting: false,
        walletType,
        network,
      });
    } catch (err) {
      this.setState({
        error:
          err instanceof Error
            ? err.message
            : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
    }
  }

  public async disconnect() {
    if (!this.state.walletType) return;

    try {
      const provider = this.getProvider(this.state.walletType);
      if (provider?.disconnect) {
        await provider.disconnect();
      }
      this.setState({
        account: null,
        walletType: null,
        network: undefined,
        balance: null,
      });
    } catch (err) {
      this.setState({
        error: "Failed to disconnect",
      });
    }
  }

  public async signTransaction(transaction: any): Promise<Uint8Array | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      // Note: AlgorandProvider doesn't have signTransaction method
      return null;
    } catch (_err) {
      this.setState({ error: "Failed to sign transaction" });
      return null;
    }
  }

  public async signTransactions(transactions: any[]): Promise<Uint8Array[] | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      // Note: AlgorandProvider doesn't have signTransactions method
      return null;
    } catch (_err) {
      this.setState({ error: "Failed to sign transactions" });
      return null;
    }
  }

  public async sendTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      // Note: AlgorandProvider doesn't have sendTransaction method
      return null;
    } catch (_err) {
      this.setState({ error: "Failed to send transaction" });
      return null;
    }
  }

  public async getBalance(address?: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return null;

      // Note: AlgorandProvider doesn't have getBalance method
      return "0";
    } catch (_err) {
      this.setState({ error: "Failed to get balance" });
      return null;
    }
  }

  public async getAssets(address?: string): Promise<any[] | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return null;

      // Note: AlgorandProvider doesn't have getAssets method
      return [];
    } catch (_err) {
      this.setState({ error: "Failed to get assets" });
      return null;
    }
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): AlgorandWalletState {
    return this.state;
  }

  public destroy() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
    }
    this.listeners.clear();
  }
}
