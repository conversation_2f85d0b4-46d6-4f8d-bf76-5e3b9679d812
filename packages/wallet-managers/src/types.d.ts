declare module "*.svg" {
  const content: string;
  export default content;
}

declare global {
  interface Window {
    // Ethereum providers
    ethereum?: any;
    
    // Solana providers
    phantom?: {
      solana?: any;
    };
    backpack?: {
      solana?: any;
    };
    solflare?: any;
    
    // Algorand providers
    PeraWallet?: any;
    DeflyWallet?: any;
    ExodusWallet?: any;
    MyAlgoConnect?: any;
    
    // Avalanche providers
    avalanche?: any;
    
    // 5ire providers
    fire?: any;
    
    // Astar/Polkadot providers
    injectedWeb3?: any;
    SubWallet?: any;
    nova?: any;
    
    // Bitcoin providers
    unisat?: any;
    xverse?: {
      bitcoin?: any;
    };
    hiro?: any;
    
    // Cardano providers
    cardano?: {
      nami?: any;
      eternl?: any;
      flint?: any;
      yoroi?: any;
      typhon?: any;
    };
    
    // Cosmos providers
    keplr?: any;
    leap?: any;
    cosmostation?: any;
    
    // NEAR providers
    nearWallets?: {
      sender?: any;
      meteor?: any;
      here?: any;
    };
    
    // Tron providers
    tronLink?: any;
    tronWeb?: any;
    tokenPocket?: {
      tron?: any;
    };
    mathwallet?: {
      tron?: any;
    };
  }
}
