import type {
  BitcoinNetwork,
  BitcoinProvider,
  BitcoinWallets,
  WalletHookState,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

interface BitcoinWalletState extends WalletHookState {
  walletType?: BitcoinWallets | null;
  network?: BitcoinNetwork;
  balance?: {
    confirmed: string;
    unconfirmed: string;
    total: string;
  } | null;
}

type EventCallback = (state: BitcoinWalletState) => void;

export class BitcoinWalletManager {
  private state: BitcoinWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;

  constructor() {
    this.state = {
      account: null,
      error: null,
      isConnecting: false,
      walletType: null,
      network: undefined,
      balance: null,
    };
    this.listeners = new Set();
  }

  private setState(newState: Partial<BitcoinWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  private getProvider(type: BitcoinWallets | null): BitcoinProvider | null {
    if (typeof window === "undefined") return null;

    switch (type) {
      case "unisat":
        return window.unisat || null;
      case "xverse":
        return window.xverse?.bitcoin || null;
      case "hiro":
        return window.hiro || null;
      default:
        return null;
    }
  }

  private setupWalletListeners(
    provider: BitcoinProvider,
    type: NonNullable<BitcoinWallets>
  ) {
    if (!provider) return;

    const handleAccountsChanged = (accounts: string[]) => {
      if (accounts.length > 0) {
        this.setState({
          account: accounts[0],
        });
        // Fetch updated balance if available
        if (provider.getBalance) {
          provider.getBalance().then((balance) => {
            this.setState({
              balance: {
                confirmed: balance.confirmed.toString(),
                unconfirmed: balance.unconfirmed.toString(),
                total: (balance.confirmed + balance.unconfirmed).toString(),
              },
            });
          });
        }
      } else {
        this.setState({
          account: null,
          walletType: null,
          balance: null,
        });
      }
    };

    const handleNetworkChanged = (network: BitcoinNetwork) => {
      this.setState({
        network,
      });
    };

    const handleDisconnect = () => {
      this.setState({
        account: null,
        walletType: null,
        network: undefined,
        balance: null,
      });
    };

    provider.on?.("accountsChanged", handleAccountsChanged);
    provider.on?.("networkChanged", handleNetworkChanged);
    provider.on?.("disconnect", handleDisconnect);

    return () => {
      provider.removeListener?.("accountsChanged", handleAccountsChanged);
      provider.removeListener?.("networkChanged", handleNetworkChanged);
      provider.removeListener?.("disconnect", handleDisconnect);
    };
  }

  public async connectWallet(walletType: BitcoinWallets) {
    if (!walletType) return;
    const wallet = WALLET_ECOSYSTEM?.bitcoin?.wallets[walletType];
    if (!wallet) {
      this.setState({
        error: `${walletType} configuration not found`,
      });
      return;
    }

    this.setState({ isConnecting: true });

    try {
      const provider = this.getProvider(walletType);
      if (!provider) {
        throw new Error(`${wallet.name} is not installed`);
      }

      const accounts = await provider.connect();
      const network = await provider.getNetwork();
      let balance = null;

      if (provider.getBalance) {
        const balanceData = await provider.getBalance();
        balance = {
          confirmed: balanceData.confirmed.toString(),
          unconfirmed: balanceData.unconfirmed.toString(),
          total: (balanceData.confirmed + balanceData.unconfirmed).toString(),
        };
      }

      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      this.setState({
        account: accounts[0],
        error: null,
        isConnecting: false,
        walletType,
        network,
        balance,
      });
    } catch (err) {
      this.setState({
        error: err instanceof Error ? err.message : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
    }
  }

  public async disconnect() {
    if (!this.state.walletType) return;

    try {
      const provider = this.getProvider(this.state.walletType);
      if (provider?.disconnect) {
        await provider.disconnect();
      }
      this.setState({
        account: null,
        walletType: null,
        network: undefined,
        balance: null,
      });
    } catch (err) {
      this.setState({
        error: "Failed to disconnect",
      });
    }
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): BitcoinWalletState {
    return this.state;
  }

  public destroy() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
    }
    this.listeners.clear();
  }
} 