import type {
  CardanoNetwork,
  CardanoProvider,
  CardanoWallets,
  WalletHookState,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

interface CardanoWalletState extends WalletHookState {
  walletType?: CardanoWallets | null;
  network?: CardanoNetwork;
  networkId?: number;
  balance?: {
    lovelace: string;
    assets?: Array<{
      policyId: string;
      assetName?: string;
      quantity?: string;
    }>;
  } | null;
}

type EventCallback = (state: CardanoWalletState) => void;

export class CardanoWalletManager {
  private state: CardanoWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;

  constructor() {
    this.state = {
      account: null,
      error: null,
      isConnecting: false,
      walletType: null,
      network: undefined,
      networkId: undefined,
      balance: null,
    };
    this.listeners = new Set();
  }

  private setState(newState: Partial<CardanoWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  private getProvider(type: CardanoWallets | null): CardanoProvider | null {
    if (typeof window === "undefined" || !window.cardano) return null;

    switch (type) {
      case "nami":
        return window.cardano.nami || null;
      case "eternl":
        return window.cardano.eternl || null;
      case "flint":
        return window.cardano.flint || null;
      case "yoroi":
        return window.cardano.yoroi || null;
      case "typhon":
        return window.cardano.typhon || null;
      default:
        return null;
    }
  }

  private getNetworkFromId(networkId: number): CardanoNetwork | undefined {
    switch (networkId) {
      case 1:
        return "mainnet";
      case 0:
        return "testnet";
      case 2:
        return "preview";
      case 3:
        return "preprod";
      default:
        return undefined;
    }
  }

  private async updateBalance(address?: string) {
    if (!address) return;

    try {
      const provider = this.getProvider(this.state.walletType || null);
      if (!provider) return;

      const api = await provider.enable();
      const balance = await api.getBalance();
      const utxos = await api.getUtxos();

      // Process UTXOs to get asset details
      const assets =
        utxos?.map((utxo: any) => ({
          policyId: utxo.policyId || utxo,
          assetName: utxo.assetName,
          quantity: utxo.quantity || "0",
        })) || [];

      this.setState({
        balance: {
          lovelace: balance,
          assets,
        },
      });
          } catch (err) {
        console.error("Failed to update balance:", err);
      }
  }

  private setupWalletListeners(
    provider: CardanoProvider,
    type: NonNullable<CardanoWallets>
  ) {
    if (!provider.experimental) return;

    const handleNetworkChange = async (networkId: number) => {
      this.setState({
        networkId,
        network: this.getNetworkFromId(networkId),
      });
    };

    const handleAccountChange = async (addresses: string[]) => {
      if (addresses.length > 0) {
        this.setState({
          account: addresses[0],
        });

        // Update balance
        await this.updateBalance(addresses[0]);
      } else {
        this.setState({
          account: null,
          walletType: null,
          balance: null,
        });
      }
    };

    const handleDisconnect = () => {
      this.setState({
        account: null,
        walletType: null,
        networkId: undefined,
        network: undefined,
        balance: null,
      });
    };

    provider.experimental.on("networkChange", handleNetworkChange);
    provider.experimental.on("accountChange", handleAccountChange);
    provider.experimental.on("disconnect", handleDisconnect);

    return () => {
      provider.experimental?.off("networkChange", handleNetworkChange);
      provider.experimental?.off("accountChange", handleAccountChange);
      provider.experimental?.off("disconnect", handleDisconnect);
    };
  }

  public async connectWallet(walletType: CardanoWallets) {
    if (!walletType) return;
    const wallet = WALLET_ECOSYSTEM?.cardano?.wallets[walletType];
    if (!wallet) {
      this.setState({
        error: `${walletType} configuration not found`,
      });
      return;
    }

    this.setState({ isConnecting: true, error: null });

    try {
      const provider = this.getProvider(walletType);
      if (!provider) {
        throw new Error(`${wallet.name} is not installed`);
      }

      const api = await provider.enable();
      const networkId = await api.getNetworkId();
      const addresses = await api.getUsedAddresses();
      const balance = await api.getBalance();
      const utxos = await api.getUtxos();

      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      // Process UTXOs to get asset details
      const assets =
        utxos?.map((utxo: any) => ({
          policyId: utxo.policyId || utxo,
          assetName: utxo.assetName,
          quantity: utxo.quantity || "0",
        })) || [];

      this.setState({
        account: addresses[0],
        error: null,
        isConnecting: false,
        walletType,
        networkId,
        network: this.getNetworkFromId(networkId),
        balance: {
          lovelace: balance,
          assets,
        },
      });
    } catch (err) {
      this.setState({
        error:
          err instanceof Error
            ? err.message
            : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
    }
  }

  public async disconnect() {
    if (!this.state.walletType) return;

    try {
      const provider = this.getProvider(this.state.walletType);
      if (provider?.disconnect) {
        await provider.disconnect();
      }
      this.setState({
        account: null,
        walletType: null,
        networkId: undefined,
        network: undefined,
        balance: null,
      });
    } catch (err) {
      this.setState({
        error: "Failed to disconnect",
      });
    }
  }

  public async signMessage(message: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const api = await provider.enable();

      // Use signData method for message signing
      const signature = await api.signData(this.state.account, message);
      return signature;
    } catch (err) {
      this.setState({ error: "Failed to sign message" });
      return null;
    }
  }

  public async signTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const api = await provider.enable();

      if (api.signTx) {
        const signedTx = await api.signTx(transaction, true);
        return signedTx;
      }

      return null;
    } catch (err) {
      this.setState({ error: "Failed to sign transaction" });
      return null;
    }
  }

  public async sendTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const api = await provider.enable();

      if (api.submitTx) {
        const txHash = await api.submitTx(transaction);
        return txHash;
      }

      return null;
    } catch (err) {
      this.setState({ error: "Failed to send transaction" });
      return null;
    }
  }

  public async getBalance(address?: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const api = await provider.enable();
      const balance = await api.getBalance();
      return balance;
    } catch (err) {
      this.setState({ error: "Failed to get balance" });
      return null;
    }
  }

  public async getUtxos(address?: string): Promise<any[] | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const api = await provider.enable();
      const utxos = await api.getUtxos();
      return utxos || null;
    } catch (err) {
      this.setState({ error: "Failed to get UTXOs" });
      return null;
    }
  }

  public async getAssets(address?: string): Promise<any[] | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const api = await provider.enable();
      const utxos = await api.getUtxos();

      // Process UTXOs to get unique assets
      const assets =
        utxos?.map((utxo: any) => ({
          policyId: utxo.policyId || utxo,
          assetName: utxo.assetName,
          quantity: utxo.quantity || "0",
        })) || [];

      return assets;
    } catch (err) {
      this.setState({ error: "Failed to get assets" });
      return null;
    }
  }

  public async getNetworkId(): Promise<number | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const api = await provider.enable();
      const networkId = await api.getNetworkId();
      return networkId;
    } catch (err) {
      this.setState({ error: "Failed to get network ID" });
      return null;
    }
  }

  public async getUsedAddresses(): Promise<string[] | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const api = await provider.enable();
      const addresses = await api.getUsedAddresses();
      return addresses;
    } catch (err) {
      this.setState({ error: "Failed to get addresses" });
      return null;
    }
  }

  public async getUnusedAddresses(): Promise<string[] | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const api = await provider.enable();
      const addresses = await api.getUnusedAddresses();
      return addresses;
    } catch (err) {
      this.setState({ error: "Failed to get unused addresses" });
      return null;
    }
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): CardanoWalletState {
    return this.state;
  }

  public destroy() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
    }
    this.listeners.clear();
  }
}
