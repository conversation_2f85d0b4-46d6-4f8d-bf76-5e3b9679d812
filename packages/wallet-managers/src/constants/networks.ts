export const NETWORK_DETAILS = {
  ethereum: {
    mainnet: {
      name: 'Ethereum Mainnet',
      chainId: '0x1',
      rpcUrl: 'https://mainnet.infura.io/v3/',
      blockExplorer: 'https://etherscan.io',
      currencySymbol: 'ETH',
      currencyDecimals: 18
    },
    goerli: {
      name: 'Goerli Testnet',
      chainId: '0x5',
      rpcUrl: 'https://goerli.infura.io/v3/',
      blockExplorer: 'https://goerli.etherscan.io',
      currencySymbol: 'ETH',
      currencyDecimals: 18
    },
    sepolia: {
      name: 'Sepolia Testnet',
      chainId: '0xaa36a7',
      rpcUrl: 'https://sepolia.infura.io/v3/',
      blockExplorer: 'https://sepolia.etherscan.io',
      currencySymbol: 'ETH',
      currencyDecimals: 18
    }
  },
  
  polygon: {
    mainnet: {
      name: 'Polygon Mainnet',
      chainId: '0x89',
      rpcUrl: 'https://polygon-rpc.com',
      blockExplorer: 'https://polygonscan.com',
      currencySymbol: 'MATIC',
      currencyDecimals: 18
    },
    mumbai: {
      name: 'Mumbai Testnet',
      chainId: '0x13881',
      rpcUrl: 'https://rpc-mumbai.maticvigil.com',
      blockExplorer: 'https://mumbai.polygonscan.com',
      currencySymbol: 'MATIC',
      currencyDecimals: 18
    }
  },

  solana: {
    mainnet: {
      name: 'Solana Mainnet',
      chainId: '101',
      rpcUrl: 'https://api.mainnet-beta.solana.com',
      blockExplorer: 'https://explorer.solana.com',
      currencySymbol: 'SOL',
      currencyDecimals: 9
    },
    devnet: {
      name: 'Solana Devnet',
      chainId: '103',
      rpcUrl: 'https://api.devnet.solana.com',
      blockExplorer: 'https://explorer.solana.com/?cluster=devnet',
      currencySymbol: 'SOL',
      currencyDecimals: 9
    }
  },

  bitcoin: {
    mainnet: {
      name: 'Bitcoin Mainnet',
      chainId: 'mainnet',
      rpcUrl: 'https://api.blockcypher.com/v1/btc/main',
      blockExplorer: 'https://blockstream.info',
      currencySymbol: 'BTC',
      currencyDecimals: 8
    },
    testnet: {
      name: 'Bitcoin Testnet',
      chainId: 'testnet',
      rpcUrl: 'https://api.blockcypher.com/v1/btc/test3',
      blockExplorer: 'https://blockstream.info/testnet',
      currencySymbol: 'tBTC',
      currencyDecimals: 8
    }
  },

  cardano: {
    mainnet: {
      name: 'Cardano Mainnet',
      chainId: 'mainnet',
      rpcUrl: 'https://cardano-mainnet.blockfrost.io/api/v0',
      blockExplorer: 'https://cardanoscan.io',
      currencySymbol: 'ADA',
      currencyDecimals: 6
    },
    testnet: {
      name: 'Cardano Testnet',
      chainId: 'testnet',
      rpcUrl: 'https://cardano-testnet.blockfrost.io/api/v0',
      blockExplorer: 'https://testnet.cardanoscan.io',
      currencySymbol: 'tADA',
      currencyDecimals: 6
    }
  },

  avalanche: {
    mainnet: {
      name: 'Avalanche C-Chain',
      chainId: '0xa86a',
      rpcUrl: 'https://api.avax.network/ext/bc/C/rpc',
      blockExplorer: 'https://snowtrace.io',
      currencySymbol: 'AVAX',
      currencyDecimals: 18
    },
    testnet: {
      name: 'Avalanche Fuji Testnet',
      chainId: '0xa869',
      rpcUrl: 'https://api.avax-test.network/ext/bc/C/rpc',
      blockExplorer: 'https://testnet.snowtrace.io',
      currencySymbol: 'AVAX',
      currencyDecimals: 18
    }
  },

  near: {
    mainnet: {
      name: 'NEAR Mainnet',
      chainId: 'mainnet',
      rpcUrl: 'https://rpc.mainnet.near.org',
      blockExplorer: 'https://explorer.near.org',
      currencySymbol: 'NEAR',
      currencyDecimals: 24
    },
    testnet: {
      name: 'NEAR Testnet',
      chainId: 'testnet',
      rpcUrl: 'https://rpc.testnet.near.org',
      blockExplorer: 'https://explorer.testnet.near.org',
      currencySymbol: 'NEAR',
      currencyDecimals: 24
    }
  },

  algorand: {
    mainnet: {
      name: 'Algorand Mainnet',
      chainId: 'mainnet-v1.0',
      rpcUrl: 'https://mainnet-api.algonode.cloud',
      blockExplorer: 'https://algoexplorer.io',
      currencySymbol: 'ALGO',
      currencyDecimals: 6
    },
    testnet: {
      name: 'Algorand Testnet',
      chainId: 'testnet-v1.0',
      rpcUrl: 'https://testnet-api.algonode.cloud',
      blockExplorer: 'https://testnet.algoexplorer.io',
      currencySymbol: 'ALGO',
      currencyDecimals: 6
    }
  },

  cosmos: {
    mainnet: {
      name: 'Cosmos Hub',
      chainId: 'cosmoshub-4',
      rpcUrl: 'https://rpc.cosmos.network',
      blockExplorer: 'https://www.mintscan.io/cosmos',
      currencySymbol: 'ATOM',
      currencyDecimals: 6
    },
    testnet: {
      name: 'Cosmos Testnet',
      chainId: 'theta-testnet-001',
      rpcUrl: 'https://rpc.testnet.cosmos.network',
      blockExplorer: 'https://testnet.mintscan.io/cosmos',
      currencySymbol: 'ATOM',
      currencyDecimals: 6
    }
  },

  polkadot: {
    mainnet: {
      name: 'Polkadot',
      chainId: '0',
      rpcUrl: 'wss://rpc.polkadot.io',
      blockExplorer: 'https://polkascan.io/polkadot',
      currencySymbol: 'DOT',
      currencyDecimals: 10
    },
    testnet: {
      name: 'Westend',
      chainId: '2',
      rpcUrl: 'wss://westend-rpc.polkadot.io',
      blockExplorer: 'https://westend.subscan.io',
      currencySymbol: 'WND',
      currencyDecimals: 12
    }
  },

  tron: {
    mainnet: {
      name: 'TRON Mainnet',
      chainId: '0x2b6653dc',
      rpcUrl: 'https://api.trongrid.io',
      blockExplorer: 'https://tronscan.org',
      currencySymbol: 'TRX',
      currencyDecimals: 6
    },
    testnet: {
      name: 'TRON Nile Testnet',
      chainId: '0x3448d2a7',
      rpcUrl: 'https://nile.trongrid.io',
      blockExplorer: 'https://nile.tronscan.org',
      currencySymbol: 'TRX',
      currencyDecimals: 6
    }
  }
} as const;

export type ChainId = keyof typeof NETWORK_DETAILS;
export type NetworkType = 'mainnet' | 'testnet'; 