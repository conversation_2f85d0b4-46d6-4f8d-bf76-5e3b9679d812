import {
  AlgorandChain,
  Astar<PERSON>hain,
  AvalancheChain,
  BitcoinChain,
  CardanoChain,
  Chain,
  ChainNetworks,
  CosmosChain,
  EthereumChain,
  FivireChain,
  NearChain,
  Network,
  PolkadotChain,
  SolanaChain,
  TronChain,
} from "@tokai/wallet-types";
import backpackIcon from "../icons/backpack.svg";
import metamaskIcon from "../icons/metamask.svg";
import phantomIcon from "../icons/phantom.svg";
import solflareIcon from "../icons/solflare.svg";
import walletConnectIcon from "../icons/walletConnect.svg";

const ethereum: EthereumChain = {
  networks: {
    mainnet: {
      name: "Ethereum Mainnet",
      chainId: "0x1",
      rpcUrl: "https://cloudflare-eth.com",
      blockExplorer: "https://etherscan.io",
      currencySymbol: "ETH",
      currencyDecimals: 18,
    },
    sepolia: {
      name: "Ethereum Sepolia",
      chainId: "0xaa36a7", // 11155111 in hexadecimal
      rpcUrl: "https://sepolia.infura.io/v3/YOUR_INFURA_PROJECT_ID",
      blockExplorer: "https://sepolia.etherscan.io",
      currencySymbol: "ETH",
      currencyDecimals: 18,
    },
    goerli: {
      name: "Ethereum Goerli",
      chainId: "0x5",
      rpcUrl: "https://goerli.infura.io/v3/YOUR_INFURA_PROJECT_ID",
      blockExplorer: "https://goerli.etherscan.io",
      currencySymbol: "ETH",
      currencyDecimals: 18,
    },
    kovan: {
      name: "Ethereum Kovan",
      chainId: "0x20",
      rpcUrl: "https://kovan.infura.io/v3/YOUR_INFURA_PROJECT_ID",
      blockExplorer: "https://kovan.etherscan.io",
      currencySymbol: "ETH",
      currencyDecimals: 18,
    },
    rinkeby: {
      name: "Ethereum Rinkeby",
      chainId: "0x4",
      rpcUrl: "https://rinkeby.infura.io/v3/YOUR_INFURA_PROJECT_ID",
      blockExplorer: "https://rinkeby.etherscan.io",
      currencySymbol: "ETH",
      currencyDecimals: 18,
    },
    ropsten: {
      name: "Ethereum Ropsten",
      chainId: "0x3",
      rpcUrl: "https://ropsten.infura.io/v3/YOUR_INFURA_PROJECT_ID",
      blockExplorer: "https://ropsten.etherscan.io",
      currencySymbol: "ETH",
      currencyDecimals: 18,
    },
    arbitrum: {
      name: "Arbitrum",
      chainId: "0xa4b1",
      rpcUrl: "https://arbitrum.api.onfinality.io/public",
      blockExplorer: "https://arbiscan.io",
      currencySymbol: "ETH",
      currencyDecimals: 18,
    },
    avalanche: {
      name: "Avalanche",
      chainId: "0xa86a",
      rpcUrl: "https://api.avax.network/ext/bc/C/rpc",
      blockExplorer: "https://snowtrace.io",
      currencySymbol: "AVAX",
      currencyDecimals: 18,
    },
    polygon: {
      name: "Polygon",
      chainId: "0x89",
      rpcUrl: "https://polygon-rpc.com",
      blockExplorer: "https://polygonscan.com",
      currencySymbol: "MATIC",
      currencyDecimals: 18,
    },
    base: {
      name: "Base",
      chainId: "0x2105",
      rpcUrl: "https://base.api.onfinality.io/public",
      blockExplorer: "https://basescan.org",
      currencySymbol: "ETH",
      currencyDecimals: 18,
    },
    bsc: {
      name: "Binance Smart Chain",
      chainId: "0x38",
      rpcUrl: "https://bsc-dataseed.binance.org",
      blockExplorer: "https://bscscan.com",
      currencySymbol: "BNB",
      currencyDecimals: 18,
    },
    fantom: {
      name: "Fantom",
      chainId: "0xfa",
      rpcUrl: "https://rpc.fantom.network",
      blockExplorer: "https://ftmscan.com",
      currencySymbol: "FTM",
      currencyDecimals: 18,
    },
    optimism: {
      name: "Optimism",
      chainId: "0xa",
      rpcUrl: "https://optimism.api.onfinality.io/public",
      blockExplorer: "https://optimistic.etherscan.io",
      currencySymbol: "ETH",
      currencyDecimals: 18,
    },
  },
  wallets: {
    metamask: {
      name: "MetaMask",
      icon: metamaskIcon,
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "ethereum",
    },
    walletconnect: {
      name: "WalletConnect",
      icon: walletConnectIcon,
      platforms: ["browser", "ios", "android"],
    },
    coinbase: {
      name: "Coinbase Wallet",
      icon: "https://www.coinbase.com/assets/press/coinbase-mark.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "ethereum",
    },
  },
};

const fivire: FivireChain = {
  networks: {
    mainnet: {
      name: "5ireChain Mainnet",
      chainId: "0x3e3", // 995 in hexadecimal
      rpcUrl: "https://rpc.5ire.network",
      blockExplorer: "https://5irescan.io",
      currencySymbol: "5IRE",
      currencyDecimals: 18,
    },
    testnet: {
      name: "5ireChain Testnet",
      chainId: "0x3e4", // 996 in hexadecimal
      rpcUrl: "https://rpc.5ire.network",
      blockExplorer: "https://5irescan.io",
      currencySymbol: "5IRE",
      currencyDecimals: 18,
    },
  },
  wallets: {
    "5ire": {
      name: "5ire Wallet",
      icon: "https://5ire.org/logo.svg",
      platforms: ["browser"],
      injectedNamespace: "fivoire",
    },
    metamask: {
      name: "MetaMask",
      icon: metamaskIcon,
      platforms: ["browser"],
      injectedNamespace: "ethereum",
    },
    walletconnect: {
      name: "WalletConnect",
      icon: walletConnectIcon,
      platforms: ["browser", "ios", "android"],
    },
  },
};

const astar: AstarChain = {
  networks: {
    mainnet: {
      name: "Astar Network",
      chainId: "0x250", // 592 in hexadecimal
      rpcUrl: "https://astar.api.onfinality.io/public",
      blockExplorer: "https://astar.subscan.io",
      currencySymbol: "ASTR",
      currencyDecimals: 18,
    },
    shiden: {
      name: "Shiden Network",
      chainId: "0x150", // 336 in hexadecimal
      rpcUrl: "https://shiden.api.onfinality.io/public",
      blockExplorer: "https://shiden.subscan.io",
      currencySymbol: "SDN",
      currencyDecimals: 18,
    },
  },
  wallets: {
    polkadotjs: {
      name: "Polkadot.js",
      icon: "https://polkadot.js.org/logo.svg",
      platforms: ["browser"],
      injectedNamespace: "injectedWeb3",
    },
    subwallet: {
      name: "SubWallet",
      icon: "https://subwallet.app/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "SubWallet",
    },
    nova: {
      name: "Nova Wallet",
      icon: "https://nova.app/logo.svg",
      platforms: ["browser"],
      injectedNamespace: "nova",
    },
  },
};

const avalanche: AvalancheChain = {
  networks: {
    mainnet: {
      name: "Avalanche C-Chain",
      chainId: "0xa86a", // 43114 in hexadecimal
      rpcUrl: "https://api.avax.network/ext/bc/C/rpc",
      blockExplorer: "https://snowtrace.io",
      currencySymbol: "AVAX",
      currencyDecimals: 18,
    },
    fuji: {
      name: "Avalanche Fuji",
      chainId: "0xa869", // 43113 in hexadecimal
      rpcUrl: "https://api.avax-test.network/ext/bc/C/rpc",
      blockExplorer: "https://testnet.snowtrace.io",
      currencySymbol: "AVAX",
      currencyDecimals: 18,
    },
    local: {
      name: "Avalanche Local",
      chainId: "0x1337", // 4919 in hexadecimal
      rpcUrl: "http://localhost:9650",
      blockExplorer: "", // Local networks typically do not have a block explorer
      currencySymbol: "AVAX",
      currencyDecimals: 18,
    },
  },
  wallets: {
    core: {
      name: "Core Wallet",
      icon: "https://support.avax.network/img/core_wallet.svg",
      platforms: ["browser"],
      injectedNamespace: "avalanche",
    },
    metamask: {
      name: "MetaMask",
      icon: metamaskIcon,
      platforms: ["browser"],
      injectedNamespace: "ethereum",
    },
  },
};

const solana: SolanaChain = {
  networks: {
    mainnetBeta: {
      name: "Solana Mainnet Beta",
      chainId: "mainnet-beta",
      rpcUrl: "https://api.mainnet-beta.solana.com",
      blockExplorer: "https://explorer.solana.com",
      currencySymbol: "SOL",
      currencyDecimals: 9,
    },
    devnet: {
      name: "Solana Devnet",
      chainId: "devnet",
      rpcUrl: "https://api.devnet.solana.com",
      blockExplorer: "https://explorer.solana.com?cluster=devnet",
      currencySymbol: "SOL",
      currencyDecimals: 9,
    },
    testnet: {
      name: "Solana Testnet",
      chainId: "testnet",
      rpcUrl: "https://api.testnet.solana.com",
      blockExplorer: "https://explorer.solana.com?cluster=testnet",
      currencySymbol: "SOL",
      currencyDecimals: 9,
    },
  },
  wallets: {
    phantom: {
      name: "Phantom",
      icon: phantomIcon,
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "solana",
    },
    backpack: {
      name: "Backpack",
      icon: backpackIcon,
      platforms: ["browser"],
      injectedNamespace: "solana",
    },
    brave: {
      name: "Brave Wallet",
      icon: "https://brave.com/logo.svg",
      platforms: ["browser"],
      injectedNamespace: "solana",
    },
    solflare: {
      name: "Solflare",
      icon: solflareIcon,
      platforms: ["browser"],
      injectedNamespace: "solana",
    },
  },
};

const cardano: CardanoChain = {
  networks: {
    mainnet: {
      name: "Cardano Mainnet",
      chainId: "mainnet",
      rpcUrl: "https://cardano-mainnet.blockfrost.io/api/v0",
      blockExplorer: "https://cardanoscan.io",
      currencySymbol: "ADA",
      currencyDecimals: 6,
    },
    preprod: {
      name: "Cardano Preprod",
      chainId: "preprod",
      rpcUrl: "https://cardano-preprod.blockfrost.io/api/v0",
      blockExplorer: "https://cardanoscan.io",
      currencySymbol: "ADA",
      currencyDecimals: 6,
    },
    preview: {
      name: "Cardano Preview",
      chainId: "preview",
      rpcUrl: "https://cardano-preview.blockfrost.io/api/v0",
      blockExplorer: "https://cardanoscan.io",
      currencySymbol: "ADA",
      currencyDecimals: 6,
    },
    testnet: {
      name: "Cardano Testnet",
      chainId: "testnet",
      rpcUrl: "https://cardano-testnet.blockfrost.io/api/v0",
      blockExplorer: "https://cardanoscan.io",
      currencySymbol: "ADA",
      currencyDecimals: 6,
    },
  },
  wallets: {
    nami: {
      name: "Nami",
      icon: "https://namiwallet.io/logo.svg",
      platforms: ["browser"],
      injectedNamespace: "cardano",
    },
    eternl: {
      name: "Eternl",
      icon: "https://eternl.io/img/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "cardano",
    },
    flint: {
      name: "Flint",
      icon: "https://flint.com/logo.svg",
      platforms: ["browser"],
      injectedNamespace: "cardano",
    },
    yoroi: {
      name: "Yoroi",
      icon: "https://yoroi.io/logo.svg",
      platforms: ["browser"],
      injectedNamespace: "cardano",
    },
    typhon: {
      name: "Typhon",
      icon: "https://typhon.com/logo.svg",
      platforms: ["browser"],
      injectedNamespace: "cardano",
    },
  },
};

const algorand: AlgorandChain = {
  networks: {
    mainnet: {
      name: "Algorand Mainnet",
      chainId: "mainnet",
      rpcUrl: "https://mainnet-algorand.api.purestake.io/ps2",
      blockExplorer: "https://algoexplorer.io",
      currencySymbol: "ALGO",
      currencyDecimals: 6,
    },
    testnet: {
      name: "Algorand Testnet",
      chainId: "testnet",
      rpcUrl: "https://testnet-algorand.api.purestake.io/ps2",
      blockExplorer: "https://testnet.algoexplorer.io",
      currencySymbol: "ALGO",
      currencyDecimals: 6,
    },
    betanet: {
      name: "Algorand Betanet",
      chainId: "betanet",
      rpcUrl: "https://betanet-algorand.api.purestake.io/ps2",
      blockExplorer: "https://betanet.algoexplorer.io",
      currencySymbol: "ALGO",
      currencyDecimals: 6,
    },
  },
  wallets: {
    defly: {
      name: "Defly",
      icon: "https://defly.io/logo.svg",
      platforms: ["browser", "mobile"],
      injectedNamespace: "algorand",
    },
    exodus: {
      name: "Exodus",
      icon: "https://exodus.com/logo.svg",
      platforms: ["browser", "mobile", "desktop"],
      injectedNamespace: "algorand",
    },
    pera: {
      name: "Pera",
      icon: "https://perawallet.app/logo.svg",
      platforms: ["browser", "mobile"],
      injectedNamespace: "algorand",
    },
  },
};

const cosmos: CosmosChain = {
  networks: {
    mainnet: {
      name: "Cosmos Mainnet",
      chainId: "cosmoshub-4",
      rpcUrl: "https://rpc.cosmos.directory/cosmoshub-4",
      blockExplorer: "https://www.mintscan.io/cosmos",
      currencySymbol: "ATOM",
      currencyDecimals: 6,
    },
    testnet: {
      name: "Cosmos Testnet",
      chainId: "cosmoshub-testnet",
      rpcUrl: "https://rpc.cosmos.directory/cosmoshub-testnet",
      blockExplorer: "https://testnet.mintscan.io/cosmos",
      currencySymbol: "ATOM",
      currencyDecimals: 6,
    },
  },
  wallets: {
    cosmostation: {
      name: "Cosmostation",
      icon: "https://cosmostation.io/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "cosmos",
    },
    keplr: {
      name: "Keplr",
      icon: "https://keplr.app/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "cosmos",
    },
    leap: {
      name: "Leap",
      icon: "https://leapwallet.io/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "cosmos",
    },
  },
};

const near: NearChain = {
  networks: {
    betanet: {
      name: "Near Betanet",
      chainId: "betanet",
      rpcUrl: "https://rpc.betanet.near.org",
      blockExplorer: "https://explorer.near.org",
      currencySymbol: "NEAR",
      currencyDecimals: 24,
    },
    mainnet: {
      name: "Near Mainnet",
      chainId: "mainnet",
      rpcUrl: "https://rpc.mainnet.near.org",
      blockExplorer: "https://explorer.near.org",
      currencySymbol: "NEAR",
      currencyDecimals: 24,
    },
    testnet: {
      name: "Near Testnet",
      chainId: "testnet",
      rpcUrl: "https://rpc.testnet.near.org",
      blockExplorer: "https://explorer.testnet.near.org",
      currencySymbol: "NEAR",
      currencyDecimals: 24,
    },
  },
  wallets: {
    here: {
      name: "Here Wallet",
      icon: "https://herewallet.app/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "near",
    },
    meteor: {
      name: "Meteor",
      icon: "https://meteorwallet.app/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "near",
    },
    sender: {
      name: "Sender",
      icon: "https://sender.com/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "near",
    },
  },
};

const polkadot: PolkadotChain = {
  networks: {
    mainnet: {
      name: "Polkadot Mainnet",
      chainId: "polkadot",
      rpcUrl: "https://rpc.polkadot.io",
      blockExplorer: "https://polkadot.subscan.io",
      currencySymbol: "DOT",
      currencyDecimals: 10,
    },
    rococo: {
      name: "Polkadot Rococo",
      chainId: "rococo",
      rpcUrl: "https://rpc.rococo.polkadot.io",
      blockExplorer: "https://rococo.subscan.io",
      currencySymbol: "ROC",
      currencyDecimals: 12,
    },
    westend: {
      name: "Polkadot Westend",
      chainId: "westend",
      rpcUrl: "https://rpc.westend.polkadot.io",
      blockExplorer: "https://westend.subscan.io",
      currencySymbol: "WND",
      currencyDecimals: 12,
    },
  },
  wallets: {
    nova: {
      name: "Nova Wallet",
      icon: "https://nova.app/logo.svg",
      platforms: ["browser"],
      injectedNamespace: "polkadot",
    },
    polkadotjs: {
      name: "Polkadot.js",
      icon: "https://polkadot.js.org/logo.svg",
      platforms: ["browser"],
      injectedNamespace: "polkadot-js",
    },
    subwallet: {
      name: "SubWallet",
      icon: "https://subwallet.app/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "SubWallet",
    },
  },
};

const tron: TronChain = {
  networks: {
    mainnet: {
      name: "Tron Mainnet",
      chainId: "1", // Commonly assigned for Tron Mainnet in multi-chain setups
      rpcUrl: "https://api.trongrid.io",
      blockExplorer: "https://tronscan.org",
      currencySymbol: "TRX",
      currencyDecimals: 6,
    },
    nile: {
      name: "Tron Nile Testnet",
      chainId: "2", // Commonly used for Tron Nile Testnet
      rpcUrl: "https://nile.trongrid.io",
      blockExplorer: "https://nile.tronscan.org",
      currencySymbol: "TRX",
      currencyDecimals: 6,
    },
    shasta: {
      name: "Tron Shasta Testnet",
      chainId: "3", // Commonly used for Tron Shasta Testnet
      rpcUrl: "https://api.shasta.trongrid.io",
      blockExplorer: "https://shasta.tronscan.org",
      currencySymbol: "TRX",
      currencyDecimals: 6,
    },
  },
  wallets: {
    tronlink: {
      name: "TronLink",
      icon: "https://tronlink.org/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "tron",
    },
    mathwallet: {
      name: "MathWallet",
      icon: "https://mathwallet.org/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "tron",
    },
    tokenpocket: {
      name: "TokenPocket",
      icon: "https://tokenpocket.com/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "tron",
    },
  },
};

const bitcoin: BitcoinChain = {
  networks: {
    mainnet: {
      name: "Bitcoin Mainnet",
      chainId: "1", // Commonly used for Bitcoin mainnet in multi-chain tools
      rpcUrl: "https://api.blockcypher.com/v1/btc/main",
      blockExplorer: "https://blockchain.info",
      currencySymbol: "BTC",
      currencyDecimals: 8,
    },
    regtest: {
      name: "Bitcoin Regtest",
      chainId: "3", // Informal chain ID used for Regtest
      rpcUrl: "https://api.blockcypher.com/v1/btc/regtest",
      blockExplorer: "https://mempool.space/regtest",
      currencySymbol: "BTC",
      currencyDecimals: 8,
    },
    testnet: {
      name: "Bitcoin Testnet",
      chainId: "2", // Commonly used for Bitcoin testnet in multi-chain tools
      rpcUrl: "https://api.blockcypher.com/v1/btc/test3",
      blockExplorer: "https://mempool.space/testnet",
      currencySymbol: "BTC",
      currencyDecimals: 8,
    },
  },
  wallets: {
    hiro: {
      name: "Hiro Wallet",
      icon: "https://hiro.so/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "bitcoin",
    },
    unisat: {
      name: "Unisat",
      icon: "https://unisat.io/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "bitcoin",
    },
    xverse: {
      name: "Xverse",
      icon: "https://xverse.app/logo.svg",
      platforms: ["browser", "ios", "android"],
      injectedNamespace: "bitcoin",
    },
  },
};

// Now define the ecosystem with proper typing
export const WALLET_ECOSYSTEM = {
  ethereum,
  fivire,
  astar,
  avalanche,
  solana,
  cardano,
  algorand,
  cosmos,
  near,
  polkadot,
  tron,
  bitcoin,
} as const;

// Helper functions
export const getNetwork = <T extends Chain>(
  chain: T,
  network: ChainNetworks[T]
): Network<T> | null => {
  const ecosystem = WALLET_ECOSYSTEM[chain];
  if (network in ecosystem.networks) {
    return ecosystem.networks[network as keyof typeof ecosystem.networks];
  }
  return null;
};
