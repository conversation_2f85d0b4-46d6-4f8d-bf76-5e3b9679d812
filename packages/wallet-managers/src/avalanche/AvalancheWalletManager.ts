import type {
  AvalancheNetwork,
  AvalancheProvider,
  AvalancheWallets,
  WalletHookState,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

interface AvalancheWalletState extends WalletHookState {
  walletType?: AvalancheWallets | null;
  chainId?: string;
  network?: AvalancheNetwork;
  balance?: {
    avax: string;
    tokens?: Array<{
      symbol: string;
      balance: string;
      address: string;
    }>;
  } | null;
}

type EventCallback = (state: AvalancheWalletState) => void;

export class AvalancheWalletManager {
  private state: AvalancheWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;

  constructor() {
    this.state = {
      account: null,
      error: null,
      isConnecting: false,
      walletType: null,
      chainId: undefined,
      network: undefined,
      balance: null,
    };
    this.listeners = new Set();
  }

  private setState(newState: Partial<AvalancheWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  private getProvider(type: AvalancheWallets | null): AvalancheProvider | null {
    if (!type) return null;

    switch (type) {
      case "core":
        return window.avalanche || null;
      case "metamask":
        return window.ethereum?.isAvalanche
          ? (window.ethereum as AvalancheProvider)
          : null;
      case "trust":
        return window.ethereum?.isTrust
          ? (window.ethereum as AvalancheProvider)
          : null;
      default:
        return null;
    }
  }

  private async updateBalance(address?: string) {
    if (!address) return;

    try {
      const provider = this.getProvider(this.state.walletType || null);
      if (!provider) return;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return;

      // Get AVAX balance
      const balance = await provider.request({
        method: "eth_getBalance",
        params: [targetAddress, "latest"],
      });

      // Get tokens if available
      let tokens: any[] = [];
      // Note: getTokens method not available in AvalancheProvider interface

      this.setState({
        balance: {
          avax: balance,
          tokens,
        },
      });
          } catch (err) {
        console.error("Failed to update balance:", err);
      }
  }

  private setupWalletListeners(
    provider: AvalancheProvider,
    type: NonNullable<AvalancheWallets>
  ) {
    if (!provider) return;

    const handleAccountsChanged = async (accounts: string[]) => {
      if (accounts.length > 0) {
        this.setState({
          account: accounts[0],
        });

        // Update balance
        await this.updateBalance(accounts[0]);
      } else {
        this.setState({
          account: null,
          walletType: null,
          balance: null,
        });
      }
    };

    const handleChainChanged = (chainId: string) => {
      this.setState({
        chainId,
        network:
          chainId === "0xa86a"
            ? "mainnet"
            : chainId === "0xa869"
              ? "fuji"
              : undefined,
      });
    };

    const handleDisconnect = () => {
      this.setState({
        account: null,
        walletType: null,
        chainId: undefined,
        network: undefined,
        balance: null,
      });
    };

    provider.on?.("accountsChanged", handleAccountsChanged);
    provider.on?.("chainChanged", handleChainChanged);
    provider.on?.("disconnect", handleDisconnect);

    return () => {
      provider.removeListener?.("accountsChanged", handleAccountsChanged);
      provider.removeListener?.("chainChanged", handleChainChanged);
      provider.removeListener?.("disconnect", handleDisconnect);
    };
  }

  public async connectWallet(walletType: AvalancheWallets) {
    if (!walletType) return;
    const wallet = WALLET_ECOSYSTEM?.avalanche?.wallets[walletType];
    if (!wallet) {
      this.setState({
        error: `${walletType} configuration not found`,
      });
      return;
    }

    this.setState({ isConnecting: true, error: null });

    try {
      let provider: AvalancheProvider;
      let accounts: string[];

      switch (walletType) {
        case "core":
          provider = this.getProvider("core")!;
          if (!provider) {
            throw new Error("Core Wallet is not installed");
          }
          accounts = await provider.enable();
          break;

        case "metamask":
          provider = this.getProvider("metamask")!;
          if (!provider) {
            throw new Error("MetaMask is not configured for Avalanche");
          }
          accounts = await provider.request({ method: "eth_requestAccounts" });
          break;

        default:
          throw new Error("Invalid wallet type");
      }

      const chainId = await provider.request({ method: "eth_chainId" });
      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      // Update initial balance
      await this.updateBalance(accounts[0]);

      this.setState({
        account: accounts[0],
        error: null,
        isConnecting: false,
        walletType,
        chainId,
        network:
          chainId === "0xa86a"
            ? "mainnet"
            : chainId === "0xa869"
              ? "fuji"
              : undefined,
        balance: {
          avax: "0", // Will be updated via event handler
        },
      });
    } catch (err) {
      this.setState({
        error:
          err instanceof Error
            ? err.message
            : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
    }
  }

  public async disconnect() {
    if (!this.state.walletType) return;

    try {
      const provider = this.getProvider(this.state.walletType);
      if (provider?.disconnect) {
        await provider.disconnect();
      }
      this.setState({
        account: null,
        walletType: null,
        chainId: undefined,
        network: undefined,
        balance: null,
      });
    } catch (err) {
      this.setState({
        error: "Failed to disconnect",
      });
    }
  }

  public async signMessage(message: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const signature = await provider.request({
        method: "personal_sign",
        params: [message, this.state.account],
      });

      return signature;
    } catch (err) {
      this.setState({ error: "Failed to sign message" });
      return null;
    }
  }

  public async signTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const signature = await provider.request({
        method: "eth_signTransaction",
        params: [{ from: this.state.account, ...transaction }],
      });

      return signature;
    } catch (err) {
      this.setState({ error: "Failed to sign transaction" });
      return null;
    }
  }

  public async sendTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account) return null;

      const txHash = await provider.request({
        method: "eth_sendTransaction",
        params: [{ from: this.state.account, ...transaction }],
      });

      return txHash;
    } catch (err) {
      this.setState({ error: "Failed to send transaction" });
      return null;
    }
  }

  public async getBalance(address?: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return null;

      const balance = await provider.request({
        method: "eth_getBalance",
        params: [targetAddress, "latest"],
      });

      return balance;
    } catch (err) {
      this.setState({ error: "Failed to get balance" });
      return null;
    }
  }

  public async getTokens(address?: string): Promise<any[] | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return null;

      // Note: getTokens method not available in AvalancheProvider interface
      return null;
    } catch (err) {
      this.setState({ error: "Failed to get tokens" });
      return null;
    }
  }

  public async switchNetwork(chainId: string): Promise<boolean> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return false;

      await provider.request({
        method: "wallet_switchEthereumChain",
        params: [{ chainId }],
      });

      return true;
    } catch (err: any) {
      if (err.code === 4902) {
        return this.addNetwork(chainId);
      }
      this.setState({ error: "Failed to switch network" });
      return false;
    }
  }

  public async addNetwork(chainId: string): Promise<boolean> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return false;

      const networkConfig = this.getNetworkConfig(chainId);
      if (!networkConfig) return false;

      await provider.request({
        method: "wallet_addEthereumChain",
        params: [networkConfig],
      });

      return true;
    } catch (err) {
      this.setState({ error: "Failed to add network" });
      return false;
    }
  }

  private getNetworkConfig(chainId: string) {
    const networkConfigs: Record<string, any> = {
      "0xa86a": {
        chainId: "0xa86a",
        chainName: "Avalanche C-Chain",
        nativeCurrency: {
          name: "AVAX",
          symbol: "AVAX",
          decimals: 18,
        },
        rpcUrls: ["https://api.avax.network/ext/bc/C/rpc"],
        blockExplorerUrls: ["https://snowtrace.io"],
      },
      "0xa869": {
        chainId: "0xa869",
        chainName: "Avalanche Fuji Testnet",
        nativeCurrency: {
          name: "AVAX",
          symbol: "AVAX",
          decimals: 18,
        },
        rpcUrls: ["https://api.avax-test.network/ext/bc/C/rpc"],
        blockExplorerUrls: ["https://testnet.snowtrace.io"],
      },
    };
    return networkConfigs[chainId];
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): AvalancheWalletState {
    return this.state;
  }

  public destroy() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
    }
    this.listeners.clear();
  }
}
