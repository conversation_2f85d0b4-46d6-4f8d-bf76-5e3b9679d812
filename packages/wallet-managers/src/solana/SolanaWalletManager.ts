import {
  SolanaNetwork,
  SolanaProvider,
  SolanaWallets,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

export interface WalletStandardWallet {
  name: string;
  icon: string;
  version: string;
  accounts: Array<{ address: string; publicKey: Uint8Array }>;
  features: string[];
}

export interface SolanaWalletState {
  account: string | null;
  publicKey: string | null;
  error: string | null;
  isConnecting: boolean;
  isConnected: boolean;
  walletType: SolanaWallets | null;
  network?: SolanaNetwork;
  balance?: {
    sol: string;
    tokens?: Array<{
      mint: string;
      amount: string;
      decimals: number;
      symbol?: string;
    }>;
  } | null;
  standardWallet?: WalletStandardWallet | null;
}

type EventCallback = (state: SolanaWalletState) => void;

export class SolanaWalletManager {
  private state: SolanaWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;
  private standardWallets: Map<string, WalletStandardWallet> = new Map();

  constructor() {
    this.state = {
      account: null,
      publicKey: null,
      error: null,
      isConnecting: false,
      walletType: null,
      network: undefined,
      balance: null,
      isConnected: false,
      standardWallet: null,
    };
    this.listeners = new Set();
    
    try {
      this.setupWalletStandardListeners();
    } catch (err) {
      // Silently handle initialization errors
    }
  }

  private setState(newState: Partial<SolanaWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach(callback => callback(this.state));
  }

  private getProvider(type: SolanaWallets): SolanaProvider | null {
    if (typeof window === "undefined") return null;

    let provider: SolanaProvider | null = null;

    try {
      if (!window || typeof window !== 'object') {
        return null;
      }
      
      switch (type) {
        case "phantom":
          const phantom = window.phantom?.solana;
          if (phantom && typeof phantom === 'object') {
            provider = {
              isPhantom: true,
              isConnected: !!phantom.publicKey,
              publicKey: phantom.publicKey,
              connect: phantom.connect?.bind(phantom),
              disconnect: phantom.disconnect?.bind(phantom),
              on: phantom.on?.bind(phantom),
              removeListener: phantom.removeListener?.bind(phantom),
              removeAllListeners: phantom.removeAllListeners?.bind(phantom),
              connection: phantom.connection,
              signTransaction: phantom.signTransaction?.bind(phantom),
              signAllTransactions: phantom.signAllTransactions?.bind(phantom),
              signMessage: phantom.signMessage?.bind(phantom),
              signIn: phantom.signIn?.bind(phantom),
              signOut: phantom.signOut?.bind(phantom),
            };
          }
          break;
        case "backpack":
          const backpack = window.backpack?.solana;
          if (backpack && typeof backpack === 'object') {
            provider = {
              isBackpack: true,
              isConnected: !!backpack.publicKey,
              publicKey: backpack.publicKey,
              connect: backpack.connect?.bind(backpack),
              disconnect: backpack.disconnect?.bind(backpack),
              on: backpack.on?.bind(backpack),
              removeListener: backpack.removeListener?.bind(backpack),
              removeAllListeners: backpack.removeAllListeners?.bind(backpack),
              connection: backpack.connection,
              signTransaction: backpack.signTransaction?.bind(backpack),
              signAllTransactions: backpack.signAllTransactions?.bind(backpack),
              signMessage: backpack.signMessage?.bind(backpack),
              signIn: backpack.signIn?.bind(backpack),
              signOut: backpack.signOut?.bind(backpack),
            };
          }
          break;
        case "solflare":
          const solflare = window.solflare;
          if (solflare && typeof solflare === 'object') {
            provider = {
              isSolflare: true,
              isConnected: !!solflare.publicKey,
              publicKey: solflare.publicKey,
              connect: solflare.connect?.bind(solflare),
              disconnect: solflare.disconnect?.bind(solflare),
              on: solflare.on?.bind(solflare),
              removeListener: solflare.removeListener?.bind(solflare),
              removeAllListeners: solflare.removeAllListeners?.bind(solflare),
              connection: solflare.connection,
              signTransaction: solflare.signTransaction?.bind(solflare),
              signAllTransactions: solflare.signAllTransactions?.bind(solflare),
              signMessage: solflare.signMessage?.bind(solflare),
              signIn: solflare.signIn?.bind(solflare),
              signOut: solflare.signOut?.bind(solflare),
            };
          }
          break;
        default:
          return null;
      }
    } catch (err) {
      return null;
    }

    return provider;
  }

  private setupWalletListeners(
    provider: SolanaProvider,
    type: NonNullable<SolanaWallets>
  ) {
    if (!provider) return;

    const handleConnect = () => {
      this.setState({
        isConnected: true,
        error: null,
      });
    };

    const handleDisconnect = () => {
      this.setState({
        isConnected: false,
        account: null,
        publicKey: null,
        walletType: null,
        network: undefined,
        balance: null,
        standardWallet: null,
      });
    };

    const handleAccountChange = (
      newPublicKey: { toString: () => string } | null
    ) => {
      if (newPublicKey) {
        const publicKeyString = newPublicKey.toString();
        this.setState({
          account: publicKeyString,
          publicKey: publicKeyString,
          isConnected: true,
        });
      } else {
        this.setState({
          account: null,
          publicKey: null,
          isConnected: false,
        });
      }
    };

    // Add event listeners
    provider.on?.("connect", handleConnect);
    provider.on?.("disconnect", handleDisconnect);
    provider.on?.("accountChanged", handleAccountChange);

    return () => {
      // Remove event listeners
      provider.removeListener?.("connect", handleConnect);
      provider.removeListener?.("disconnect", handleDisconnect);
      provider.removeListener?.("accountChanged", handleAccountChange);
    };
  }

  private setupWalletStandardListeners() {
    if (typeof window === "undefined") return;

    const handleWalletRegister = (event: Event) => {
      try {
        const customEvent = event as CustomEvent<WalletStandardWallet>;
        const wallet = customEvent.detail;
        
        if (wallet && wallet.name) {
          this.standardWallets.set(wallet.name, wallet);
          this.setState({
            standardWallet: wallet,
          });
        }
      } catch (err) {
        // Silently handle errors
      }
    };

    window.addEventListener('wallet-standard:register', handleWalletRegister as EventListener);
    
    // Request wallet registration
    window.dispatchEvent(new Event('wallet-standard:request'));
  }

  public getAvailableWallets(): WalletStandardWallet[] {
    return Array.from(this.standardWallets.values());
  }

  public getInstalledWallets(): { [key in SolanaWallets]: boolean } {
    const installed: { [key in SolanaWallets]: boolean } = {
      phantom: false,
      backpack: false,
      solflare: false,
      brave: false,
    };

    if (typeof window !== "undefined") {
      installed.phantom = !!window.phantom?.solana;
      installed.backpack = !!window.backpack?.solana;
      installed.solflare = !!window.solflare;
    }

    return installed;
  }

  public async connectWallet(walletType: SolanaWallets | string) {
    if (!walletType) return;

    try {
      this.setState({ isConnecting: true, error: null });

      // Try to connect to standard wallet first
      const standardWallet = this.standardWallets.get(walletType);
      if (standardWallet) {
        await this.connectStandardWallet(standardWallet);
        return;
      }

      // Fallback to legacy wallet connection
      const legacyType = this.mapStandardWalletToLegacy(walletType) || walletType as SolanaWallets;
      await this.connectLegacyWallet(legacyType);
    } catch (err) {
      this.setState({
        error: err instanceof Error ? err.message : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
    }
  }

  private async connectStandardWallet(wallet: WalletStandardWallet) {
    try {
      // For now, we'll use the first account from the standard wallet
      if (wallet.accounts && wallet.accounts.length > 0) {
        const account = wallet.accounts[0];
        if (account && account.address) {
          this.setState({
            account: account.address,
            publicKey: account.address,
            isConnected: true,
            isConnecting: false,
            walletType: this.mapStandardWalletToLegacy(wallet.name),
            standardWallet: wallet,
          });
        } else {
          throw new Error("Invalid account data");
        }
      } else {
        throw new Error("No accounts available in wallet");
      }
    } catch (err) {
      throw new Error(`Failed to connect standard wallet: ${err}`);
    }
  }

  private mapStandardWalletToLegacy(name: string): SolanaWallets | null {
    const mapping: Record<string, SolanaWallets> = {
      'Phantom': 'phantom',
      'Backpack': 'backpack',
      'Solflare': 'solflare',
    };
    return mapping[name] || null;
  }

  private async connectLegacyWallet(walletType: SolanaWallets) {
    const wallet = WALLET_ECOSYSTEM?.solana?.wallets[walletType];
    if (!wallet) {
      throw new Error(`${walletType} configuration not found`);
    }

    const provider = this.getProvider(walletType);
    if (!provider) {
      throw new Error(`${wallet.name} is not installed`);
    }

    try {
      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      if (!provider.connect) {
        throw new Error("Provider does not support connect method");
      }

      const response = await provider.connect();
      
      if (response && response.publicKey) {
        const publicKeyString = response.publicKey.toString();
        
        this.setState({
          account: publicKeyString,
          publicKey: publicKeyString,
          isConnected: true,
          isConnecting: false,
          walletType,
          network: "mainnetBeta",
        });
      } else {
        throw new Error("Connection failed - no public key returned");
      }
    } catch (err) {
      if (this.cleanupListeners) {
        this.cleanupListeners();
        this.cleanupListeners = undefined;
      }
      throw err;
    }
  }

  public async disconnect() {
    try {
      if (this.cleanupListeners) {
        this.cleanupListeners();
        this.cleanupListeners = undefined;
      }

      // Try to disconnect from the current provider
      if (this.state.walletType) {
        const provider = this.getProvider(this.state.walletType);
        if (provider && provider.disconnect) {
          await provider.disconnect();
        }
      }

      this.setState({
        account: null,
        publicKey: null,
        isConnected: false,
        walletType: null,
        network: undefined,
        balance: null,
        standardWallet: null,
        error: null,
        isConnecting: false,
      });
    } catch (err) {
      // Even if disconnect fails, clear the state
      this.setState({
        account: null,
        publicKey: null,
        isConnected: false,
        walletType: null,
        network: undefined,
        balance: null,
        standardWallet: null,
        error: null,
        isConnecting: false,
      });
    }
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): SolanaWalletState {
    return this.state;
  }

  public destroy() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
    }
    this.listeners.clear();
    this.standardWallets.clear();
  }

  public getProviderInstance(): SolanaProvider | null {
    return this.state.walletType ? this.getProvider(this.state.walletType) : null;
  }

  public async getAccountDetails(network: SolanaNetwork) {
    // Implementation for getting account details
    // This would typically involve RPC calls to get balance, tokens, etc.
    return null;
  }
}
