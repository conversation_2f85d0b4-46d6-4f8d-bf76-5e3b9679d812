import type {
  CosmosNetwork,
  <PERSON><PERSON>Provider,
  CosmosWallets,
  WalletHookState,
} from "@tokai/wallet-types";
import { WALLET_ECOSYSTEM } from "../constants/wallets";

interface CosmosWalletState extends WalletHookState {
  walletType?: CosmosWallets | null;
  network?: CosmosNetwork;
  chainId?: string;
  balance?: {
    amount: string;
    denom: string;
  } | null;
}

type EventCallback = (state: CosmosWalletState) => void;

export class CosmosWalletManager {
  private state: CosmosWalletState;
  private listeners: Set<EventCallback>;
  private cleanupListeners?: () => void;

  constructor() {
    this.state = {
      account: null,
      error: null,
      isConnecting: false,
      walletType: null,
      chainId: undefined,
      network: undefined,
      balance: null,
    };
    this.listeners = new Set();
  }

  private setState(newState: Partial<CosmosWalletState>) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  private getProvider(type: CosmosWallets | null): CosmosProvider | null {
    if (typeof window === "undefined") return null;

    switch (type) {
      case "keplr":
        return window.keplr || null;
      case "leap":
        return window.leap || null;
      case "cosmostation":
        return window.cosmostation || null;
      default:
        return null;
    }
  }

  private async updateBalance(address?: string) {
    if (!address) return;

    try {
      const provider = this.getProvider(this.state.walletType);
      if (!provider || !this.state.chainId) return;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return;

      // Get balance
      let balance = null;
      if (provider.getBalance) {
        balance = await provider.getBalance(this.state.chainId, targetAddress);
      }

      this.setState({
        balance,
      });
    } catch (err) {
      console.error("Failed to update balance:", err);
    }
  }

  private setupWalletListeners(
    provider: CosmosProvider,
    type: NonNullable<CosmosWallets>
  ) {
    if (!provider) return;

    const handleKeyStoreChange = async () => {
      try {
        if (this.state.chainId) {
          const { address } = await provider.getKey(this.state.chainId);
          this.setState({
            account: address,
          });

          // Update balance if available
          await this.updateBalance(address);
        }
      } catch (err) {
        this.setState({
          account: null,
          walletType: null,
          balance: null,
        });
      }
    };

    const handleNetworkChange = (chainId: string) => {
      this.setState({
        chainId,
        network: chainId.includes("cosmoshub") ? "mainnet" : "testnet",
      });
    };

    provider.on?.("keystorechange", handleKeyStoreChange);
    provider.on?.("chainChanged", handleNetworkChange);

    return () => {
      provider.removeListener?.("keystorechange", handleKeyStoreChange);
      provider.removeListener?.("chainChanged", handleNetworkChange);
    };
  }

  public async connectWallet(walletType: CosmosWallets) {
    if (!walletType) return;
    const wallet = WALLET_ECOSYSTEM?.cosmos?.wallets[walletType];
    if (!wallet) {
      this.setState({
        error: `${walletType} configuration not found`,
      });
      return;
    }

    this.setState({ isConnecting: true, error: null });

    try {
      const provider = this.getProvider(walletType);
      if (!provider) {
        throw new Error(`${wallet.name} is not installed`);
      }

      // Get chain info from ecosystem config
      const chainId = WALLET_ECOSYSTEM?.cosmos?.networks?.mainnet?.chainId;
      if (!chainId) {
        throw new Error("Chain configuration not found");
      }

      // Enable chain
      await provider.enable([chainId]);

      const { address } = await provider.getKey(chainId);
      let balance = null;

      if (provider.getBalance) {
        balance = await provider.getBalance(chainId, address);
      }

      this.cleanupListeners = this.setupWalletListeners(provider, walletType);

      this.setState({
        account: address,
        error: null,
        isConnecting: false,
        walletType,
        chainId,
        network: chainId.includes("cosmoshub") ? "mainnet" : "testnet",
        balance,
      });
    } catch (err) {
      this.setState({
        error:
          err instanceof Error
            ? err.message
            : `Failed to connect ${walletType}`,
        isConnecting: false,
      });
    }
  }

  public async disconnect() {
    if (!this.state.walletType) return;

    try {
      const provider = this.getProvider(this.state.walletType);
      if (provider?.disconnect) {
        await provider.disconnect();
      }
      this.setState({
        account: null,
        walletType: null,
        chainId: undefined,
        network: undefined,
        balance: null,
      });
    } catch (err) {
      this.setState({
        error: "Failed to disconnect",
      });
    }
  }

  public async signMessage(message: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account || !this.state.chainId) return null;

      const { address } = await provider.getKey(this.state.chainId);
      
      if (provider.signMessage) {
        return await provider.signMessage(message, address);
      }

      // Fallback to signing with address
      const signature = await provider.signData(address, message);
      return signature;
    } catch (err) {
      this.setState({ error: "Failed to sign message" });
      return null;
    }
  }

  public async signTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account || !this.state.chainId) return null;

      const { address } = await provider.getKey(this.state.chainId);
      
      if (provider.signTx) {
        const signedTx = await provider.signTx(this.state.chainId, transaction, address);
        return signedTx;
      }

      return null;
    } catch (err) {
      this.setState({ error: "Failed to sign transaction" });
      return null;
    }
  }

  public async sendTransaction(transaction: any): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.account || !this.state.chainId) return null;

      const { address } = await provider.getKey(this.state.chainId);
      
      if (provider.sendTx) {
        const txHash = await provider.sendTx(this.state.chainId, transaction, address);
        return txHash;
      }

      return null;
    } catch (err) {
      this.setState({ error: "Failed to send transaction" });
      return null;
    }
  }

  public async getBalance(address?: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !this.state.chainId) return null;

      const targetAddress = address || this.state.account;
      if (!targetAddress) return null;

      if (provider.getBalance) {
        const balance = await provider.getBalance(this.state.chainId, targetAddress);
        return balance?.amount || "0";
      }

      return "0";
    } catch (err) {
      this.setState({ error: "Failed to get balance" });
      return null;
    }
  }

  public async getKey(chainId?: string): Promise<any | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const targetChainId = chainId || this.state.chainId;
      if (!targetChainId) return null;

      const key = await provider.getKey(targetChainId);
      return key;
    } catch (err) {
      this.setState({ error: "Failed to get key" });
      return null;
    }
  }

  public async getOfflineSigner(chainId?: string): Promise<any | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const targetChainId = chainId || this.state.chainId;
      if (!targetChainId) return null;

      const offlineSigner = await provider.getOfflineSigner(targetChainId);
      return offlineSigner;
    } catch (err) {
      this.setState({ error: "Failed to get offline signer" });
      return null;
    }
  }

  public async getOfflineSignerOnlyAmino(chainId?: string): Promise<any | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const targetChainId = chainId || this.state.chainId;
      if (!targetChainId) return null;

      const offlineSigner = await provider.getOfflineSignerOnlyAmino(targetChainId);
      return offlineSigner;
    } catch (err) {
      this.setState({ error: "Failed to get offline signer" });
      return null;
    }
  }

  public async getOfflineSignerAuto(chainId?: string): Promise<any | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider) return null;

      const targetChainId = chainId || this.state.chainId;
      if (!targetChainId) return null;

      const offlineSigner = await provider.getOfflineSignerAuto(targetChainId);
      return offlineSigner;
    } catch (err) {
      this.setState({ error: "Failed to get offline signer" });
      return null;
    }
  }

  public async suggestToken(chainId: string, contractAddress: string): Promise<void> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !provider.suggestToken) return;

      await provider.suggestToken(chainId, contractAddress);
    } catch (err) {
      this.setState({ error: "Failed to suggest token" });
    }
  }

  public async getSecret20ViewingKey(chainId: string, contractAddress: string): Promise<string | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !provider.getSecret20ViewingKey) return null;

      const viewingKey = await provider.getSecret20ViewingKey(chainId, contractAddress);
      return viewingKey;
    } catch (err) {
      this.setState({ error: "Failed to get viewing key" });
      return null;
    }
  }

  public async getEnigmaUtils(chainId: string): Promise<any | null> {
    try {
      const provider = this.getProvider(this.state.walletType!);
      if (!provider || !provider.getEnigmaUtils) return null;

      const enigmaUtils = await provider.getEnigmaUtils(chainId);
      return enigmaUtils;
    } catch (err) {
      this.setState({ error: "Failed to get Enigma utils" });
      return null;
    }
  }

  public subscribe(callback: EventCallback) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  public getState(): CosmosWalletState {
    return this.state;
  }

  public destroy() {
    if (this.cleanupListeners) {
      this.cleanupListeners();
    }
    this.listeners.clear();
  }
}
