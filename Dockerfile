# Tokai - Self-hosted Wallet Infrastructure
# Multi-stage Docker build for production deployment

# Stage 1: Build stage for dependencies and compilation
FROM oven/bun:1.1.38-alpine AS builder

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++ \
    sqlite

# Copy package files
COPY package.json bun.lockb* ./
COPY packages/ ./packages/
COPY dashboard/backend/package.json ./dashboard/backend/
COPY dashboard/frontend/package.json ./dashboard/frontend/

# Install dependencies
RUN bun install --frozen-lockfile

# Copy source code
COPY . .

# Build packages
RUN cd packages/react && bun run build
RUN cd packages/wallet-connectors && bun run build
RUN cd packages/wallet-managers && bun run build

# Build backend
RUN cd dashboard/backend && bun run build

# Build frontend
RUN cd dashboard/frontend && bun run build

# Stage 2: Production stage
FROM oven/bun:1.1.38-alpine AS production

# Set environment
ENV NODE_ENV=production
ENV PORT=3001

# Install production system dependencies
RUN apk add --no-cache \
    sqlite \
    curl \
    dumb-init

# Create app user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S tokai -u 1001

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=tokai:nodejs /app/dashboard/backend/dist ./backend/
COPY --from=builder --chown=tokai:nodejs /app/dashboard/backend/package.json ./backend/
COPY --from=builder --chown=tokai:nodejs /app/dashboard/frontend/dist ./frontend/
COPY --from=builder --chown=tokai:nodejs /app/node_modules ./node_modules/

# Copy necessary runtime files
COPY --from=builder --chown=tokai:nodejs /app/packages ./packages/

# Create directories for data persistence
RUN mkdir -p /app/data /app/logs && \
    chown -R tokai:nodejs /app/data /app/logs

# Create volume for persistent data
VOLUME ["/app/data", "/app/logs"]

# Switch to non-root user
USER tokai

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose port
EXPOSE ${PORT}

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["bun", "run", "backend/index.js"]