{"name": "tokai", "private": true, "scripts": {"build": "pnpm --filter=@tokai/react build && pnpm --filter=@tokai/dashboard-frontend build", "dev": "pnpm run dev:frontend & pnpm run dev:backend", "dev:frontend": "pnpm --filter=@tokai/dashboard-frontend dev", "dev:backend": "pnpm --filter=@tokai/dashboard-backend dev", "dev:sandbox": "pnpm --filter=sandbox dev", "dev:all": "pnpm run dev:frontend & pnpm run dev:backend & pnpm run dev:sandbox", "lint": "pnpm --parallel --filter=@tokai/react lint --filter=@tokai/dashboard-frontend lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"prettier": "^3.2.5", "tailwindcss-animate": "^1.0.7", "typescript": "5.5.4"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "dependencies": {"@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.2", "@web3icons/react": "^4.0.20", "better-sqlite3": "^9.2.2", "bip39": "^3.1.0", "ethers": "^6.15.0", "ioredis": "^5.7.0"}}