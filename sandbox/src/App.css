/* Tokai Sandbox Styles */

.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2a1a2a 100%);
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.app-header {
  text-align: center;
  padding: 40px 20px;
  border-bottom: 1px solid #404040;
}

.app-header h1 {
  font-size: 48px;
  font-weight: 800;
  margin: 0 0 16px 0;
  background: linear-gradient(45deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-header p {
  font-size: 18px;
  color: #a0a0a0;
  margin: 0;
}

.app-main {
  padding: 40px 20px;
}

/* Sandbox Container */
.sandbox-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Tab Navigation */
.tab-nav {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.tab-button {
  padding: 16px 24px;
  background: #2a2a2a;
  color: #ffffff;
  border: 1px solid #404040;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  min-width: 200px;
}

.tab-button:hover {
  background: #3a3a3a;
  transform: translateY(-1px);
}

.tab-button.active {
  background: #6366f1;
  border-color: #6366f1;
}

.tab-label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.tab-desc {
  font-size: 12px;
  opacity: 0.7;
}

/* Tab Content */
.tab-content {
  min-height: 600px;
}

/* Sandbox Section */
.sandbox-section {
  background: #2a2a2a;
  border-radius: 16px;
  border: 1px solid #404040;
  padding: 40px;
}

.sandbox-section h2 {
  margin: 0 0 24px 0;
  font-size: 24px;
  color: #ffffff;
}

/* Controls */
.controls {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.control-group label {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
}

.control-group select {
  padding: 8px 12px;
  background: #1a1a1a;
  color: #ffffff;
  border: 1px solid #404040;
  border-radius: 6px;
  font-size: 14px;
}

/* Demo Area */
.demo-area {
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  margin-bottom: 24px;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.demo-area.dark {
  background: #1a1a1a;
  border: 1px solid #404040;
}

.demo-area.light {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  color: #000000;
}

.demo-button {
  padding: 12px 24px;
  background: #6366f1;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.demo-button:hover {
  background: #5855eb;
  transform: translateY(-1px);
}

/* Code Preview */
.code-preview {
  margin-top: 24px;
}

.code-preview summary {
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: #6366f1;
  padding: 12px 0;
}

.code-preview pre {
  background: #1a1a1a;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 14px;
  color: #a0a0a0;
  margin: 0;
}

.code-preview code {
  font-family: 'Fira Code', monospace;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-content {
  background: #1a1a1a;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 1px solid #404040;
}

.modal-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #ffffff;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.modal-close:hover {
  background: #404040;
}

/* Hook Demo */
.hook-demo {
  display: grid;
  gap: 24px;
}

.status-panel,
.actions-panel,
.wallets-panel {
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 12px;
  padding: 20px;
}

.status-panel h3,
.actions-panel h3,
.wallets-panel h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #ffffff;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #404040;
  font-size: 14px;
}

.status-item:last-child {
  border-bottom: none;
}

.status-item.error {
  color: #ef4444;
}

.status-item button {
  padding: 4px 8px;
  background: #ef4444;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

/* Auth Actions */
.auth-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group input {
  padding: 12px;
  background: #2a2a2a;
  color: #ffffff;
  border: 1px solid #404040;
  border-radius: 6px;
  font-size: 14px;
}

.form-group input::placeholder {
  color: #666666;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-buttons button,
.user-actions button {
  padding: 12px 16px;
  background: #6366f1;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  flex: 1;
}

.action-buttons button:hover,
.user-actions button:hover {
  background: #5855eb;
}

.action-buttons button:disabled,
.user-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Wallets */
.wallet-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 8px;
  margin-bottom: 8px;
}

.wallet-network {
  font-weight: 500;
  color: #6366f1;
}

.wallet-address {
  font-family: 'Fira Code', monospace;
  font-size: 12px;
  color: #a0a0a0;
}

.wallet-status {
  font-size: 12px;
  color: #10b981;
}

/* Responsive */
@media (max-width: 768px) {
  .app-header h1 {
    font-size: 36px;
  }
  
  .app-header p {
    font-size: 16px;
  }
  
  .tab-nav {
    flex-direction: column;
    align-items: center;
  }
  
  .tab-button {
    min-width: 280px;
  }
  
  .controls {
    flex-direction: column;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .wallet-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sandbox-section,
.modal-content {
  animation: fadeIn 0.3s ease;
}