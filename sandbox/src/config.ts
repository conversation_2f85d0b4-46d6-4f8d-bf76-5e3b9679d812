// Sandbox Configuration
export const SANDBOX_CONFIG = {
  // Backend Configuration
  BACKEND_URL: 'http://localhost:3001',
  
  // Default API Key for testing (replace with your own for production)
  DEFAULT_API_KEY: 'tokai_534e8ec2139c5e0db81157e5d6d55274223835dc079e4d281142db555de306f5',
  
  // Demo Configuration
  DEMO_MODES: {
    TRADITIONAL: 'traditional',
    COMPLETE: 'complete', 
    CUSTOM: 'custom',
    STANDALONE: 'standalone'
  } as const,
  
  // Wallet Networks
  SUPPORTED_NETWORKS: [
    'ethereum',
    'polygon', 
    'bsc',
    'avalanche',
    'arbitrum',
    'optimism',
    'solana'
  ],
  
  // UI Configuration
  THEMES: {
    LIGHT: 'light',
    DARK: 'dark'
  } as const,
  
  // Button Variants
  BUTTON_VARIANTS: {
    PRIMARY: 'primary',
    SECONDARY: 'secondary', 
    OUTLINE: 'outline'
  } as const,
  
  // Button Sizes
  BUTTON_SIZES: {
    SM: 'sm',
    MD: 'md',
    LG: 'lg'
  } as const
}

// Environment-specific configuration
export const getConfig = () => {
  const isDevelopment = import.meta.env.DEV
  
  return {
    ...SANDBOX_CONFIG,
    BACKEND_URL: isDevelopment 
      ? 'http://localhost:3001' 
      : import.meta.env.VITE_BACKEND_URL || SANDBOX_CONFIG.BACKEND_URL,
    DEFAULT_API_KEY: import.meta.env.VITE_API_KEY || SANDBOX_CONFIG.DEFAULT_API_KEY
  }
}

export default SANDBOX_CONFIG 