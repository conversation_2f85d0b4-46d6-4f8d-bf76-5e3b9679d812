import React, { useState } from 'react'
import { TokaiProvider } from '@tokai/react'
import { getConfig } from '../config'

interface LayoutProps {
  children: React.ReactNode
}

// API Key Configuration Component
const ApiKeyConfig: React.FC<{ onApiKeyChange: (apiKey: string) => void }> = ({ onApiKeyChange }) => {
  const config = getConfig()
  const [apiKey, setApiKey] = useState(config.DEFAULT_API_KEY)
  const [showApiKey, setShowApiKey] = useState(false)

  const handleApiKeyChange = (newApiKey: string) => {
    setApiKey(newApiKey)
    onApiKeyChange(newApiKey)
  }

  return (
    <div className="card mb-6">
      <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2">🔑 API Key Configuration</h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            API Key
          </label>
          <div className="flex space-x-2">
            <input
              type={showApiKey ? "text" : "password"}
              value={apiKey}
              onChange={(e) => handleApiKeyChange(e.target.value)}
              className="flex-1 input-field"
              placeholder="Enter your API key"
            />
            <button
              onClick={() => setShowApiKey(!showApiKey)}
              className="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm"
            >
              {showApiKey ? 'Hide' : 'Show'}
            </button>
          </div>
        </div>
        <div className="text-xs text-gray-500">
          <p>• Default API key is provided for testing</p>
          <p>• Change this to your own API key for production use</p>
          <p>• Backend URL: <code className="bg-gray-100 px-1 rounded">{config.BACKEND_URL}</code></p>
        </div>
      </div>
    </div>
  )
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const config = getConfig()
  const [apiKey, setApiKey] = useState(config.DEFAULT_API_KEY)

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">🎮 Tokai React Sandbox</h1>
          <p className="text-gray-600">Showcase of @tokai/react package features</p>
        </header>

        {/* API Key Configuration */}
        <ApiKeyConfig onApiKeyChange={setApiKey} />

        {/* Wrap everything with TokaiProvider */}
        <TokaiProvider backendUrl={config.BACKEND_URL} apiKey={apiKey}>
          {children}
        </TokaiProvider>
      </div>
    </div>
  )
} 