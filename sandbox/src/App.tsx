import { useState } from "react";
import {
  TokaiProvider,
  TokaiConnectButton,
  TokaiAuth,
  useTokai,
} from "@tokai/react";
import "./App.css";

// Main Sandbox App
function App() {
  return (
    <div className="app">
      <header className="app-header">
        <h1>🧪 Tokai SDK Sandbox</h1>
        <p>Test your Tokai React components locally</p>
      </header>

      <main className="app-main">
        <SandboxTabs />
      </main>
    </div>
  );
}

// Tabbed interface for testing different components
function SandboxTabs() {
  const [activeTab, setActiveTab] = useState<"connect" | "auth" | "custom">(
    "connect"
  );

  const tabs = [
    {
      id: "connect",
      label: "🔗 Connect Button",
      desc: "Test TokaiConnectButton",
    },
    {
      id: "auth",
      label: "🔐 Auth Modal",
      desc: "Test TokaiAuth component",
    },
    {
      id: "custom",
      label: "🛠️ Custom Hook",
      desc: "Test useTokai hook",
    },
  ];

  return (
    <div className="sandbox-container">
      {/* Tab Navigation */}
      <nav className="tab-nav">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`tab-button ${activeTab === tab.id ? "active" : ""}`}
          >
            <div className="tab-label">{tab.label}</div>
            <div className="tab-desc">{tab.desc}</div>
          </button>
        ))}
      </nav>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === "connect" && <ConnectButtonSandbox />}
        {activeTab === "auth" && <AuthModalSandbox />}
        {activeTab === "custom" && <CustomHookSandbox />}
      </div>
    </div>
  );
}

// Connect Button Sandbox
function ConnectButtonSandbox() {
  const [variant, setVariant] = useState<"primary" | "secondary" | "outline">(
    "primary"
  );
  const [size, setSize] = useState<"sm" | "md" | "lg">("md");
  const [theme, setTheme] = useState<"light" | "dark">("dark");

  const config = {
    apiUrl: "http://localhost:3001/api",
    apiKey: "sandbox-api-key",
    appName: "Tokai Sandbox",
    theme,
    embeddedWallets: true,
    socialLogins: [
      {
        id: "google" as const,
        enabled: true,
      },
      {
        id: "discord" as const,
        enabled: true,
      },
      {
        id: "twitter" as const,
        enabled: true,
      },
      {
        id: "github" as const,
        enabled: true,
      },
    ],
    customization: {
      primaryColor: "#6366f1",
      backgroundColor: theme === "dark" ? "#1a1a1a" : "#ffffff",
      textColor: theme === "dark" ? "#ffffff" : "#000000",
    },
  };

  return (
    <TokaiProvider config={config}>
      <div className="sandbox-section">
        <h2>🔗 Connect Button Testing</h2>

        {/* Controls */}
        <div className="controls">
          <div className="control-group">
            <label>Variant:</label>
            <select
              value={variant}
              onChange={(e) => setVariant(e.target.value as any)}
            >
              <option value="primary">Primary</option>
              <option value="secondary">Secondary</option>
              <option value="outline">Outline</option>
            </select>
          </div>

          <div className="control-group">
            <label>Size:</label>
            <select
              value={size}
              onChange={(e) => setSize(e.target.value as any)}
            >
              <option value="sm">Small</option>
              <option value="md">Medium</option>
              <option value="lg">Large</option>
            </select>
          </div>

          <div className="control-group">
            <label>Theme:</label>
            <select
              value={theme}
              onChange={(e) => setTheme(e.target.value as any)}
            >
              <option value="dark">Dark</option>
              <option value="light">Light</option>
            </select>
          </div>
        </div>

        {/* Demo Area */}
        <div className={`demo-area ${theme}`}>
          <TokaiConnectButton
            variant={variant}
            size={size}
            embeddedWallets={true}
                    onSuccess={(user: any) => console.log('Connected:', user)}
        onError={(error: any) => console.error('Error:', error)}
          />
        </div>

        {/* Code Preview */}
        <details className="code-preview">
          <summary>View Code</summary>
          <pre>
            <code>{`<TokaiConnectButton
  variant="${variant}"
  size="${size}"
  embeddedWallets={true}
            onSuccess={(user) => console.log('Connected:', user)}
  onError={(error) => console.error('Error:', error)}
/>`}</code>
          </pre>
        </details>
      </div>
    </TokaiProvider>
  );
}

// Auth Modal Sandbox
function AuthModalSandbox() {
  const [showModal, setShowModal] = useState(false);
  const [theme, setTheme] = useState<"light" | "dark">("dark");

  const config = {
    apiUrl: "http://localhost:3001/api",
    apiKey: "sandbox-api-key",
    appName: "Tokai Sandbox",
    theme,
    embeddedWallets: true,
  };

  return (
    <TokaiProvider config={config}>
      <div className="sandbox-section">
        <h2>🔐 Auth Modal Testing</h2>

        {/* Controls */}
        <div className="controls">
          <div className="control-group">
            <label>Theme:</label>
            <select
              value={theme}
              onChange={(e) => setTheme(e.target.value as any)}
            >
              <option value="dark">Dark</option>
              <option value="light">Light</option>
            </select>
          </div>
        </div>

        {/* Demo Area */}
        <div className={`demo-area ${theme}`}>
          <button className="demo-button" onClick={() => setShowModal(true)}>
            Open Auth Modal
          </button>
        </div>

        {/* Modal */}
        {showModal && (
          <div className="modal-overlay">
            <div className="modal-content">
              <button
                className="modal-close"
                onClick={() => setShowModal(false)}
              >
                ✕
              </button>
              <TokaiAuth
                embeddedWallets={true}
                onSuccess={(user: any) => {
                  console.log("Auth success:", user);
                  setShowModal(false);
                }}
                onError={(error: any) => console.error("Auth error:", error)}
              />
            </div>
          </div>
        )}
      </div>
    </TokaiProvider>
  );
}

// Custom Hook Sandbox
function CustomHookSandbox() {
  const config = {
    apiUrl: "http://localhost:3001/api",
    apiKey: "sandbox-api-key",
    appName: "Tokai Sandbox",
    embeddedWallets: true,
  };

  return (
    <TokaiProvider config={config}>
      <div className="sandbox-section">
        <h2>🛠️ useTokai Hook Testing</h2>
        <HookDemo />
      </div>
    </TokaiProvider>
  );
}

function HookDemo() {
  const {
    user,
    wallets,
    isLoading,
    error,
    login,
    register,
    logout,
    createWallet,
    clearError,
  } = useTokai();

  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("password123");

  return (
    <div className="hook-demo">
      {/* Status Display */}
      <div className="status-panel">
        <h3>Current Status</h3>
        <div className="status-item">
          <strong>Loading:</strong> {isLoading ? "Yes" : "No"}
        </div>
        <div className="status-item">
          <strong>User:</strong>{" "}
          {user ? user.email || user.full_name || "Anonymous" : "Not logged in"}
        </div>
        <div className="status-item">
          <strong>Wallets:</strong> {wallets.length}
        </div>
        {error && (
          <div className="status-item error">
            <strong>Error:</strong> {error}
            <button onClick={clearError}>Clear</button>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="actions-panel">
        <h3>Actions</h3>

        {!user ? (
          <div className="auth-actions">
            <div className="form-group">
              <input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
              <input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <div className="action-buttons">
              <button
                onClick={() => login(email, password)}
                disabled={isLoading}
              >
                Login
              </button>
              <button
                onClick={() => register(email, password, "Test User")}
                disabled={isLoading}
              >
                Register
              </button>
            </div>
          </div>
        ) : (
          <div className="user-actions">
            <button
              onClick={() => createWallet("ethereum")}
              disabled={isLoading}
            >
              Create Ethereum Wallet
            </button>
            <button
              onClick={() => createWallet("polygon")}
              disabled={isLoading}
            >
              Create Polygon Wallet
            </button>
            <button onClick={logout} disabled={isLoading}>
              Logout
            </button>
          </div>
        )}
      </div>

      {/* Wallets Display */}
      {wallets.length > 0 && (
        <div className="wallets-panel">
          <h3>Wallets ({wallets.length})</h3>
          {wallets.map((wallet: any) => (
            <div key={wallet.id} className="wallet-item">
              <div className="wallet-network">
                {wallet.network.toUpperCase()}
              </div>
              <div className="wallet-address">
                {wallet.wallet_address.substring(0, 6)}...
                {wallet.wallet_address.substring(
                  wallet.wallet_address.length - 4
                )}
              </div>
              <div className="wallet-status">
                {wallet.is_active ? "✅ Active" : "○ Inactive"}
                {wallet.is_embedded && " 🔐 Embedded"}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default App;
