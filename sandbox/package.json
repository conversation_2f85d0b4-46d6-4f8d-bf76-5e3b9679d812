{"name": "@tokai/sandbox", "version": "1.0.0", "description": "Sandbox app for testing @tokai/react package", "type": "module", "scripts": {"dev": "vite --port 3002", "build": "tsc && vite build", "preview": "vite preview --port 3002", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives"}, "dependencies": {"@tokai/react": "workspace:*", "@tokai/wallet-connectors": "workspace:*", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.0", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "typescript": "^5.5.4", "vite": "^5.0.0"}, "keywords": ["tokai", "sandbox", "wallet", "testing"], "author": "Tokai Team", "license": "MIT"}