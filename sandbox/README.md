# 🎮 Tokai React Sandbox

A comprehensive sandbox application for testing and showcasing the `@tokai/react` package features with configurable API key management.

## 🚀 Features

### 🔑 API Key Configuration
- **Centralized Configuration**: All API keys and backend URLs are managed in `src/config.ts`
- **Dynamic API Key Input**: Real-time API key configuration with show/hide functionality
- **Environment Support**: Supports both development and production configurations
- **Secure Defaults**: Pre-configured with test API keys for immediate use

### 🎯 Demo Modes

#### 1. **Traditional Mode**
- Classic wallet connection experience similar to popular dApps
- Multiple wallet options (MetaMask, Phantom, Coinbase, Backpack)
- Social login options (Google, Twitter)
- Integrated Tokai components for enhanced functionality

#### 2. **Complete App Mode**
- Full TokaiApp wrapper demonstration
- Complete authentication and wallet management
- Dark theme showcase
- All-in-one solution for quick integration

#### 3. **Custom Integration Mode**
- Custom components using `useTokai` hook
- Individual component showcase
- Flexible integration patterns
- Custom wallet connector with personalized content

#### 4. **Standalone Mode**
- Individual button component demonstrations
- Multiple variants and sizes
- Theme switching examples
- Minimal integration requirements

## 🛠️ Setup

### Prerequisites
- Node.js 18+ 
- pnpm (recommended) or npm
- Backend server running on port 3001

### Installation
```bash
# Install dependencies
pnpm install

# Start the sandbox
pnpm dev
```

### Backend Setup
```bash
# From the project root
cd dashboard/backend
npm run dev
```

## ⚙️ Configuration

### API Key Management
The sandbox includes a dedicated API key configuration section at the top of the page:

1. **Default API Key**: Pre-configured for testing
2. **Custom API Key**: Input field to use your own API key
3. **Show/Hide Toggle**: Secure display of API keys
4. **Real-time Updates**: Changes apply immediately to all components

### Environment Variables
Create a `.env` file in the sandbox directory for production settings:

```env
VITE_BACKEND_URL=https://your-backend-url.com
VITE_API_KEY=your-production-api-key
```

### Configuration File
All settings are centralized in `src/config.ts`:

```typescript
export const SANDBOX_CONFIG = {
  BACKEND_URL: 'http://localhost:3001',
  DEFAULT_API_KEY: 'your-default-api-key',
  // ... other configurations
}
```

## 🎨 Customization

### Adding New Demo Modes
1. Add the mode to `SANDBOX_CONFIG.DEMO_MODES`
2. Create a new component following the existing pattern
3. Add the component to the main App render logic

### Styling
- Uses Tailwind CSS for styling
- Custom CSS classes defined in `src/index.css`
- Responsive design with mobile support

### Provider Architecture
The `TokaiProvider` is centralized in the `Layout` component, which:
- Manages API key configuration globally
- Wraps all demo components with the provider
- Handles authentication state consistently
- Provides backend URL and API key to all components

### Component Integration
Each demo mode demonstrates different integration patterns:

```typescript
// Components are automatically wrapped by Layout
<Layout>
  <YourComponent />
</Layout>

// Using the Hook (no provider needed in individual components)
const { user, wallets, createWallet } = useTokai()

// Standalone Components
<TokaiConnectButton variant="primary" size="lg" theme="dark" />
```

## 🔧 Development

### Project Structure
```
sandbox/
├── src/
│   ├── components/
│   │   └── Layout.tsx   # Main layout with TokaiProvider
│   ├── App.tsx          # Main application component
│   ├── config.ts        # Configuration management
│   ├── main.tsx         # Application entry point
│   └── index.css        # Global styles
├── public/
│   └── icons/           # Wallet icons
├── package.json         # Dependencies and scripts
└── README.md           # This file
```

### Available Scripts
```bash
pnpm dev          # Start development server (port 3002)
pnpm build        # Build for production
pnpm preview      # Preview production build
pnpm lint         # Run ESLint
```

### Port Configuration
- **Sandbox**: Port 3002 (configurable in package.json)
- **Backend**: Port 3001 (configurable in config.ts)
- **Frontend**: Port 3000 (dashboard)

## 🧪 Testing

### Manual Testing
1. **API Key Configuration**: Test different API keys
2. **Demo Modes**: Switch between different integration patterns
3. **Wallet Creation**: Test wallet creation functionality
4. **Authentication**: Test login/signup flows
5. **Responsive Design**: Test on different screen sizes

### Integration Testing
- All components use the same API key configuration
- Real-time updates when API key changes
- Consistent backend URL across all demos
- Error handling and loading states

## 🚀 Production

### Building for Production
```bash
pnpm build
pnpm preview
```

### Environment Configuration
1. Set production backend URL in environment variables
2. Configure production API keys
3. Update CORS settings on backend
4. Deploy to your hosting platform

## 📝 Notes

- The sandbox is designed for development and testing
- API keys are stored in browser memory (not persisted)
- All components are functional and interactive
- Backend must be running for full functionality
- Supports both light and dark themes

## 🤝 Contributing

1. Follow the existing component patterns
2. Add new demo modes as separate components
3. Update configuration file for new settings
4. Test all integration patterns
5. Update documentation for new features

---

**Happy Testing! 🎉** 